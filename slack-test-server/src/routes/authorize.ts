import assert from "node:assert";
import http from "node:http";

import State, { Installation } from "@organice/slack-testing-library/src/State";

import StateRepository from "../StateRepository";
import streamToPromise from "../utils/streamToPromise";

export default async (
  req: http.IncomingMessage,
  res: http.ServerResponse,
  globalState: State,
  basePath: string,
  repository: StateRepository
): Promise<void> => {
  const url = new URL(req.url ?? "/", "http://localhost:5555/");

  if (req.method === "POST") {
    const body = await streamToPromise(req);
    const params = new URLSearchParams(body);

    const scopes = params.get("scope")?.split(",");
    const userScopes = params.get("user_scope")?.split(",");
    const state = params.get("state");
    const clientId = params.get("client_id");
    const redirectUri = params.get("redirect_uri");
    const teamId = params.get("team");
    const userId = params.get("member");

    assert(teamId);
    assert(userId);
    assert(scopes);
    assert(userScopes);
    assert(state);
    assert(clientId);
    assert(redirectUri);

    const code = `${Math.random()}`;
    const acceptUrl = new URL(redirectUri);

    acceptUrl.searchParams.set("code", code);
    acceptUrl.searchParams.set("state", state);

    const installation: Installation = {
      appId: "1",
      bot: {
        id: "U11111",
        scopes,
        token: `xoxb-${Math.random()}`,
        userId: "U11111",
      },
      team: {
        id: teamId,
      },
      enterprise: undefined,
      user: {
        id: userId,
        scopes: userScopes,
        token: `xoxp-${Math.random()}`,
      },
    };

    globalState.installations.push(installation);
    globalState.codeToToken[code] = installation.bot?.token;

    await repository.setState(globalState);
    res.statusCode = 301;
    res.setHeader("Location", acceptUrl.toString());
    res.end();

    return;
  }

  if (Object.keys(globalState.teams).length === 0) {
    res.write(
      `
      <!DOCTYPE html>
      <html>
        <head></head>
        <body>
          <div style="display: flex">
            <form action="${basePath}/authorize" method="GET" style="margin-top: 1rem">
              <fieldset>
                <legend>Sign in with Slack</legend>

                There are no workspaces.<br>
                First <a href="${basePath}" target="_blank">create one</a> and then refresh this page.
              </fieldset>
            </form>
          </div>
        </body>
      </html>
      `.trim()
    );
    res.end();

    return;
  }

  const params = [...url.searchParams.entries()];
  const teamId =
    url.searchParams.get("team") ?? Object.keys(globalState.teams).reverse()[0];

  assert(teamId);

  const members = globalState.members[teamId]!.filter(
    (m) => m.id !== "USLACKBOT"
  );
  const userId =
    url.searchParams.get("member") &&
    members.some((m) => m.id === url.searchParams.get("member"))
      ? url.searchParams.get("member")
      : members[0]?.id;

  assert(userId);

  res.write(
    `
    <!DOCTYPE html>
    <html>
      <head></head>
      <body>
        <div style="display: flex">
          <form action="${basePath}/authorize" method="GET" style="margin-top: 1rem">
            <fieldset>
              <legend>Sign in with Slack</legend>

              ${params
                .filter(([key]) => key !== "team" && key !== "member")
                .map(
                  ([key, value]) =>
                    `<input type="hidden" name="${key}" value="${value}" />`
                )
                .join("")}

              <div>
                <label for="team">Workspace:</label>
                <select id="team" name="team" onChange="this.form.submit()">
                  ${Object.values(globalState.teams)
                    .map(
                      (t) =>
                        `<option value="${t!.id!}" ${
                          t!.id === teamId ? "selected" : ""
                        }>${t!.name!}</option>`
                    )
                    .join("")}
                </select>
              </div>

              <div>
                <label for="member">Member:</label>
                <select id="member" name="member">
                  ${members
                    .map(
                      (m) =>
                        `<option value="${m.id!}" ${
                          m.id === userId ? "checked" : ""
                        }>${m.real_name ?? m.name!}</option>`
                    )
                    .join("")}
                </select>
              </div>

              <button type="submit" style="margin-top: 0.5rem" onclick="this.form.method = 'POST'">
                Accept
              </button>
            </fieldset>
          </form>
        </div>
      </body>
    </html>
    `.trim()
  );
  res.end();
};
