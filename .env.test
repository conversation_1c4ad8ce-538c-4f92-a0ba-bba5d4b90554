NODE_ENV=test

# 1. Put your username into brackets
ORGANICE_SLACK_BOT_NAME=OrgaNice (MY NAME)
KUDOS_SLACK_BOT_NAME=OrgaNice (MY NAME)

# 2. Issue an app configuration token here: https://api.slack.com/authentication/config-tokens
# Put the _refresh_ token below:
SLACK_CONFIG_REFRESH_TOKEN=

# 3. Run `npm start`, go to https://api.slack.com/apps, select your app there,
# and click "Generate Token and Scopeы" within "App-Level Tokens" section.
# Give the token and arbitrary name, and grant access to `connections:write` scope.
# Copy the newly generated token here:
ORGANICE_SLACK_APP_TOKEN=

# 4. Ask nikityy or andrewfan for this key.
CHATGPT_API_KEY=

# Everything that is below this line doesn't require setup or sets up automatically
# -----------------------------

# Clean next 6 variables if you're switching to a new bot
SLACK_CONFIG_ACCESS_TOKEN=
SLACK_CONFIG_ACCESS_TOKEN_EXPIRES_AT=
ORGANICE_SLACK_APP_ID=1
ORGANICE_SLACK_CLIENT_ID=1
ORGANICE_SLACK_CLIENT_SECRET=1
ORGANICE_SLACK_SIGNING_SECRET=1
KUDOS_SLACK_APP_ID=2
KUDOS_SLACK_CLIENT_ID=2
KUDOS_SLACK_CLIENT_SECRET=2
KUDOS_SLACK_SIGNING_SECRET=2

SLACK_STATE_SECRET=random-string
SLACK_ADMIN_SITE_URL=http://localhost:3001
SLACK_API_URL=http://localhost:3001/api/slack/mock_api/api
SLACK_AUTHORIZATION_URL=http://localhost:3001/api/slack/mock_api/authorize
SLACK_MOCK_API_URL=http://localhost:3001/api/slack/mock_api

# Set to X if you want to receive notifications every X minutes
# Leave empty if you want production behavior
NOTIFICATION_PERIOD_IN_MINUTES=0

# Set to X if you want trial mode to expire in X minutes
# Leave empty if you want production behavior
TRIAL_DURATION_IN_MINUTES=10

# Set to X if you want to receive a follow-up message about non-finished onboarding in Intercom in X minutes
# Leave empty if you want production behavior
NEXT_PUBLIC_INTERCOM_ONBOARDING_FOLLOW_UP_DELAY_IN_MINUTES=

# Set to 1 if you want to run a local test version of Slack. It's convienient for performance testing.
# Leave empty to use real Slack.
SLACK_TEST_SERVER=1

# Set to 1 if you want worker to re-run cycles immediately, without any time gap.
# Leave empty if you want production behavior
BACKGROUND_WORKER_NO_DELAY=1

# When OrgaNice gets installed into a new workspace, send a message to the specified channel.
# Webhook URL may be generated here: https://slack.com/apps/A0F7XDUAZ-incoming-webhooks?tab=more_info
SLACK_INSTALLATION_HANDLER_WEBHOOK_URL=

# Username, password, and database name should match to docker-compose.yml
POSTGRES_CONNECTION_URL=postgresql://organice:organice@localhost:5434/organice

# Email credentials for outgoing emails
SMTP_CONNECTION_URL=smtp://test:<EMAIL>:587/

# Crypto key (must be 32 characters long)
COOKIE_ENCRYPTION_KEY=01234567890123456789012345678901

# Sentry credentials
SENTRY_URL=https://sentry.io/
SENTRY_ORG=organice-f0
SENTRY_PROJECT=organice-admin
SENTRY_AUTH_TOKEN=
NEXT_PUBLIC_SENTRY_ENVIRONMENT=test
NEXT_PUBLIC_SENTRY_DSN_ADMIN_SITE=
SENTRY_DSN_BACKGROUND_WORKER=

# Mixpanel credentials
NEXT_PUBLIC_MIXPANEL_AUTH_TOKEN=
MIXPANEL_AUTH_TOKEN=

# Intercom credentials
NEXT_PUBLIC_INTERCOM_APP_ID=
INTERCOM_SECRET_KEY=

# Stripe credentials
# Without it you won't be able to receive Stripe Webhook events.
# See README.md "How to enable Stripe" section to obtain this secret.
STRIPE_WEBHOOK_SIGNING_SECRET=
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=
STRIPE_SECRET_KEY=
STRIPE_PRODUCT_ID=

# LogRocket credentials
NEXT_PUBLIC_LOGROCKET_APP_ID=

# Token for our landing page API
# https://www.organice.app/birthday-message-generator
BIRTHDAY_GENERATOR_API_TOKEN=

# Ask nikityy for these ones if you wish to access production logs
DIGITALOCEAN_ACCESS_TOKEN=
GITHUB_TOKEN=
PULUMI_ACCESS_TOKEN=
PULUMI_CONFIG_PASSPHRASE=
