import assert from "assert";

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>er,
  ProductOwnerNotifier,
  WorkspaceRepository,
  HomePageState,
  replaceM<PERSON>ber,
  HOW_MANY_TIMES_SHOW_FEATURE_BANNER,
  Channel,
  SupportedFeature,
  SlackBotApplication,
} from "@organice/core/domain";
import { ActivityLog } from "@organice/core/domain/activityLog";
import { formatPolicyFields } from "@organice/core/domain/data-completion";
import {
  KudosQueryHandler,
  KudosRepository,
} from "@organice/core/domain/kudos";
import {
  updateApproversOnMemberChannelJoin,
  updateApproversOnMemberChannelLeave,
} from "@organice/core/domain/time-offs";
import * as Sentry from "@sentry/nextjs";
import {
  AllMiddlewareArgs,
  AnyMiddlewareArgs,
  App,
  Receiver,
} from "@slack/bolt";
import { ExtendedErrorHandlerArgs } from "@slack/bolt/dist/App";
import { InstallationStore } from "@slack/oauth";
import { WebClient } from "@slack/web-api";

import { parseMember } from "./SlackAdapterImpl";
import SlackAppManifest from "./SlackAppManifest";
import SlackBotQueryHandler from "./SlackBotQueryHandler";
import * as AdminAskToHelpFillFields from "./components/AdminAskToHelpFillFields";
import * as AnnouncementReminderMessage from "./components/AnnouncementReminderMessage";
import { getUIMemberById } from "./components/AppHome";
import * as AppHome from "./components/AppHome";
import * as AskManagerEmployeeHiredByReferral from "./components/AskManagerEmployeeHiredByReferral";
import * as AskVacancyMessage from "./components/AskVacancyMessage";
import * as CalendarEventCard from "./components/CalendarEventCard";
import * as CelebrationEventsSection from "./components/CelebrationEventsSection";
import * as DiscountedSubscriptionOffer from "./components/DiscountedSubscriptionOffer";
import * as EditReminderModal from "./components/EditReminderModal";
import * as FieldAskMessage from "./components/FieldAskMessage";
import * as GiveKudosModal from "./components/GiveKudosModal";
import * as HelpNavigation from "./components/HelpNavigation";
import * as HiredSection from "./components/HiredSection";
import NoFeaturesModal from "./components/NoFeaturesModal";
import * as NotificationButtons from "./components/Notifications/Button";
import * as NotificationPagination from "./components/Notifications/_common";
import * as OpenPositionItem from "./components/OpenPositionItem";
import * as OpenPositionsNotification from "./components/OpenPositionsNotification";
import * as PrimaryOwnerApprovalRequest from "./components/PrimaryOwnerApprovalRequest";
import * as ProfileModal from "./components/ProfileModal";
import * as ReferralCandidateDetails from "./components/ReferralCandidateDetails";
import * as ReferralModal from "./components/ReferralModal";
import * as RequestTimeOffModal from "./components/RequestTimeOffModal";
import * as SearchModal from "./components/SearchModal";
import * as NewSurveyMessage from "./components/Surveys/NewSurveyMessage";
import * as PassSurveyModal from "./components/Surveys/PassSurveyModal";
import * as RunSurveyModal from "./components/Surveys/RunSurveyModal";
import SurveyAccessLimitModal from "./components/Surveys/SurveyAccessLimitModal";
import * as SurveyCard from "./components/Surveys/SurveyCard";
import * as TimeOffStatsBreakdownModal from "./components/TimeOffPolicyModal";
import * as TimeOffRequest from "./components/TimeOffRequest";
import * as UpdateProfileButton from "./components/UpdateProfileButton";
import * as KudosValuesButton from "./components/ViewKudosValuesButton";
import {
  tryJsonParse,
  loggerFromBoltContextAndBody,
} from "./components/_common";
import { isFeatureEnabled } from "./helpers";

declare module "@slack/bolt" {
  interface Context {
    logger: Logger;
    adminSiteUrl: string;
    getActivityLog(): ActivityLog;
    getRepository(): WorkspaceRepository;
    getSlackAdapter(client: WebClient): SlackAdapter;
    getProductOwnerNotifier(): ProductOwnerNotifier;
    getKudosQueryHandler(): KudosQueryHandler;
    getKudosRepository(): KudosRepository;
    getSlackBotQueryHandler(): SlackBotQueryHandler;
    getTime(): Date;

    provideContextForUnhandledRequestHandler?(
      args: AnyMiddlewareArgs & AllMiddlewareArgs
    ): void;
  }
}

interface ConstructorOptions {
  clientId: string;
  clientSecret: string;
  signingSecret: string;
  stateSecret: string;
  slackApiUrl?: string;
  logger: Logger;
  adminSiteUrl: string;

  installationStore: InstallationStore;
  receiver?: Receiver;
  getActivityLog: () => ActivityLog;
  getRepository: () => WorkspaceRepository;
  getSlackAdapter: (client: WebClient) => SlackAdapter;
  getProductOwnerNotifier: () => ProductOwnerNotifier;
  getKudosQueryHandler: () => KudosQueryHandler;
  getKudosRepository: () => KudosRepository;
  getSlackBotQueryHandler: () => SlackBotQueryHandler;
  getTime: () => Date;
}

class SlackBot extends App {
  constructor(options: ConstructorOptions) {
    super({
      clientId: options.clientId,
      clientSecret: options.clientSecret,
      signingSecret: options.signingSecret,
      stateSecret: options.stateSecret,
      scopes: SlackAppManifest.botScopes,
      receiver: options.receiver,
      logger: options.logger,
      clientOptions: {
        slackApiUrl: options.slackApiUrl,
      },
      installerOptions: {
        userScopes: SlackAppManifest.userScopes,
      },
      installationStore: options.installationStore,
      extendedErrorHandler: true,
    });

    this.use((args) => {
      const context = args.context;

      context.logger = options.logger;
      context.getRepository = options.getRepository;
      context.getSlackAdapter = options.getSlackAdapter;
      context.getProductOwnerNotifier = options.getProductOwnerNotifier;
      context.getSlackBotQueryHandler = options.getSlackBotQueryHandler;
      context.getKudosQueryHandler = options.getKudosQueryHandler;
      context.getKudosRepository = options.getKudosRepository;
      context.getTime = options.getTime;
      context.getActivityLog = options.getActivityLog;
      context.adminSiteUrl = options.adminSiteUrl;

      context.provideContextForUnhandledRequestHandler?.(args);

      return args.next();
    });

    // Sentry middleware
    this.use((args) => {
      const spanOptions =
        "action" in args
          ? ({
              op: "slack.action",
              name:
                "action_id" in args.action
                  ? args.action.action_id
                  : "name" in args.action
                  ? args.action.name
                  : args.action.callback_id,
              attributes: args.action,
            } as const)
          : "command" in args
          ? ({
              op: "slack.command",
              name: args.command.command,
              attributes: args.command,
            } as const)
          : "event" in args
          ? ({
              op: "slack.event",
              name: args.event.type,
              attributes: args.event,
            } as const)
          : "shortcut" in args
          ? ({
              op: "slack.shortcut",
              name: args.shortcut.callback_id,
              attributes: args.shortcut,
            } as const)
          : "view" in args
          ? ({
              op: "slack.view",
              name: args.view.callback_id,
              attributes: { ...args.view, blocks: undefined },
            } as const)
          : null;

      if (spanOptions) {
        return Sentry.startSpan(
          {
            ...spanOptions,
            attributes: convertToSentryStartSpanAttributes(
              spanOptions.attributes as Record<string, unknown>
            ),
          },
          () => args.next()
        );
      }

      return args.next();
    });

    [
      AdminAskToHelpFillFields.goToOrgTreeClicked,
      AnnouncementReminderMessage.dontSendAnnouncement,
      AnnouncementReminderMessage.editAnnouncement,
      AnnouncementReminderMessage.goToAnnouncementsPricing,
      AnnouncementReminderMessage.goToAnnouncementsSettings,
      AppHome.calendarClicked,
      AppHome.clearTimeOffFilters,
      AppHome.completeOnboardingClicked,
      AppHome.departmentSelect,
      AppHome.giveKudos,
      AppHome.handleStateOfPeopleAreMissingBlock,
      AppHome.homeClearButton,
      AppHome.homeViewOnWebButton,
      AppHome.kudosClicked,
      AppHome.kudosPageSelected,
      AppHome.orgChartClicked,
      AppHome.renderKudos,
      AppHome.renderSurveys,
      AppHome.renderRequests,
      AppHome.requestTimeOffClicked,
      AppHome.selectMangerOnHomeTab,
      AppHome.teamSelect,
      AppHome.timeOffCalendarPageSelected,
      AppHome.timeOffStatusSelect,
      AppHome.calendarEventSelect,
      AppHome.clearCalendarEventFilters,
      AppHome.timeOffUserSelect,
      AppHome.viewKudosInsightsClicked,
      AppHome.surveysClicked,
      AppHome.runSurveysClicked,
      AppHome.surveyPageSelected,
      AppHome.dashboardClicked,
      AppHome.surveyTemplatesClicked,
      TimeOffStatsBreakdownModal.viewTimeOffPolicyClicked,
      RunSurveyModal.runSurveyModalSubmit,
      RunSurveyModal.surveyTemplateSelected,
      NewSurveyMessage.passSurveyClicked,
      SurveyCard.surveyCardActionsClicked,
      PassSurveyModal.passSurveyModalSubmit,
      AskManagerEmployeeHiredByReferral.hiredByReferral,
      AskManagerEmployeeHiredByReferral.hiredNotByReferral,
      AskVacancyMessage.vacancySelectAbort,
      AskVacancyMessage.vacancySelectAction,
      DiscountedSubscriptionOffer.subscribeWithDiscountClicked,
      EditReminderModal.editReminderModalSubmit,
      EditReminderModal.regenerateGif,
      FieldAskMessage.conversationsSelectAbort,
      FieldAskMessage.fieldAskAction,
      GiveKudosModal.checkedKudosGif,
      GiveKudosModal.giveKudosModalSubmit,
      GiveKudosModal.selectedKudosGif,
      HelpNavigation.openInAppClicked,
      HiredSection.addReferralOnHomeTab,
      HiredSection.handleStateOfHiredBlock,
      NotificationButtons.weeklyReminderGiveKudos,
      NotificationButtons.customLinkClicked,
      NotificationPagination.paginateBlocks,
      OpenPositionItem.goToHiringPage,
      OpenPositionsNotification.addReferral,
      PrimaryOwnerApprovalRequest.approveSyncSlackActionsClicked,
      ReferralCandidateDetails.approveCandidate,
      ReferralCandidateDetails.rejectCandidate,
      ReferralModal.referralModalSubmit,
      RequestTimeOffModal.requestTimeOffModalSelectedDateRange,
      RequestTimeOffModal.requestTimeOffModalSelectedType,
      RequestTimeOffModal.requestTimeOffModalSubmit,
      SearchModal.searchModalUserSelected,
      TimeOffRequest.approveTimeOffRequest,
      TimeOffRequest.rejectTimeOffRequest,
      TimeOffRequest.rejectTimeOffRequestConfirm,
      TimeOffRequest.discussTimeOffRequest,
      CalendarEventCard.approveTimeOffRequestFromCalendarTab,
      CalendarEventCard.deleteRequestClicked,
      CalendarEventCard.rejectTimeOffRequestFromCalendarTab,
      CalendarEventCard.rejectTimeOffRequestFromCalendarTabConfirm,
      KudosValuesButton.viewKudosValues,
      UpdateProfileButton.viewUpdateProfile,
      UpdateProfileButton.verifyManager,
      CelebrationEventsSection.celebrationEventsBlockToggleHideBirthday,
      CelebrationEventsSection.celebrationEventsBlockOpenModal,
      CelebrationEventsSection.celebrationEventsBlockUpdateField,
      UpdateProfileButton.populateProfileFields,
    ].forEach((injectable) => injectable.register(this));

    this.event(
      "user_profile_changed",
      async ({ client, context, event, body }) => {
        const repository = options.getRepository();
        const workspaceId = context.teamId ?? context.enterpriseId!;
        const updatedMember = parseMember(event.user);
        const slackAdapter = options.getSlackAdapter(client);
        let logger = loggerFromBoltContextAndBody(context, body);
        const time = options.getTime();

        let workspace = await repository.getWorkspace(workspaceId);

        // We've lost several OAuth tokens on production and we have to live with it forever...
        if (!workspace) {
          return;
        }

        const formattedPolicy = formatPolicyFields(workspace);
        const requiredFields = formattedPolicy.filter(
          (field) => field.required
        );

        logger = logger.withContext({ workspace });

        let member = workspace.members.find((x) => x.id === updatedMember.id);

        // Slack sends 'user_profile_changed' event even for users from Slack Connect,
        // which don't exist in the workspace.members list. We should skip them.
        if (!member) {
          return;
        }

        logger = logger.withContext({ member });

        if (
          member.botState.type === "required-photo-url" &&
          member.photoUrl !== updatedMember.photoUrl
        ) {
          await slackAdapter.thankUserForImageUpdate(workspace.id, member.id);

          logger.info("Answered to the field", {
            field_name: "Profile image",
            name: member.realName,
            required_fields: requiredFields.length,
          });

          member = {
            ...member,
            ...updatedMember,
          };

          workspace = replaceMember(workspace, member);
          await repository.setWorkspace(workspace);

          member = await FieldAskMessage.askNextFieldOrThank(
            slackAdapter,
            logger,
            time,
            workspace,
            member
          );
          workspace = replaceMember(workspace, member);
          await repository.setWorkspace(workspace);
        } else if (
          updatedMember.isSlackWorkspaceAdmin !==
            member.isSlackWorkspaceAdmin ||
          updatedMember.isSlackWorkspaceOwner !==
            member.isSlackWorkspaceOwner ||
          updatedMember.isSlackWorkspacePrimaryOwner !==
            member.isSlackWorkspacePrimaryOwner
        ) {
          logger.info("Member admin status has changed");
          member = {
            ...member,
            isSlackWorkspaceAdmin: updatedMember.isSlackWorkspaceAdmin,
            isSlackWorkspaceOwner: updatedMember.isSlackWorkspaceOwner,
            isSlackWorkspacePrimaryOwner:
              updatedMember.isSlackWorkspacePrimaryOwner,
          };
          workspace = replaceMember(workspace, member);
          await repository.setWorkspace(workspace);
        } else if (
          member.status?.text !== updatedMember.status?.text ||
          member.status?.emoji !== updatedMember.status?.emoji ||
          member.status?.expiresAt !== updatedMember.status?.expiresAt
        ) {
          member = {
            ...member,
            ...updatedMember,
          };

          workspace = replaceMember(workspace, member);
          await repository.setWorkspace(workspace);
        }
      }
    );

    this.event("team_join", async () => {
      // Do nothing
    });

    this.event("channel_created", async ({ body, context, event }) => {
      const repository = options.getRepository();
      const logger = loggerFromBoltContextAndBody(context, body);
      const workspaceId = context.teamId ?? context.enterpriseId!;
      let workspace = await repository.getWorkspace(workspaceId);

      // We've lost several OAuth tokens on production and we have to live with it forever...
      if (!workspace) {
        return;
      }
      const newChannel: Channel = {
        id: event.channel.id,
        name: event.channel.name,
        is_general: false,
        membersCount: 1,
      };

      workspace = {
        ...workspace,
        channels: [...workspace.channels, newChannel],
      };

      await repository.setWorkspace(workspace);
      logger
        .withContext({ workspace, channel: newChannel })
        .info("Created new channel");
    });

    this.event("channel_deleted", async () => {
      // Do nothing. This event conflicts with `refreshWorkspaceChannels` from background worker.
    });

    this.event("tokens_revoked", async ({ client, context, body, event }) => {
      const logger = loggerFromBoltContextAndBody(context, body);
      const workspaceId = (context.teamId ?? context.enterpriseId)!;
      const repository = options.getRepository();
      const productOwnerNotifier = options.getProductOwnerNotifier();

      let workspace = await repository.getWorkspace(workspaceId);

      assert(workspace, "Workspace not found");

      const slackAdapter = options.getSlackAdapter(client);
      const application = workspace.slackBotToken
        ? slackAdapter.getSlackBotApplicationByAppId(
            workspace.slackBotToken.appId
          )
        : SlackBotApplication.ORGANICE;

      for (const memberId of event.tokens.oauth ?? []) {
        logger.info("Token has been revoked", { memberId });

        workspace = {
          ...workspace,
          slackMemberTokens: {
            ...workspace.slackMemberTokens,
            [memberId]: undefined,
          },
        };
      }

      for (const memberId of event.tokens.bot ?? []) {
        logger.info("Bot token has been revoked", { memberId });

        if (workspace.slackBotToken?.botMemberId === memberId) {
          workspace = {
            ...workspace,
            slackBotToken: undefined,
          };
        }
      }

      await repository.setWorkspace(workspace);

      if (
        !workspace.slackBotToken &&
        Object.keys(workspace.slackMemberTokens).length === 0
      ) {
        logger.info("App has been uninstalled");

        workspace = {
          ...workspace,
          wasDeleted: true,
        };
        await repository.setWorkspace(workspace);
        await productOwnerNotifier.handleUninstallation({
          application,
          workspace: {
            name: workspace.name,
            url: workspace.url,
          },
        });
      }
    });

    this.shortcut(
      "open-profile-message-shortcut",
      async ({ shortcut, ack, body, client, context }) => {
        await ack();

        assert(
          shortcut.type === "message_action",
          "Expected action to be a message action"
        );
        assert(shortcut.message.user, "Expected user to be defined");

        const workspaceId = (context.teamId ?? context.enterpriseId)!;
        const memberId = shortcut.message.user;
        const triggerId = shortcut.trigger_id;
        const repository = options.getRepository();
        let logger = loggerFromBoltContextAndBody(context, body);

        const workspace = await repository.getWorkspace(workspaceId);

        assert(workspace, `Workspace with ${workspaceId} not found`);
        logger = logger.withContext({ workspace });

        const domainMember = workspace.members.find((x) => x.id === memberId);

        assert(domainMember, `Member not found`);
        logger = logger.withContext({ member: domainMember });

        logger.info("Message shortcut used - Open profile");

        const member = getUIMemberById(workspace, memberId);

        assert(member, `Member not found`);

        await client.views.open({
          view: ProfileModal.default(member, context.adminSiteUrl, workspaceId),
          trigger_id: triggerId,
          notify_on_close: true,
        });
      }
    );

    this.shortcut(
      "open-search-global-shortcut",
      async ({ shortcut, ack, body, client, context }) => {
        await ack();

        const workspaceId = (context.teamId ?? context.enterpriseId)!;
        const logger = loggerFromBoltContextAndBody(context, body);

        logger.info("Global search shortcut used");

        await client.views.open({
          view: SearchModal.default({}, context.adminSiteUrl, workspaceId),
          trigger_id: shortcut.trigger_id,
          notify_on_close: true,
        });
      }
    );

    this.shortcut(
      "request-time-off-shortcut",
      async ({ shortcut, ack, client, context, body }) => {
        await ack();

        const workspaceId = (context.teamId ?? context.enterpriseId)!;

        assert(workspaceId, "Expected workspace id to exist");

        const repository = options.getRepository();
        let logger = loggerFromBoltContextAndBody(context, body);
        const slackAdapter = options.getSlackAdapter(client);
        const memberId = shortcut.user.id;
        const triggerId = shortcut.trigger_id;

        const workspace = await repository.getWorkspace(workspaceId);

        assert(workspace, `Workspace not found`);
        logger = logger.withContext({ workspace });

        const member = workspace.members.find((x) => x.id === memberId);

        assert(member, `Member not found`);

        if (
          !isFeatureEnabled(
            workspace.onboarding.bookmarkedFeatures,
            SupportedFeature.TimeOffs
          )
        ) {
          await client.views.open({
            view: NoFeaturesModal({
              me: member,
              adminSiteUrl: context.adminSiteUrl,
            }),
            trigger_id: triggerId,
          });

          return;
        }
        logger = logger.withContext({ member });

        logger.info("Time off request shortcut used");

        await AppHome.openRequestTimeOffModal(
          slackAdapter,
          client,
          triggerId,
          workspace,
          member
        );
      }
    );

    this.shortcut(
      "run-survey-shortcut",
      async ({ shortcut, ack, client, context, body }) => {
        await ack();

        const workspaceId = (context.teamId ?? context.enterpriseId)!;

        assert(workspaceId, "Expected workspace id to exist");

        const repository = options.getRepository();
        let logger = loggerFromBoltContextAndBody(context, body);
        const memberId = shortcut.user.id;
        const triggerId = shortcut.trigger_id;

        const workspace = await repository.getWorkspace(workspaceId);

        assert(workspace, `Workspace not found`);
        logger = logger.withContext({ workspace });

        const member = workspace.members.find((x) => x.id === memberId);

        assert(member, `Member not found`);

        if (
          !isFeatureEnabled(
            workspace.onboarding.bookmarkedFeatures,
            SupportedFeature.Surveys
          )
        ) {
          await client.views.open({
            view: NoFeaturesModal({
              me: member,
              adminSiteUrl: context.adminSiteUrl,
            }),
            trigger_id: triggerId,
          });

          return;
        }
        logger = logger.withContext({ member });

        logger.info("Run Survey shortcut triggered");

        if (member.isAdmin) {
          await client.views.open({
            view: RunSurveyModal.default(
              {
                templates: workspace.surveyTemplates,
              },
              { currentTemplate: null }
            ),
            trigger_id: triggerId,
            notify_on_close: true,
          });
        } else {
          await client.views.open({
            view: SurveyAccessLimitModal(),
            trigger_id: triggerId,
            notify_on_close: true,
          });
        }
      }
    );

    this.shortcut(
      "give-kudos-shortcut",
      async ({ shortcut, ack, client, context, body }) => {
        await ack();

        const workspaceId = (context.teamId ?? context.enterpriseId)!;

        assert(workspaceId, "Expected workspace id to exist");

        const repository = options.getRepository();
        let logger = loggerFromBoltContextAndBody(context, body);
        const memberId = shortcut.user.id;
        const triggerId = shortcut.trigger_id;

        const workspace = await repository.getWorkspace(workspaceId);

        assert(workspace, `Workspace not found`);
        logger = logger.withContext({ workspace });

        const member = workspace.members.find((x) => x.id === memberId);

        assert(member, `Member not found`);

        if (
          !isFeatureEnabled(
            workspace.onboarding.bookmarkedFeatures,
            SupportedFeature.Kudos
          )
        ) {
          await client.views.open({
            view: NoFeaturesModal({
              me: member,
              adminSiteUrl: context.adminSiteUrl,
            }),
            trigger_id: triggerId,
          });

          return;
        }
        logger = logger.withContext({ member });

        logger.info("Kudos - Home tab: 'Give Kudos' clicked");

        const queryHandler = context.getSlackBotQueryHandler();

        await client.views.open({
          view: GiveKudosModal.default(
            await queryHandler.getKudosModalProps(
              workspace,
              member,
              context.getTime()
            ),
            {
              withGIF: false,
            }
          ),
          trigger_id: triggerId,
        });
      }
    );

    this.shortcut(
      "give-kudos-message-shortcut",
      async ({ shortcut, ack, client, context, body }) => {
        await ack();

        const workspaceId = (context.teamId ?? context.enterpriseId)!;

        assert(workspaceId, "Expected workspace id to exist");

        assert(
          shortcut.type === "message_action",
          "Expected action to be a message action"
        );
        assert(shortcut.message.user, "Expected user to be defined");

        const repository = options.getRepository();
        let logger = loggerFromBoltContextAndBody(context, body);
        const memberId = shortcut.user.id;
        const triggerId = shortcut.trigger_id;
        const messageAuthorId = shortcut.message.user;
        const messageText = shortcut.message.text ?? "";

        const workspace = await repository.getWorkspace(workspaceId);

        assert(workspace, `Workspace not found`);
        logger = logger.withContext({ workspace });

        const member = workspace.members.find((x) => x.id === memberId);

        assert(member, `Member not found`);

        if (
          !isFeatureEnabled(
            workspace.onboarding.bookmarkedFeatures,
            SupportedFeature.Kudos
          )
        ) {
          await client.views.open({
            view: NoFeaturesModal({
              me: member,
              adminSiteUrl: context.adminSiteUrl,
            }),
            trigger_id: triggerId,
          });

          return;
        }
        logger = logger.withContext({ member });

        logger.info("Kudos - 'Give Kudos' message shortcut called");

        const queryHandler = context.getSlackBotQueryHandler();

        await client.views.open({
          view: GiveKudosModal.default(
            await queryHandler.getKudosModalProps(
              workspace,
              member,
              context.getTime()
            ),
            {
              withGIF: false,
              defaultReceiverId: messageAuthorId,
              defaultText: messageText,
            }
          ),
          trigger_id: triggerId,
        });
      }
    );

    this.event("app_home_opened", async ({ body, client, context, event }) => {
      const workspaceId = (context.teamId ?? context.enterpriseId)!;
      const memberId = event.user;
      const repository = options.getRepository();
      let logger = loggerFromBoltContextAndBody(context, body);

      if (event.tab === "messages") {
        const workspace = await repository.getWorkspace(workspaceId);

        assert(workspace, `Workspace not found`);
        logger = logger.withContext({ workspace });

        const member = workspace.members.find((x) => x.id === memberId);

        assert(member, `Member not found`);
        logger = logger.withContext({ member });

        logger.info("Messages tab opened");
      } else {
        const state = body.event.view?.private_metadata
          ? (tryJsonParse(
              body.event.view.private_metadata
            ) as HomePageState | null)
          : null;

        const slackAdapter = options.getSlackAdapter(client);
        let workspace = await repository.getWorkspace(workspaceId);

        assert(workspace, `Workspace not found`);

        logger = logger.withContext({ workspace });

        const member = workspace.members.find((m) => m.id === memberId);

        assert(member, `Member not found`);

        if (
          member.howManyTimesHomeTabVisited <=
          HOW_MANY_TIMES_SHOW_FEATURE_BANNER
        ) {
          workspace = replaceMember(workspace, {
            ...member,
            howManyTimesHomeTabVisited: member.howManyTimesHomeTabVisited + 1,
          });

          await repository.setWorkspace(workspace);
        }

        logger = logger.withContext({ member });

        logger.info("Home tab opened");

        await slackAdapter.renderActiveTab(workspace, member, state);
      }
    });

    this.event("member_joined_channel", async ({ body, context, event }) => {
      const repository = options.getRepository();
      let logger = loggerFromBoltContextAndBody(context, body);
      const workspaceId = (context.teamId ?? context.enterpriseId)!;
      const memberId = event.user;
      const channelId = event.channel;

      let workspace = await repository.getWorkspace(workspaceId);

      // We've lost several OAuth tokens on production and we have to live with it forever...
      if (!workspace) {
        return;
      }

      logger = logger.withContext({ workspace });

      const member = workspace.members.find((x) => x.id === memberId);

      // Slack sends 'user_profile_changed' event even for users from Slack Connect,
      // which don't exist in the workspace.members list. We should skip them.
      if (!member) {
        return;
      }

      logger = logger.withContext({ member });

      workspace = updateApproversOnMemberChannelJoin(
        logger,
        workspace,
        member,
        channelId
      );

      await repository.setWorkspace(workspace);
    });

    this.event("member_left_channel", async ({ body, context, event }) => {
      const repository = options.getRepository();
      let logger = loggerFromBoltContextAndBody(context, body);
      const workspaceId = (context.teamId ?? context.enterpriseId)!;
      const memberId = event.user;
      const channelId = event.channel;

      let workspace = await repository.getWorkspace(workspaceId);

      // We've lost several OAuth tokens on production and we have to live with it forever...
      if (!workspace) {
        return;
      }

      logger = logger.withContext({ workspace });

      const member = workspace.members.find((x) => x.id === memberId);

      // Slack sends 'user_profile_changed' event even for users from Slack Connect,
      // which don't exist in the workspace.members list. We should skip them.
      if (!member) {
        return;
      }

      logger = logger.withContext({ member });

      workspace = updateApproversOnMemberChannelLeave(
        logger,
        workspace,
        member,
        channelId
      );

      await repository.setWorkspace(workspace);
    });

    // eslint-disable-next-line @typescript-eslint/require-await
    this.error(async ({ body, context, error }: ExtendedErrorHandlerArgs) => {
      const logger = loggerFromBoltContextAndBody(context, body);

      logger.error(error);

      const res = error.res;

      if (res) {
        res.statusCode = 500;
      }
    });
  }
}

function convertToSentryStartSpanAttributes(
  obj: Record<string, unknown>
): Record<string, string | number | boolean> {
  const result: Record<string, string | number | boolean> = {};

  for (const [key, value] of Object.entries(obj)) {
    if (typeof value === "object" && value !== null) {
      const nested = convertToSentryStartSpanAttributes(
        value as Record<string, unknown>
      );

      for (const [nestedKey, nestedValue] of Object.entries(nested)) {
        result[`${key}.${nestedKey}`] = nestedValue;
      }
    } else if (Array.isArray(value)) {
      for (const [index, item] of value.entries()) {
        const nested = convertToSentryStartSpanAttributes(
          item as Record<string, unknown>
        );

        for (const [nestedKey, nestedValue] of Object.entries(nested)) {
          result[`${index}.${nestedKey}`] = nestedValue;
        }
      }
    } else if (
      typeof value === "string" ||
      typeof value === "number" ||
      typeof value === "boolean"
    ) {
      result[key] = value;
    }
  }

  return result;
}

export default SlackBot;
