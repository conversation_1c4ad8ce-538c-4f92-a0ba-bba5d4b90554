import assert from "assert";

import { SlackBotApplication } from "@organice/core/domain";
import {
  Button,
  Message,
  MessageBuilder,
  Section,
  Actions,
} from "slack-block-builder";

import { declareAction, loggerFromBoltContextAndBody } from "./_common";

interface Props {
  app: SlackBotApplication;
  adminSiteUrl: string;
  adminId: string;
  senderId: string;
  workspaceId: string;
}

export const approveSyncSlackActionsClicked = declareAction(
  { action_id: "approve_sync_slack_actions_clicked" },
  async ({ ack, action, body, context }) => {
    assert(
      action.type === "button",
      `Expected "approve_sync_slack_actions_clicked" action to be triggered on button click, instead got "${action.type}"`
    );

    const logger = loggerFromBoltContextAndBody(context, body);

    logger.info("Primary owner clicked 'Approve' and was redirected to OAuth");

    await ack();
  }
);

function PrimaryOwnerApprovalRequest({
  app,
  adminSiteUrl,
  adminId,
  senderId,
  workspaceId,
}: Props): MessageBuilder {
  return Message()
    .channel(adminId)
    .text(`Hey there`)
    .blocks(
      Section().text(
        [
          `Hey there`,
          `<@${senderId}> wants to sync OrgaNice's fields with Slack, but we need Slack Primary Owner's approval in order for it to work properly.`,
        ].join("\n")
      ),
      Actions()
        .blockId("approve_sync_slack_actions")
        .elements(
          Button()
            .primary()
            .text("Approve")
            .actionId(approveSyncSlackActionsClicked.actionId)
            .url(
              {
                [SlackBotApplication.ORGANICE]: `${adminSiteUrl}/api/slack/organice/oauth?${new URLSearchParams(
                  {
                    workspace_id: workspaceId,
                  }
                ).toString()}`,
                [SlackBotApplication.KUDOS]: `${adminSiteUrl}/api/slack/kudos/oauth?${new URLSearchParams(
                  {
                    workspace_id: workspaceId,
                  }
                ).toString()}`,
              }[app]
            )
        )
    );
}

export default PrimaryOwnerApprovalRequest;
