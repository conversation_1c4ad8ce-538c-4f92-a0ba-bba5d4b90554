// eslint-disable-next-line max-classes-per-file
import assert from "assert";

import {
  NotifyAdminAboutAnnouncementProps,
  Candidate,
  Channel,
  CustomField,
  CustomFieldHasntBeenUpdatedError,
  DigestPosition,
  EmployeeReferenceStatus,
  GreetUserOptions,
  Member,
  MemberPolicyViolation,
  MembersCompletionCounts,
  MessageNotFoundError,
  Policy,
  Position,
  ProgressBar,
  ReadonlyDeep,
  ReferralDetailsOptions,
  ReferralModalOptions,
  ResultOfReference,
  SlackAdapter,
  SlackMember,
  SlackPlan,
  SlackWorkspace,
  TeamProfile,
  UpdateMember,
  UpdateMemberCustomFields,
  VacancyOption,
  TimeOffRequest,
  PresetPolicyId,
  Workspace,
  EditReminderMessageProps,
  RenderTrialExpiredModalOptions,
  DeletedTimeOffNotificationsType,
  OrgChartHomePageState,
  CalendarHomePageState,
  KudosHomePageState,
  SurveysHomePageState,
  UISurvey,
  Survey,
  TimeOffNotificationType,
  GlobalContext,
  HomePageState,
  HomePages,
  UIMember,
  SurveyStatus,
  Department,
  RootNode,
  KudosValue,
  TimeOffPolicyType,
  DashboardHomePageState,
  UpdateMemberOptions,
  SlackBotApplication,
  NotificationBlockData,
  PublishedNotificationState,
  StatusHasntBeenUpdatedError,
} from "@organice/core/domain";
import { collectMemberData } from "@organice/core/domain/data-completion";
import {
  getMembersWithoutManager,
  getNode,
  getNodes,
  getSubordinatesInfo,
} from "@organice/core/domain/org-chart";
import { getDigestPosition } from "@organice/core/domain/referrals";
import { getSlackUISurvey } from "@organice/core/domain/surveys";
import {
  ChannelCreateForbiddenError,
  ChannelNameTakenError,
} from "@organice/core/domain/time-offs";
import SentrySpanMethods from "@organice/core/sentry/SentrySpanMethods";
import {
  AuthTestResponse,
  ChatPostMessageResponse,
  ChatScheduleMessageArguments,
  ChatUpdateResponse,
  WebClient,
} from "@slack/web-api";
import { Channel as SlackChannel } from "@slack/web-api/dist/response/ConversationsListResponse";
import {
  Field,
  Profile,
} from "@slack/web-api/dist/response/UsersProfileGetResponse";
import { invert, difference, sortBy } from "lodash";
import { Message, Section } from "slack-block-builder";

import SlackBotQueryHandler from "./SlackBotQueryHandler";
import AdminAskToHelpFillFields from "./components/AdminAskToHelpFillFields";
import AnnouncementBasic from "./components/AnnouncementBasic";
import AnnouncementMessage from "./components/AnnouncementMessage";
import AnnouncementReminderMessage from "./components/AnnouncementReminderMessage";
import {
  KudosPage,
  OrgChartPage,
  CalendarPage,
  SurveysPage,
  getDefaultKudosHomePageState,
  getCalendarEventsForCurrentCalendarPage,
  getDefaultCalendarHomePageState,
  getDefaultOrgChartHomePageState,
  getHomeTabs,
  getUIMemberById,
  inferCalendarHomePageState,
  inferOrgChartHomePageState,
  inferSurveysHomePageState,
  inferKudosHomePageState,
  getDefaultSurveysHomePageState,
  getNodeManager,
  getJoinedPeopleInLast30Days,
  getDefaultDashboardHomePageState,
  DashboardPage,
  inferDashboardHomePageState,
} from "./components/AppHome";
import AskManagerEmployeeHiredByReferral from "./components/AskManagerEmployeeHiredByReferral";
import AskVacancyMessage from "./components/AskVacancyMessage";
import DiscountedSubscriptionOffer from "./components/DiscountedSubscriptionOffer";
import FieldAskMessage from "./components/FieldAskMessage";
import KudosDirectMessage, { KudosChannelMessage } from "./components/Kudos";
import NotificationMessage from "./components/Notifications/Notification";
import NotifyAboutSendingKudos from "./components/NotifyAbountSendingKudos";
import NotifyAboutReceivingAccruals from "./components/NotifyAboutReceivingAccruals";
import NotifyAboutTimeOffChangeInChannel from "./components/NotifyAboutTimeOffChangeInChannel";
import NotifyAboutTimeOffInChannel from "./components/NotifyAboutTimeOffInChannel";
import NotifyAdminAboutAdminsRights from "./components/NotifyAdminAboutAdminsRights";
import NotifyAdminAboutOrgTreeMessage from "./components/NotifyAdminAboutOrgTreeMessage";
import NotifyAdminsAboutAppSumoSubscriptionSoonExpiration from "./components/NotifyAdminsAboutAppSumoSubscriptionSoonExpiration";
import NotifyAdminsAboutArchivedNotificationsChannel from "./components/NotifyAdminsAboutArchivedNotificationsChannel";
import NotifyAdminsAboutArchivedTimeOffsChannel from "./components/NotifyAdminsAboutArchivedTimeOffsChannel";
import NotifyAdminsAboutTrialExpiration from "./components/NotifyAdminsAboutTrialExpiration";
import NotifyMemberMangerAboutTimeOffStatus from "./components/NotifyMemberMangerAboutTimeOffStatus";
import NotifyRequesterAboutTimeOff from "./components/NotifyRequesterAboutTimeOff";
import NotifyRequesterAboutTimeOffChange from "./components/NotifyRequesterAboutTimeOffChange";
import NotifyThatApprovedTimeOffWasDeleted from "./components/NotifyThatApprovedTimeOffWasDeleted";
import OpenPositionsNotification from "./components/OpenPositionsNotification";
import PopulatedProfileMessage, {
  PopulateFieldsMessage,
} from "./components/PopulatedProfileMessage";
import PrimaryOwnerApprovalRequest from "./components/PrimaryOwnerApprovalRequest";
import ReferralCandidateDetails from "./components/ReferralCandidateDetails";
import ReferralCandidateResult from "./components/ReferralCandidateResult";
import ReferralModal from "./components/ReferralModal";
import ReferralRecognitionMessage from "./components/ReferralRecognitionMessage";
import NewSurveyMessage from "./components/Surveys/NewSurveyMessage";
import ThanksMessage from "./components/ThanksMessage";
import TimeOffReminder from "./components/TimeOffReminder";
import TimeOffRequestMessage from "./components/TimeOffRequest";
import TrialExpiredModal from "./components/TrialExpiredModal";
import UpdateProfileButton from "./components/UpdateProfileButton";

class RateLimiter {
  private lastTS: number | null = null;

  constructor(private maxRequestsPerMinute: number) {}

  async wait(): Promise<void> {
    const safeDelay = 60_000 / this.maxRequestsPerMinute;
    const ts = this.lastTS;

    this.lastTS = Date.now();

    if (typeof ts === "number") {
      const millisecondsSinceLastCall = Date.now() - ts;

      if (millisecondsSinceLastCall < safeDelay) {
        const restDelay = safeDelay - millisecondsSinceLastCall;

        await new Promise((resolve) => {
          setTimeout(resolve, restDelay);
        });
      }
    }
  }
}

class PerWorkspaceRateLimiter {
  private rateLimiters = new Map<string, RateLimiter>();

  constructor(private maxRequestsPerMinute: number) {}

  async wait(workspaceId: string): Promise<void> {
    if (!this.rateLimiters.has(workspaceId)) {
      this.rateLimiters.set(
        workspaceId,
        new RateLimiter(this.maxRequestsPerMinute)
      );
    }

    await this.rateLimiters.get(workspaceId)!.wait();
  }
}

class SlackAdapterImpl implements SlackAdapter {
  static rateLimiters = {
    "chat.delete": new PerWorkspaceRateLimiter(17),
    "conversations.list": new PerWorkspaceRateLimiter(10),
    "team.billing.info": new PerWorkspaceRateLimiter(50),
    "users.list": new PerWorkspaceRateLimiter(1 / 3),
    "users.profile.get": new PerWorkspaceRateLimiter(100),
    "users.profile.set": new PerWorkspaceRateLimiter(20),
  };

  client: WebClient;
  adminSiteUrl: string;
  appIdResolverMap: Record<string, SlackBotApplication | undefined>;
  scopes?: readonly string[];
  queryHandler: SlackBotQueryHandler;

  constructor(props: {
    client: WebClient;
    adminSiteUrl: string;
    appIds: Record<SlackBotApplication, string>;
    scopes?: readonly string[];
    queryHandler: SlackBotQueryHandler;
  }) {
    this.client = props.client;
    this.adminSiteUrl = props.adminSiteUrl;
    this.appIdResolverMap = invert(props.appIds) as Record<
      string,
      SlackBotApplication | undefined
    >;
    this.scopes = props.scopes;
    this.queryHandler = props.queryHandler;
  }

  get token(): string {
    assert(this.client.token, "Expected this.client.token to be defined");

    return this.client.token;
  }

  async getWorkspace(workspaceId: string): Promise<SlackWorkspace | null> {
    const { team } = await this.client.team.info({
      team: workspaceId,
    });
    const profile = await this.getTeamProfile(workspaceId);

    assert(team, "Expected team");
    assert(team.id, "Expected team.id");
    assert(team.name, "Expected team.name");

    const url =
      team.url ??
      (team.domain ? `https://${team.domain}.slack.com/` : undefined);

    assert(url, "Expected team.url or team.domain");

    return {
      id: team.id,
      name: team.name,
      iconUrl: team.icon?.image_230,
      url,
      profile,
    };
  }

  async setUserPhoto(fileUrl: string, userToken: string): Promise<boolean> {
    const response = await fetch(fileUrl, {
      headers: {
        Authorization: `Bearer ${userToken}`,
      },
    });
    const buffer = Buffer.from(await response.arrayBuffer());

    const { ok } = await this.client.users.setPhoto({
      token: userToken,
      image: buffer,
    });

    return ok;
  }

  async getProfileConfiguration(
    workspace: Workspace,
    memberId: string | null,
    userToken: string | null
  ): Promise<TeamProfile> {
    const { id: workspaceId, members } = workspace;
    const profile = await this.getTeamProfile(workspaceId);
    let updatedFields = profile.fields;
    const peopleFields = profile.fields.filter(
      (f) => f.type === "user" && f.visible
    );

    if (userToken && memberId && peopleFields.length > 0) {
      updatedFields = await this.getPeopleFieldsConfiguration(
        workspace.id,
        memberId,
        members,
        updatedFields,
        peopleFields,
        workspace.policy,
        userToken
      );
    }

    return {
      sections: profile.sections,
      fields: updatedFields,
      defaultManagerFieldId: profile.defaultManagerFieldId,
      defaultDepartmentFieldId: profile.defaultDepartmentFieldId,
      defaultTeamFieldId: profile.defaultTeamFieldId,
      defaultPhoneFieldId: profile.defaultPhoneFieldId,
      defaultTitleFieldId: profile.defaultTitleFieldId,
      defaultCountryFieldId: profile.defaultCountryFieldId,
    };
  }

  async getMembersPartially(workspaceId: string): Promise<SlackMember[]> {
    await SlackAdapterImpl.rateLimiters["users.list"].wait(workspaceId);

    const response = await this.client.users.list({
      team_id: workspaceId,
      exclude_archived: true,
    });

    return (
      response.members
        ?.filter(
          (member) =>
            !member.is_bot &&
            !member.deleted &&
            member.name !== "slackbot" &&
            !member.is_restricted &&
            !member.is_ultra_restricted
        )
        .map(parseMember) ?? []
    );
  }

  async getWorkspaceMembers(workspaceId: string): Promise<SlackMember[]> {
    const members = await this.getRawMembers(workspaceId);

    return members.map(parseMember);
  }

  async getWorkspaceMembersCount(workspaceId: string): Promise<number> {
    const response = await this.client.conversations.list({
      team_id: workspaceId,
      exclude_archived: true,
    });
    const generalChannel = response.channels?.find((c) => !!c.is_general);
    const count = generalChannel?.num_members ?? 1;

    return count;
  }

  async getMember(memberId: string): Promise<SlackMember | null> {
    const { user } = await this.client.users.info({
      token: this.token,
      user: memberId,
    });

    const isValid =
      user &&
      !user.is_bot &&
      !user.deleted &&
      user.name !== "slackbot" &&
      !user.is_restricted &&
      !user.is_ultra_restricted;

    return isValid ? parseMember(user) : null;
  }

  async getMemberCustomFields(
    workspaceId: string,
    memberId: string,
    policy: Policy
  ): Promise<Record<string, string>> {
    await SlackAdapterImpl.rateLimiters["users.profile.get"].wait(workspaceId);

    const { profile } = await this.client.users.profile.get({
      user: memberId,
    });

    assert(profile, "Expected profile");

    const fields = parseCustomFields(profile.fields);

    const phoneSlackFieldId = policy[PresetPolicyId.PHONE].slackFieldId;
    const jobTitleSlackFieldId = policy[PresetPolicyId.JOB_TITLE].slackFieldId;

    if (phoneSlackFieldId) {
      fields[phoneSlackFieldId] =
        fields[phoneSlackFieldId] || (profile.phone ?? "");
    }

    if (jobTitleSlackFieldId) {
      fields[jobTitleSlackFieldId] =
        fields[jobTitleSlackFieldId] || (profile.title ?? "");
    }

    return fields;
  }

  getSlackBotApplicationByAppId(appId: string): SlackBotApplication {
    const app = this.appIdResolverMap[appId];

    assert(app, `Unknown Slack bot app: ${appId}`);

    return app;
  }

  async deleteReminder(channel: string, ts: string): Promise<void> {
    await this.client.chat.deleteScheduledMessage({
      channel,
      scheduled_message_id: ts,
    });
  }

  async postTimeOffInChannel(
    timeOff: ReadonlyDeep<TimeOffRequest>,
    channel: string,
    context: GlobalContext
  ): Promise<void> {
    await this.client.chat.postMessage(
      NotifyAboutTimeOffInChannel(timeOff, channel, context).buildToObject()
    );
  }

  async postTimeOffChangeInChannel(
    prevTimeOff: ReadonlyDeep<TimeOffRequest>,
    timeOff: ReadonlyDeep<TimeOffRequest>,
    editor: ReadonlyDeep<Member>,
    channel: string,
    context: GlobalContext
  ): Promise<void> {
    await this.client.chat.postMessage(
      NotifyAboutTimeOffChangeInChannel(
        prevTimeOff,
        timeOff,
        editor,
        channel,
        context
      ).buildToObject()
    );
  }

  async sendReminderAboutTimeOffRequest(
    channel: string,
    postAt: Date,
    threadTs?: string
  ): Promise<{
    ts: string;
    channel: string;
  }> {
    const payload = TimeOffReminder({
      channel,
      channelType: this.isDirectMessageChannel(channel) ? "dm" : "private",
      threadTs,
      postAt: Math.floor(postAt.getTime() / 1000).toString(),
    }).buildToObject() as unknown as ChatScheduleMessageArguments;

    const message = await this.client.chat.scheduleMessage(payload);

    assert(
      message.scheduled_message_id,
      "Expected message scheduled id to exist"
    );
    assert(message.channel, "Expected message chanel id to exist");

    return { ts: message.scheduled_message_id, channel: message.channel };
  }

  async notifyThatApprovedTimeOffWasDeleted(
    channel: string,
    deletedBy: string,
    type: DeletedTimeOffNotificationsType,
    threadTs: string
  ): Promise<void> {
    await this.client.chat.postMessage(
      NotifyThatApprovedTimeOffWasDeleted(
        channel,
        deletedBy,
        type,
        threadTs
      ).buildToObject()
    );
  }

  async getMessagePermalink(
    channel: string,
    messageTS: string
  ): Promise<string> {
    const response = await this.client.chat.getPermalink({
      channel,
      message_ts: messageTS,
    });

    const permalink = response.permalink;

    assert(permalink, "Expected permalink to be generated");

    return permalink;
  }

  async postKudosInChannel(
    senderId: string,
    channel: string,
    recipientsIds: string[],
    data: {
      message: string;
      quantity: number;
      gifUrl?: string;
      kudosValues: KudosValue[];
    }
  ): Promise<{ ts: string; channel: string }> {
    const response = await this.client.chat.postMessage(
      KudosChannelMessage({
        channel,
        senderId,
        text: data.message,
        quantity: data.quantity,
        gifUrl: data.gifUrl,
        recipientsIds,
        kudosValues: data.kudosValues,
      }).buildToObject()
    );

    assert(
      response.channel,
      "Expected 'channel' to exist at chat.postMessage response"
    );
    assert(response.ts, "Expected 'ts' to exist at chat.postMessage response");

    return {
      ts: response.ts,
      channel: response.channel,
    };
  }

  async postNotification(
    channel: string,
    blocks: NotificationBlockData[],
    title: string,
    context: GlobalContext,
    notificationId?: string
  ): Promise<{ ts: string }> {
    const message = await this.client.chat.postMessage(
      NotificationMessage({
        notificationId,
        blocks,
        context,
        channel,
        title,
      }).buildToObject()
    );

    assert(message.ts, "Expected message.ts to exist");

    return {
      ts: message.ts,
    };
  }

  async updateNotification(
    notificationId: string,
    channel: string,
    blocks: NotificationBlockData[],
    title: string,
    state: ReadonlyDeep<PublishedNotificationState>,
    ts: string,
    context: GlobalContext
  ): Promise<{ ts: string }> {
    const message = await this.client.chat.update({
      ...NotificationMessage({
        notificationId,
        blocks,
        context,
        channel,
        state,
        title,
      }).buildToObject(),
      channel,
      ts,
    });

    assert(message.ts, "Expected message.ts to exist");

    return {
      ts: message.ts,
    };
  }

  async sendKudosAsDirectMessage(
    senderId: string,
    recipientsIds: string[],
    data: {
      message: string;
      quantity: number;
      gifUrl?: string;
      kudosValues: KudosValue[];
    }
  ): Promise<{ ts: string; channel: string }[]> {
    const responses = await Promise.all(
      recipientsIds.map((id) =>
        this.client.chat.postMessage(
          KudosDirectMessage({
            channel: id,
            senderId,
            text: data.message,
            quantity: data.quantity,
            gifUrl: data.gifUrl,
            kudosValues: data.kudosValues,
          }).buildToObject()
        )
      )
    );

    const result = responses.map((item) => {
      assert(
        item.channel,
        "Expected 'channel' to exist at chat.postMessage response"
      );
      assert(item.ts, "Expected 'ts' to exist at chat.postMessage response");

      return {
        ts: item.ts,
        channel: item.channel,
      };
    });

    return result;
  }

  async sendTimeOffRequest(
    timeOff: ReadonlyDeep<TimeOffRequest>,
    channel: string,
    context: GlobalContext
  ): Promise<{ ts: string; channel: string }> {
    const privateChannelId = timeOff.notifications.find(
      (x) =>
        x.type === TimeOffNotificationType.TimeOffRequest &&
        !this.isDirectMessageChannel(x.recipientId)
    )?.recipientId;

    const response = await this.client.chat.postMessage(
      TimeOffRequestMessage({
        channel,
        channelType: this.isDirectMessageChannel(channel) ? "dm" : "private",
        timeOff,
        showTimeOffsSummary: true,
        context,
        privateChannelUrl: privateChannelId
          ? `${context.workspace.url}/messages/${privateChannelId}`
          : undefined,
      }).buildToObject()
    );

    assert(
      response.channel,
      "Expected 'channel' to exist at chat.postMessage response"
    );
    assert(response.ts, "Expected 'ts' to exist at chat.postMessage response");

    return {
      ts: response.ts,
      channel: response.channel,
    };
  }

  async updateTimeOffRequest(
    context: GlobalContext,
    timeOff: ReadonlyDeep<TimeOffRequest>,
    channel: string,
    ts: string,
    deletedByMemberId?: string
  ): Promise<void> {
    const privateChannelId = timeOff.notifications.find(
      (x) =>
        x.type === TimeOffNotificationType.TimeOffRequest &&
        !this.isDirectMessageChannel(x.recipientId)
    )?.recipientId;

    await this.client.chat.update({
      ...TimeOffRequestMessage({
        channel,
        channelType: this.isDirectMessageChannel(channel) ? "dm" : "private",
        timeOff,
        deletedByMemberId,
        showTimeOffsSummary: false,
        privateChannelUrl: privateChannelId
          ? `${context.workspace.url}/messages/${privateChannelId}`
          : undefined,
        context,
      }).buildToObject(),
      channel,
      ts,
    });
  }

  async notifyMemberMangerAboutTimeOffStatus(
    _workspaceId: string,
    managerId: string,
    timeOff: ReadonlyDeep<TimeOffRequest>,
    context: GlobalContext
  ): Promise<{ ts: string; channel: string }> {
    const response = await this.client.chat.postMessage(
      NotifyMemberMangerAboutTimeOffStatus({
        timeOff,
        context,
      })
        .channel(managerId)
        .buildToObject()
    );

    assert(
      response.channel,
      "Expected 'channel' to exist at chat.postMessage response"
    );
    assert(response.ts, "Expected 'ts' to exist at chat.postMessage response");

    return {
      ts: response.ts,
      channel: response.channel,
    };
  }

  async createPrivateChannel(
    name: string,
    participants: string[]
  ): Promise<{ channel: string }> {
    try {
      const response = await this.client.conversations.create({
        name,
        is_private: true,
      });

      assert(
        response.channel,
        "Expected 'channel' to exist at conversations.create response"
      );
      assert(
        response.channel.id,
        "Expected 'channel.id' to exist at conversations.create response"
      );

      await this.client.conversations.invite({
        channel: response.channel.id,
        users: participants.join(","),
      });

      return { channel: response.channel.id };
    } catch (error) {
      if (error instanceof Error && error.message.includes("name_taken")) {
        throw new ChannelNameTakenError();
      } else if (
        error instanceof Error &&
        error.message.includes("restricted_action")
      ) {
        throw new ChannelCreateForbiddenError();
      }

      throw error;
    }
  }

  async archiveChannel(channel: string): Promise<void> {
    await this.client.conversations.archive({
      channel,
    });
  }

  // eslint-disable-next-line class-methods-use-this
  isDirectMessageChannel(channelId: string): boolean {
    return channelId.startsWith("D") || channelId.startsWith("U");
  }

  async notifyRequesterAboutTimeOff(
    timeOff: ReadonlyDeep<TimeOffRequest>,
    context: GlobalContext
  ): Promise<{ ts: string; channel: string }> {
    const response = await this.client.chat.postMessage(
      NotifyRequesterAboutTimeOff({
        timeOff,
        context,
      }).buildToObject()
    );

    assert(
      response.channel,
      "Expected 'channel' to exist at chat.postMessage response"
    );
    assert(response.ts, "Expected 'ts' to exist at chat.postMessage response");

    return {
      ts: response.ts,
      channel: response.channel,
    };
  }

  async notifyRequesterAboutTimeOffChange(
    prevTimeOff: ReadonlyDeep<TimeOffRequest>,
    timeOff: ReadonlyDeep<TimeOffRequest>,
    editor: ReadonlyDeep<Member>,
    context: GlobalContext
  ): Promise<{ ts: string; channel: string }> {
    const response = await this.client.chat.postMessage(
      NotifyRequesterAboutTimeOffChange({
        prevTimeOff,
        timeOff,
        editor,
        context,
      }).buildToObject()
    );

    assert(
      response.channel,
      "Expected 'channel' to exist at chat.postMessage response"
    );
    assert(response.ts, "Expected 'ts' to exist at chat.postMessage response");

    return {
      ts: response.ts,
      channel: response.channel,
    };
  }

  async notifyAboutSendingKudos(
    senderId: string,
    recipientsIds: string[],
    kudosLeft: number
  ): Promise<{ ts: string; channel: string }> {
    const response = await this.client.chat.postMessage(
      NotifyAboutSendingKudos({
        senderId,
        recipientsIds,
        kudosLeft,
      }).buildToObject()
    );

    assert(
      response.channel,
      "Expected 'channel' to exist at chat.postMessage response"
    );
    assert(response.ts, "Expected 'ts' to exist at chat.postMessage response");

    return {
      ts: response.ts,
      channel: response.channel,
    };
  }

  async sendTimeOffsChannelWasArchivedReminder(
    workspaceId: string,
    adminId: string
  ): Promise<{ ts: string; channel: string }> {
    const response = await this.client.chat.postMessage(
      NotifyAdminsAboutArchivedTimeOffsChannel({
        adminSiteUrl: this.adminSiteUrl,
        memberId: adminId,
        workspaceId,
      }).buildToObject()
    );

    assert(
      response.channel,
      "Expected 'channel' to exist at chat.postMessage response"
    );
    assert(response.ts, "Expected 'ts' to exist at chat.postMessage response");

    return {
      ts: response.ts,
      channel: response.channel,
    };
  }

  async sendNotificationsChannelWasArchivedReminder(
    workspaceId: string,
    adminId: string,
    notificationTitles: string[]
  ): Promise<{ ts: string; channel: string }> {
    const response = await this.client.chat.postMessage(
      NotifyAdminsAboutArchivedNotificationsChannel({
        adminSiteUrl: this.adminSiteUrl,
        memberId: adminId,
        workspaceId,
        notificationTitles,
      }).buildToObject()
    );

    assert(
      response.channel,
      "Expected 'channel' to exist at chat.postMessage response"
    );
    assert(response.ts, "Expected 'ts' to exist at chat.postMessage response");

    return {
      ts: response.ts,
      channel: response.channel,
    };
  }

  async notifyAdminsAboutTrialExpiration(memberId: string): Promise<void> {
    await this.client.chat.postMessage(
      NotifyAdminsAboutTrialExpiration({
        memberId,
        adminSiteUrl: this.adminSiteUrl,
      }).buildToObject()
    );
  }

  async notifyAdminsAboutAppSumoSubscriptionSoonExpiration(
    memberId: string
  ): Promise<void> {
    await this.client.chat.postMessage(
      NotifyAdminsAboutAppSumoSubscriptionSoonExpiration({
        memberId,
        adminSiteUrl: this.adminSiteUrl,
      }).buildToObject()
    );
  }

  async askToPopulateProfileFields(
    workspaceId: string,
    memberId: string,
    progress: ProgressBar,
    channel: string | undefined,
    ts: string | undefined
  ): Promise<{ ts: string | undefined; channel: string | undefined }> {
    const orgChartUrl = `${this.adminSiteUrl}/users?workspace=${workspaceId}`;

    if (ts == null || channel == null) {
      const response = await this.client.chat.postMessage(
        PopulateFieldsMessage({
          memberId,
          progress,
          orgChartUrl,
        }).buildToObject()
      );

      return {
        ts: response.ts,
        channel: response.channel,
      };
    }

    const response = await this.client.chat.update({
      ...PopulateFieldsMessage({
        memberId,
        progress,
        orgChartUrl,
      }).buildToObject(),
      channel,
      ts,
    });

    return {
      ts: response.ts,
      channel: response.channel,
    };
  }

  async sendPopulatedProfileMessage(
    workspace: ReadonlyDeep<Workspace>,
    memberId: string,
    channel: string | undefined,
    ts: string | undefined
  ): Promise<{ ts: string | undefined; channel: string | undefined }> {
    const orgChartUrl = `${this.adminSiteUrl}/users?workspace=${workspace.id}`;

    if (ts == null || channel == null) {
      const response = await this.client.chat.postMessage(
        PopulatedProfileMessage({
          workspace,
          memberId,
          orgChartUrl,
        }).buildToObject()
      );

      return {
        ts: response.ts,
        channel: response.channel,
      };
    }

    const response = await this.client.chat.update({
      ...PopulatedProfileMessage({
        workspace,
        memberId,
        orgChartUrl,
      }).buildToObject(),
      channel,
      ts,
    });

    return {
      ts: response.ts,
      channel: response.channel,
    };
  }

  async sendFollowUpMessage(
    memberId: string,
    threadTs: string,
    input: {
      violations: ReadonlyDeep<MemberPolicyViolation>[];
      isPolicyRecentlyChanged: boolean;
      installedBy: string;
    }
  ): Promise<{ ts: string | undefined; channel: string | undefined }> {
    const message = Message()
      .channel(memberId)
      .text(
        input.violations.length > 0
          ? "Complete Your Profile: Just a Few More Fields Left!"
          : "Hey, your profile is now fully completed!"
      )
      .blocks([
        Section()
          .text(
            input.isPolicyRecentlyChanged
              ? `<@${memberId}> new ${
                  input.violations.length > 1 ? "fields were" : "field was"
                } added by <@${input.installedBy}>: ${input.violations
                  .map((v) => `*${v.label}*`)
                  .join(
                    ", "
                  )}. Please fill in the missing information in your Slack profile.`
              : `<@${memberId}> please fill in the missing information in your Slack profile`
          )
          .accessory(UpdateProfileButton({ title: "Update Profile" })),
      ])
      .buildToObject();

    const response = await this.client.chat.postMessage({
      ...message,
      thread_ts: threadTs,
    });

    return {
      ts: response.ts,
      channel: response.channel,
    };
  }

  async sendFieldAskMessage(
    workspaceId: string,
    memberId: string,
    violation: MemberPolicyViolation,
    progress: ProgressBar
  ): Promise<{ ts: string | undefined; channel: string | undefined }> {
    const response = await this.client.chat.postMessage(
      FieldAskMessage({
        memberId,
        violation,
        progress,
      }).buildToObject()
    );

    return {
      ts: response.ts,
      channel: response.channel,
    };
  }

  async approveFieldAskMessage(
    workspaceId: string,
    memberId: string,
    violation: MemberPolicyViolation,
    channel: string,
    ts: string,
    progress: ProgressBar,
    value?: string,
    updatesCount?: number
  ): Promise<void> {
    await this.client.chat.update({
      ...FieldAskMessage({
        memberId,
        violation,
        updatesCount,
        progress,
        status: { type: "ok" },
        value,
      }).buildToObject(),
      channel,
      ts,
    });
  }

  async rejectFieldAskMessage(
    workspaceId: string,
    memberId: string,
    violation: MemberPolicyViolation,
    channel: string,
    ts: string,
    progress: ProgressBar,
    error: string
  ): Promise<void> {
    await this.client.chat.update({
      ...FieldAskMessage({
        memberId,
        violation,
        progress,
        status: { error, type: "error" },
      }).buildToObject(),
      channel,
      ts,
    });
  }

  async sendVacancyAskMessage(
    memberId: string,
    vacancies: Readonly<VacancyOption>[]
  ): Promise<{ ts: string | undefined; channel: string | undefined }> {
    const response = await this.client.chat.postMessage(
      AskVacancyMessage({
        memberId,
        vacancies,
      }).buildToObject()
    );

    return {
      ts: response.ts,
      channel: response.channel,
    };
  }

  async approveVacancyAskMessage(
    memberId: string,
    channel: string,
    ts: string,
    vacancies?: Readonly<VacancyOption>[]
  ): Promise<void> {
    await this.client.chat.update({
      ...AskVacancyMessage({
        memberId,
        vacancies,
        status: { type: "ok" },
      }).buildToObject(),
      channel,
      ts,
    });
  }

  async rejectVacancyAskMessage(
    memberId: string,
    channel: string,
    ts: string,
    error: string,
    vacancies?: Readonly<VacancyOption>[]
  ): Promise<void> {
    await this.client.chat.update({
      ...AskVacancyMessage({
        memberId,
        vacancies,
        status: { error, type: "error" },
      }).buildToObject(),
      channel,
      ts,
    });
  }

  async updateMember(
    workspaceId: string,
    { memberId, realName, email, customFields, status }: UpdateMember,
    policy: Policy,
    userToken: string,
    options: UpdateMemberOptions = { throwIfHaventChangedFields: true }
  ): Promise<Profile | null> {
    let phone: string | undefined;
    let title: string | undefined;

    const phoneSlackFieldId = policy[PresetPolicyId.PHONE].slackFieldId;
    const jobTitleSlackFieldId = policy[PresetPolicyId.JOB_TITLE].slackFieldId;

    if (phoneSlackFieldId && customFields?.[phoneSlackFieldId]) {
      phone = customFields[phoneSlackFieldId].value ?? "";
    }

    if (jobTitleSlackFieldId && customFields?.[jobTitleSlackFieldId]) {
      title = customFields[jobTitleSlackFieldId].value ?? "";
    }

    await SlackAdapterImpl.rateLimiters["users.profile.set"].wait(workspaceId);

    let updateProfile: Profile = {
      real_name: realName,
      phone,
      title,
      email,
      fields:
        customFields && Object.keys(customFields).length > 0
          ? (customFields as Record<string, Field> | undefined)
          : undefined,
    };

    if (status) {
      updateProfile = {
        ...updateProfile,
        status_text: status.statusText,
        status_emoji: status.statusEmoji,
        status_expiration: status.statusExpiration
          ? new Date(status.statusExpiration).getTime() / 1000
          : 0,
      };
    }

    const hasUpdates = JSON.stringify(updateProfile) !== "{}";

    if (hasUpdates) {
      let response;

      try {
        response = await this.client.users.profile.set({
          token: userToken,
          user: memberId,
          profile: JSON.stringify(updateProfile),
        });
      } catch (error) {
        if (
          error instanceof Error &&
          (error.message.includes("invalid_user") ||
            error.message.includes("partial_profile_set_failed")) &&
          customFields &&
          Object.keys(customFields).length === 1
        ) {
          throw new CustomFieldHasntBeenUpdatedError(customFields);
        } else if (
          error instanceof Error &&
          (error.message.includes("invalid_user") ||
            error.message.includes("cannot_update_status")) &&
          status
        ) {
          throw new StatusHasntBeenUpdatedError(status);
        } else {
          throw error;
        }
      }

      if (
        options.throwIfHaventChangedFields &&
        customFields &&
        response.profile
      ) {
        validateCustomFieldsUpdate(
          customFields,
          parseCustomFields(response.profile.fields)
        );
      }

      return response.profile ?? null;
    }

    return null;
  }

  async sugestUserToInstall(
    workspaceId: string,
    memberId: string
  ): Promise<void> {
    await this.client.chat.postMessage({
      channel: memberId,
      blocks: [
        {
          type: "section",
          text: {
            type: "mrkdwn",
            text: "But firstly please Install App.",
          },
          accessory: {
            type: "button",
            text: {
              type: "plain_text",
              text: "Install",
            },
            value: "Install App",
            url: `${this.adminSiteUrl}`,
          },
        },
      ],
    });
  }

  async greetUser({
    installedBy,
    message,
    recipient,
    sender,
  }: GreetUserOptions): Promise<{ channel: string; ts: string }> {
    const text = message
      .replace(
        "<RECIPIENT>",
        recipient.name ? `@${recipient.name}` : "team member"
      )
      .replace("<ME>", sender.name ? `@${sender.name}` : "admin")
      .replace("<INSTALLER_NAME>", installedBy ? `<@${installedBy}>` : "admin");

    const response = await this.client.chat.postMessage({
      channel: recipient.id,
      blocks: [
        {
          type: "section",
          text: {
            type: "mrkdwn",
            text,
          },
        },
      ],
      text,
    });

    assert(
      response.channel,
      "Expected 'channel' to exist at chat.postMessage response"
    );
    assert(response.ts, "Expected 'ts' to exist at chat.postMessage response");

    return { channel: response.channel, ts: response.ts };
  }

  async getSlackBillingInfo(
    workspaceId: string
  ): Promise<Pick<SlackPlan, "plan">> {
    await SlackAdapterImpl.rateLimiters["team.billing.info"].wait(workspaceId);

    const info = await this.client.team.billing.info();

    const plan = info.plan as SlackPlan["plan"] | undefined;

    return {
      plan: plan && plan.length > 0 ? plan : "free",
    };
  }

  async deleteMessage(
    workspaceId: string,
    ts: string,
    channel: string
  ): Promise<void> {
    try {
      await SlackAdapterImpl.rateLimiters["chat.delete"].wait(workspaceId);

      await this.client.chat.delete({
        channel,
        ts,
      });
    } catch (e) {
      if (e instanceof Error && e.message.includes("message_not_found")) {
        throw new MessageNotFoundError(
          "Failed to call chat.delete because the message was not found"
        );
      } else {
        throw e;
      }
    }
  }

  async sendOpenPositionsNotification(
    memberId: string,
    positions: DigestPosition[]
  ): Promise<ChatPostMessageResponse> {
    const message = await this.client.chat.postMessage(
      OpenPositionsNotification({
        adminSiteUrl: this.adminSiteUrl,
        positions,
        memberId,
      }).buildToObject()
    );

    return message;
  }

  async updateOpenPositionsNotification(
    id: string,
    ts: string,
    positions: DigestPosition[]
  ): Promise<ChatUpdateResponse> {
    const result = await this.client.chat.update({
      ...OpenPositionsNotification({
        adminSiteUrl: this.adminSiteUrl,
        positions,
        memberId: id,
      }).buildToObject(),
      id,
      ts,
    });

    return result;
  }

  async updateReferralCandidateDetails(
    positionId: string,
    channel: string,
    ts: string,
    candidate: Readonly<Candidate>,
    userId: string,
    isShowHandledByInStatus: boolean
  ): Promise<ChatUpdateResponse> {
    const message = await this.client.chat.update({
      ...ReferralCandidateDetails(
        {
          userId,
          positionId,
          candidate,
          isShowHandledByInStatus,
        },
        this.adminSiteUrl
      ).buildToObject(),
      channel,
      ts,
    });

    return message;
  }

  async updateAskHiredByReferralMessage(
    channel: string,
    ts: string,
    node: Position,
    isShowResolvedBy: boolean,
    status: EmployeeReferenceStatus,
    employeeId: string,
    userId: string,
    resolvedBy?: string
  ): Promise<ChatUpdateResponse> {
    const message = await this.client.chat.update({
      ...AskManagerEmployeeHiredByReferral({
        resolvedBy,
        position: node,
        adminSiteUrl: this.adminSiteUrl,
        isShowResolvedBy,
        status,
        employeeId,
        userId,
        candidates: [],
      }).buildToObject(),
      channel,
      ts,
    });

    return message;
  }

  async checkToken(token: string): Promise<AuthTestResponse> {
    const result = await this.client.auth.test({
      token,
    });

    return result;
  }

  async sendReferralRecognitionMessage(
    memberId: string,
    referrerId: string,
    employeeId: string
  ): Promise<void> {
    await this.client.chat.postMessage(
      ReferralRecognitionMessage({
        memberId,
        employeeId,
        referrerId,
      }).buildToObject()
    );
  }

  async openReferralModal({
    triggerId,
    positionId,
    title,
    triggeredBy,
    showPeopleAreMissingOnOrgChartBlock,
  }: ReferralModalOptions): Promise<void> {
    await this.client.views.open({
      view: ReferralModal(
        positionId,
        title,
        triggeredBy,
        showPeopleAreMissingOnOrgChartBlock
      ),
      trigger_id: triggerId,
      notify_on_close: true,
    });
  }

  async updateReminderMessage({
    celebrantId,
    channelId,
    messageText,
    gifUrl,
    ts,
    eventType,
    editedBy,
    showCallToActionButton,
    showEditButton,
    showDontSendButton,
    cancelled,
    workspaceId,
  }: EditReminderMessageProps): Promise<void> {
    try {
      await this.client.chat.update({
        ...AnnouncementReminderMessage({
          celebrantId,
          channelId,
          messageText,
          gifUrl,
          eventType,
          editedBy,
          showCallToActionButton,
          showDontSendButton,
          showEditButton,
          cancelled,
          adminSiteUrl: this.adminSiteUrl,
          workspaceId,
        }).buildToObject(),
        channel: channelId,
        ts,
      });
    } catch (e) {
      if (e instanceof Error && e.message.includes("message_not_found")) {
        throw new MessageNotFoundError(
          "Failed to call chat.update because the message was not found"
        );
      } else {
        throw e;
      }
    }
  }

  async sendReferralCandidateDetails(
    referral: ReferralDetailsOptions
  ): Promise<ChatUpdateResponse> {
    const message = await this.client.chat.postMessage(
      ReferralCandidateDetails(referral, this.adminSiteUrl).buildToObject()
    );

    return message;
  }

  async sendCandidateStatusToReferrer({
    memberId,
    name,
    position,
    status,
    positionId,
  }: ResultOfReference): Promise<void> {
    await this.client.chat.postMessage(
      ReferralCandidateResult({
        memberId,
        name,
        position,
        status,
        positionId,
        adminSiteUrl: this.adminSiteUrl,
      }).buildToObject()
    );
  }

  async askManagersEmployeeHiredByReferral(
    node: ReadonlyDeep<Position>,
    userId: string,
    employeeId: string,
    candidates: Candidate[]
  ): Promise<ChatUpdateResponse> {
    const message = await this.client.chat.postMessage(
      AskManagerEmployeeHiredByReferral({
        position: node,
        adminSiteUrl: this.adminSiteUrl,
        userId,
        employeeId,
        candidates,
      }).buildToObject()
    );

    return message;
  }

  async thankUserForImageUpdate(
    workspaceId: string,
    memberId: string
  ): Promise<void> {
    await this.client.chat.postMessage({
      channel: memberId,
      text: `Thanks for the photo update!`,
    });
  }

  async thankUser(
    workspaceId: string,
    memberId: string,
    position: Position,
    progress: ProgressBar
  ): Promise<void> {
    await this.client.chat.postMessage(
      ThanksMessage({
        memberId,
        adminSiteUrl: this.adminSiteUrl,
        position,
        workspaceId,
        progress,
      }).buildToObject()
    );
  }

  async notifyAdminAboutAdminsRights(
    adminId: string,
    userId: string,
    workspaceId: string
  ): Promise<void> {
    await this.client.chat.postMessage(
      NotifyAdminAboutAdminsRights({
        adminId,
        userId,
        adminSiteUrl: this.adminSiteUrl,
        workspaceId,
      }).buildToObject()
    );
  }

  async notifyAdminAboutOrgTree(
    adminId: string,
    memberGroups: MembersCompletionCounts,
    workspaceId: string
  ): Promise<void> {
    await this.client.chat.postMessage(
      NotifyAdminAboutOrgTreeMessage({
        adminId,
        adminSiteUrl: this.adminSiteUrl,
        memberGroups,
        workspaceId,
      }).buildToObject()
    );
  }

  async notifyAdminAboutAnnouncement({
    celebrantId,
    channelId,
    messageText,
    eventType,
    gifUrl,
    showEditButton,
    showCallToActionButton,
    showDontSendButton,
    workspaceId,
  }: NotifyAdminAboutAnnouncementProps): Promise<{
    ts: string;
    channel: string;
  }> {
    const payload = AnnouncementReminderMessage({
      celebrantId,
      channelId,
      messageText,
      gifUrl,
      eventType,
      showCallToActionButton,
      showDontSendButton,
      showEditButton,
      adminSiteUrl: this.adminSiteUrl,
      workspaceId,
    }).buildToObject() as unknown as ChatScheduleMessageArguments;

    const message = await this.client.chat.postMessage(payload);

    assert(message.ts, "Expect ts is exist");
    assert(message.channel, "Expect channel is exist");

    return {
      ts: message.ts,
      channel: message.channel,
    };
  }

  async postAnnouncement(
    channelId: string,
    messageText: string,
    postAt: Date,
    gifUrl?: string | null
  ): Promise<{ scheduledMessageId: string }> {
    const payload = AnnouncementMessage({
      channelId,
      messageText,
      gifUrl,
      postAt: (postAt.getTime() / 1000).toString(),
    }).buildToObject() as unknown as ChatScheduleMessageArguments;

    const message = await this.client.chat.scheduleMessage(payload);

    assert(
      message.scheduled_message_id,
      "Expected message scheduled id to exist"
    );

    return { scheduledMessageId: message.scheduled_message_id };
  }

  async postMessage(
    channelId: string,
    messageText: string,
    threadTS?: string
  ): Promise<ChatPostMessageResponse> {
    return this.client.chat.postMessage(
      AnnouncementBasic({
        channelId,
        messageText,
        threadTS,
      }).buildToObject()
    );
  }

  async askAdminToHelpFillFields(
    adminId: string,
    memberGroups: MembersCompletionCounts,
    needHelpMemberIds: string[],
    workspaceId: string
  ): Promise<void> {
    await this.client.chat.postMessage(
      AdminAskToHelpFillFields({
        adminId,
        adminSiteUrl: this.adminSiteUrl,
        memberGroups,
        needHelpMemberIds,
        workspaceId,
      }).buildToObject()
    );
  }

  async askPrimaryOwnerToApprove(
    workspaceId: string,
    adminId: string,
    senderId: string,
    app: SlackBotApplication
  ): Promise<ChatPostMessageResponse> {
    return this.client.chat.postMessage(
      PrimaryOwnerApprovalRequest({
        app,
        adminSiteUrl: this.adminSiteUrl,
        adminId,
        senderId,
        workspaceId,
      }).buildToObject()
    );
  }

  async runNewSurvey(survey: Survey): Promise<ChatPostMessageResponse> {
    return this.client.chat.postMessage(
      NewSurveyMessage({
        survey,
      }).buildToObject()
    );
  }

  async renderOrgChartHomeTab(
    workspace: ReadonlyDeep<Workspace>,
    member: ReadonlyDeep<Member> | ReadonlyDeep<UIMember>,
    state: OrgChartHomePageState
  ): Promise<void> {
    const positions = getNodes(
      workspace.orgTree.rootNode,
      (n): n is Position =>
        n.type === "position" &&
        n.memberId === null &&
        n.title !== "" &&
        !!n.reference?.isIncludeToReferrals
    );
    const digestPositions = getDigestPosition(workspace, positions);
    const { assignedPositionNumber } = getSubordinatesInfo(
      workspace.orgTree.rootNode.subordinates
    );
    const joinedPeople = getJoinedPeopleInLast30Days(workspace, new Date());
    const membersWithoutManager = getMembersWithoutManager(workspace);
    const departments = getNodes(
      workspace.orgTree.rootNode,
      (n): n is Department => n.type === "department"
    );
    const teams = workspace.teams;

    let filteredMembersOnOrganizationTab: ReadonlyDeep<UIMember[]> = [];

    if (state.organizationSelectedDepartment) {
      const selectedDepartment = getNode(
        workspace.orgTree.rootNode,
        (x): x is Department =>
          x.type === "department" &&
          x.id === state.organizationSelectedDepartment!.id
      );

      assert(selectedDepartment, "Department not found");

      filteredMembersOnOrganizationTab = getMembersFromDepartment(
        selectedDepartment,
        workspace
      );

      if (state.organizationSelectedTeam) {
        const selectedTeam = teams.find(
          ({ id }) => id === state.organizationSelectedTeam!.id
        );

        assert(selectedTeam, "Team not found");

        const teamMembers = getMembersFromTeam(
          workspace.orgTree.rootNode,
          workspace,
          selectedTeam.id
        );

        filteredMembersOnOrganizationTab =
          filteredMembersOnOrganizationTab.filter((tm) =>
            teamMembers.some((m) => m.id === tm.id)
          );
      }
    }

    if (state.organizationSelectedTeam) {
      const selectedTeam = teams.find(
        ({ id }) => id === state.organizationSelectedTeam!.id
      );

      assert(selectedTeam, "Team not found");

      filteredMembersOnOrganizationTab = getMembersFromTeam(
        workspace.orgTree.rootNode,
        workspace,
        selectedTeam.id
      );

      if (state.organizationSelectedDepartment) {
        const selectedDepartment = departments.find(
          ({ id }) => id === state.organizationSelectedDepartment!.id
        );

        assert(selectedDepartment, "Department not found");

        const departmentMembers = getMembersFromDepartment(
          selectedDepartment,
          workspace
        );

        filteredMembersOnOrganizationTab =
          filteredMembersOnOrganizationTab.filter((tm) =>
            departmentMembers.some((m) => m.id === tm.id)
          );
      }
    }

    await this.client.views.publish({
      view: OrgChartPage(
        {
          workspaceId: workspace.id,
          isCompletedOnboarding: workspace.onboarding.completed,
          teams,
          departments,
          filteredMembersOnOrganizationTab,
          currentMember: member,
          trialExpired:
            workspace.billing.subscription.type === "trial" &&
            !workspace.billing.subscription.ok,
          assignedPositionNumber,
          installedBy: workspace.installedBy,
          joinedPeople,
          companyName: workspace.name,
          membersWithoutManager,
          openPositions: digestPositions,
          enableHiring: workspace.reference.isEnable,
          enabledFeatures: workspace.onboarding.bookmarkedFeatures,
        },
        state,
        this.adminSiteUrl
      ),
      user_id: member.id,
    });
  }

  async renderCalendarHomeTab(
    workspace: ReadonlyDeep<Workspace>,
    memberId: string,
    state: CalendarHomePageState = getDefaultCalendarHomePageState()
  ): Promise<void> {
    const now = new Date();
    const memberPosition = getNode(
      workspace.orgTree.rootNode,
      (node): node is Position =>
        node.type === "position" && node.memberId === memberId
    );
    const subordinates = memberPosition?.subordinates ?? [];
    const currentMember = workspace.members.find((m) => m.id === memberId);

    assert(currentMember, `Member not found`);

    const calendarEvents = getCalendarEventsForCurrentCalendarPage(
      workspace,
      currentMember,
      state,
      now
    );

    let selectedUser;

    if (state.selectedUser) {
      selectedUser = workspace.members.find((m) => m.id === state.selectedUser);

      assert(selectedUser, `Member with ${state.selectedUser} not found`);
    }

    const memberData = selectedUser
      ? collectMemberData(workspace, selectedUser)
      : undefined;

    await this.client.views.publish({
      view: CalendarPage(
        {
          workspaceId: workspace.id,
          currentMember,
          trialExpired:
            workspace.billing.subscription.type === "trial" &&
            !workspace.billing.subscription.ok,
          allTimeOffsRequests: workspace.timeOffs.requests,
          events: calendarEvents,
          workspaceMembers: [
            ...workspace.members.map(
              ({
                id,
                isAdmin,
                name,
                photoUrl,
                photo512Url,
                realName,
                email,
                updated,
                hideBirthday,
              }) => ({
                id,
                isAdmin,
                name,
                photoUrl,
                photo512Url,
                realName,
                email,
                updated,
                hideBirthday,
              })
            ),
          ],
          memberSubordinates: subordinates,
          memberData,
          selectedUser,
          countries: [...workspace.holidaysSettings.countries],
          timeOffTypes: [...workspace.timeOffs.types],
          enabledFeatures: workspace.onboarding.bookmarkedFeatures,
        },
        state,
        this.adminSiteUrl,
        {
          workspace,
        }
      ),
      user_id: currentMember.id,
    });
  }

  async renderKudosHomeTab(
    workspace: ReadonlyDeep<Workspace>,
    member: ReadonlyDeep<Member>,
    stateArg: KudosHomePageState | "given_kudos" | "received_kudos"
  ): Promise<void> {
    const state =
      stateArg === "given_kudos"
        ? getDefaultKudosHomePageState("given_kudos")
        : stateArg === "received_kudos"
        ? getDefaultKudosHomePageState("received_kudos")
        : stateArg;

    await this.client.views.publish({
      view: KudosPage(
        await this.queryHandler.getKudosHomePageProps(
          state,
          workspace,
          member,
          new Date()
        ),
        state,
        this.adminSiteUrl
      ),
      user_id: member.id,
    });
  }

  async renderSurveysHomeTab(
    workspace: ReadonlyDeep<Workspace>,
    member: ReadonlyDeep<Member> | ReadonlyDeep<UIMember>,
    state = getDefaultSurveysHomePageState()
  ): Promise<void> {
    const sortedSurveys: ReadonlyDeep<UISurvey[]> = sortBy(
      workspace.surveys.filter((s) =>
        state.currentSurveyPage === "closed_surveys"
          ? s.status === SurveyStatus.Closed
          : state.currentSurveyPage === "in_progress_surveys" || !member.isAdmin
          ? s.status === SurveyStatus.InProgress
          : true
      ),
      (s) => s.title.toLowerCase()
    ).map((s) => getSlackUISurvey(workspace, s));

    await this.client.views.publish({
      view: SurveysPage(
        {
          workspaceId: workspace.id,
          currentMember: member,
          trialExpired:
            workspace.billing.subscription.type === "trial" &&
            !workspace.billing.subscription.ok,
          surveys: sortedSurveys,
          enabledFeatures: workspace.onboarding.bookmarkedFeatures,
        },
        state,
        this.adminSiteUrl
      ),
      user_id: member.id,
    });
  }

  async renderDashboardHomeTab(
    workspace: ReadonlyDeep<Workspace>,
    member: ReadonlyDeep<Member>,
    state: DashboardHomePageState
  ): Promise<void> {
    await this.client.views.publish({
      view: DashboardPage(
        {
          currentMember: member,
          enabledFeatures: workspace.onboarding.bookmarkedFeatures,
          kudosCurrentCycleStatsProps:
            await this.queryHandler.getCurrentCycleStatsProps(
              workspace,
              member,
              new Date()
            ),
        },
        state,
        this.adminSiteUrl,
        {
          workspace,
        }
      ),
      user_id: member.id,
    });
  }

  async renderActiveTab(
    workspace: ReadonlyDeep<Workspace>,
    member: ReadonlyDeep<Member>,
    state: HomePageState | null,
    getDefaultState?: (pages: {
      [HomePages.Surveys]: SurveysHomePageState;
      [HomePages.Kudos]: (
        kudosPreset: KudosHomePageState["currentKudosPage"]
      ) => KudosHomePageState;
    }) => HomePageState
  ): Promise<void> {
    const allTabs = getHomeTabs(workspace.onboarding.bookmarkedFeatures);
    const pages = {
      [HomePages.Surveys]: getDefaultSurveysHomePageState(),
      [HomePages.Kudos]: (
        kudosPreset: KudosHomePageState["currentKudosPage"]
      ) => getDefaultKudosHomePageState(kudosPreset),
    };
    const currentState = state ?? getDefaultState?.(pages);

    if (!allTabs.length) {
      await this.renderOrgChartHomeTab(
        workspace,
        member,
        getDefaultOrgChartHomePageState()
      );

      return;
    }
    const activeTab =
      allTabs.find((tab) => tab.name === currentState?.currentPage) ??
      allTabs[0];
    const currentMember = getUIMemberById(workspace, member.id);

    if (activeTab.name === HomePages.Calendar) {
      await this.renderCalendarHomeTab(
        workspace,
        member.id,
        inferCalendarHomePageState(
          currentState ?? getDefaultCalendarHomePageState()
        )
      );
    } else if (activeTab.name === HomePages.OrgChart) {
      await this.renderOrgChartHomeTab(
        workspace,
        currentMember,
        inferOrgChartHomePageState(
          currentState ?? getDefaultOrgChartHomePageState()
        )
      );
    } else if (activeTab.name === HomePages.Surveys) {
      await this.renderSurveysHomeTab(
        workspace,
        currentMember,
        inferSurveysHomePageState(
          currentState ?? getDefaultSurveysHomePageState()
        )
      );
    } else if (activeTab.name === HomePages.Dashboard) {
      await this.renderDashboardHomeTab(
        workspace,
        member,
        inferDashboardHomePageState(
          currentState ?? getDefaultDashboardHomePageState()
        )
      );
    } else {
      await this.renderKudosHomeTab(
        workspace,
        member,
        inferKudosHomePageState(currentState ?? getDefaultKudosHomePageState())
      );
    }
  }

  async sendSurveyReminder(survey: ReadonlyDeep<Survey>): Promise<void> {
    await this.client.chat.postMessage(
      NewSurveyMessage({ survey, mode: "reminder" }).buildToObject()
    );
  }

  async updateSurveyMessageOnDelete(
    survey: ReadonlyDeep<Survey>
  ): Promise<void> {
    await this.client.chat.update({
      ...NewSurveyMessage({ survey, mode: "delete" }).buildToObject(),
      channel: survey.channelId!,
      ts: survey.startMessageTS,
    });
  }

  async sendDiscountedSubscriptionOffer(
    workspaceId: string,
    memberId: string,
    regularPrice: string,
    discountedPrice: string
  ): Promise<{ ts: string | undefined; channel: string | undefined }> {
    const response = await this.client.chat.postMessage(
      DiscountedSubscriptionOffer({
        adminSiteUrl: this.adminSiteUrl,
        memberId,
        regularPrice,
        discountedPrice,
        workspaceId,
      }).buildToObject()
    );

    assert(
      response.channel,
      "Expected 'channel' to exist at chat.postMessage response"
    );
    assert(response.ts, "Expected 'ts' to exist at chat.postMessage response");

    return {
      ts: response.ts,
      channel: response.channel,
    };
  }

  async openTrialExpiredModal({
    triggerId,
    members,
    me,
    trialExpired,
  }: RenderTrialExpiredModalOptions): Promise<void> {
    await this.client.views.open({
      view: TrialExpiredModal({
        members,
        me,
        trialExpired,
        adminSiteUrl: this.adminSiteUrl,
      }),
      trigger_id: triggerId,
      notify_on_close: true,
    });
  }

  async getChannels(workspaceId: string): Promise<Channel[]> {
    assert(
      this.scopes,
      "You should have passed 'scopes' to SlackAdapterImpl constructor when calling getChannels method"
    );

    let cursor: string | undefined;
    const channels: SlackChannel[] = [];

    do {
      await SlackAdapterImpl.rateLimiters["conversations.list"].wait(
        workspaceId
      );
      const response = await this.client.conversations.list({
        team_id: workspaceId,
        cursor,
        limit: 500,
        exclude_archived: true,
        types: this.scopes.includes("groups:read")
          ? "public_channel,private_channel"
          : "public_channel",
      });

      if (response.channels) {
        channels.push(...response.channels);
      }
      cursor = response.response_metadata?.next_cursor;
    } while (cursor);

    return channels.map((channel) => {
      const id = channel.id;
      const name = channel.name;

      assert(id, "Expected id to exist at conversations.list response");
      assert(name, "Expected name to exist at conversations.list response");

      return {
        id,
        name,
        membersCount: channel.num_members,
        is_general: channel.is_general,
      };
    });
  }

  async getChannelMessages(channel: string): Promise<{ ts: string }[]> {
    const response = await this.client.conversations.history({ channel });

    assert(
      response.messages,
      "Expected conversations.history response to have 'messages'"
    );

    return response.messages.map((message) => {
      assert(
        message.ts,
        "Expected message 'ts' to exist at conversations.history response"
      );

      return {
        ts: message.ts,
      };
    });
  }

  async notifyAboutReceivingAccruals(
    memberId: string,
    timeOffPolicyTypes: ReadonlyDeep<TimeOffPolicyType>[],
    context: GlobalContext
  ): Promise<void> {
    await this.client.chat.postMessage(
      NotifyAboutReceivingAccruals({
        memberId,
        timeOffPolicyTypes,
        context,
      }).buildToObject()
    );
  }

  private async getTeamProfile(workspaceId: string): Promise<TeamProfile> {
    const { profile } = await this.client.team.profile.get({
      team_id: workspaceId,
    });

    assert(profile, "Expected profile");

    const sections = profile.sections ?? [];
    const fields = profile.fields ?? [];

    const defaultManagerFieldId = fields.find(
      (field) => field.field_name === "managerId" && field.options?.is_protected
    )?.id;

    const defaultDepartmentFieldId = fields.find(
      (field) =>
        field.field_name === "department" && field.options?.is_protected
    )?.id;

    const defaultTeamFieldId = fields.find(
      (field) => field.field_name === "division" && field.options?.is_protected
    )?.id;

    const defaultPhoneFieldId = fields.find(
      (field) => field.field_name === "Alternate Phone"
    )?.id;

    const defaultTitleFieldId = fields.find(
      (field) => field.field_name === "title" && field.options?.is_protected
    )?.id;

    const defaultCountryFieldId = fields.find(
      (field) => field.field_name === "country" && field.options?.is_protected
    )?.id;

    return {
      fields: fields
        .map((field): CustomField | null => {
          assert(field.id, "Expected field.id to exist");
          assert(field.label, "Expected field.label to exist");
          assert(
            typeof field.ordering === "number",
            "Expected field.ordering to exist"
          );
          assert(field.section_id, "Expected field.section_id to exist");
          assert(field.type, "Expected field.type to exist");

          if (
            field.type === "text" ||
            field.type === "options_list" ||
            field.type === "date" ||
            field.type === "link" ||
            field.type === "user"
          ) {
            const hidden =
              (field.is_hidden ?? false) ||
              sections.some(
                (section) =>
                  section.is_hidden && section.id === field.section_id
              );

            return {
              id: field.id,
              visible: !hidden,
              label: field.label,
              hint: field.hint,
              isEditableOnSlackOnly:
                field.id === defaultManagerFieldId
                  ? false
                  : (field.permissions?.ui ?? false) ||
                    (field.permissions?.scim ?? false),
              type: field.type,
              order: field.ordering,
              isSlackDefaultField: field.options?.is_protected ?? false,
              sectionId: field.section_id,
            };
          }

          return null;
        })
        .filter((field): field is CustomField => field !== null),
      sections: sections.map((section) => ({
        id: section.id!,
        label: section.label!,
        order: section.order!,
        sectionType: section.section_type,
        isHidden: section.is_hidden,
      })),
      defaultManagerFieldId: defaultManagerFieldId ?? null,
      defaultDepartmentFieldId: defaultDepartmentFieldId ?? null,
      defaultTeamFieldId: defaultTeamFieldId ?? null,
      defaultPhoneFieldId: defaultPhoneFieldId ?? null,
      defaultTitleFieldId: defaultTitleFieldId ?? null,
      defaultCountryFieldId: defaultCountryFieldId ?? null,
    };
  }

  private async getRawMembers(workspaceId: string): Promise<RawMember[]> {
    let cursor: string | undefined;
    const members: RawMember[] = [];

    do {
      await SlackAdapterImpl.rateLimiters["users.list"].wait(workspaceId);
      const response = await this.client.users.list({
        team_id: workspaceId,
        cursor,
        limit: 200,
        exclude_archived: true,
      });

      if (response.members) {
        members.push(...response.members);
      }
      cursor = response.response_metadata?.next_cursor;
    } while (cursor);

    return members.filter(
      (member) =>
        !member.is_bot &&
        !member.deleted &&
        member.name !== "slackbot" &&
        !member.is_restricted &&
        !member.is_ultra_restricted
    );
  }

  private async getPeopleFieldsConfiguration(
    workspaceId: string,
    memberId: string,
    members: Member[],
    allFields: TeamProfile["fields"],
    peopleFields: TeamProfile["fields"],
    policy: Policy,
    userToken: string
  ): Promise<TeamProfile["fields"]> {
    const { profile: userProfile } = await this.client.users.profile.get({
      user: memberId,
    });

    assert(userProfile, "Expected profile");

    const currentUserCustomFields =
      userProfile.fields as UpdateMember["customFields"];
    const possibleValuesForPeopleFields = members
      .filter((m) => m.id !== memberId)
      .map((m) => m.id);

    if (possibleValuesForPeopleFields.length === 0) {
      return allFields.map((f) => {
        const fieldId = f.id;

        assert(fieldId, "Field not found");

        if (peopleFields.some((field) => field.id === fieldId)) {
          return {
            ...f,
            isEditableOnSlackOnly: false,
            permissions: {
              ui: false,
            },
          };
        }

        return f;
      });
    }

    const testValues = peopleFields.reduce<
      Record<string, { value: string | null }>
    >((acc, current) => {
      assert(current.id, "Field not found");
      let testValue: string | null = null;

      if (currentUserCustomFields && current.id in currentUserCustomFields) {
        const currentFieldValue =
          currentUserCustomFields[current.id]!.value?.split(",");

        assert(
          currentFieldValue && currentFieldValue.length > 0,
          "Field value not found"
        );

        const possibleTestValue = difference(
          possibleValuesForPeopleFields,
          currentFieldValue
        );
        const randomValue =
          possibleTestValue[
            Math.floor(Math.random() * possibleTestValue.length)
          ];

        testValue = randomValue!;
      } else {
        testValue =
          possibleValuesForPeopleFields[
            Math.floor(Math.random() * possibleValuesForPeopleFields.length)
          ]!;
      }

      const result = {
        ...acc,
        [current.id]: { value: testValue },
      };

      return result;
    }, {});

    const userTestProfile = await this.updateMember(
      workspaceId,
      {
        memberId,
        customFields: testValues,
      },
      policy,
      userToken,
      { throwIfHaventChangedFields: false }
    );

    assert(userTestProfile?.fields, "Test user fields not found");

    const { fields: testProfileFields } = userTestProfile;

    const updatedFields = allFields.map((f) => {
      const fieldId = f.id;

      assert(fieldId, "Field not found");

      if (peopleFields.some((field) => field.id === fieldId)) {
        if (fieldId in testProfileFields) {
          const ui =
            testValues[fieldId].value !== testProfileFields[fieldId].value;

          return {
            ...f,
            isEditableOnSlackOnly: ui,
            permissions: {
              ui,
            },
          };
        }

        return {
          ...f,
          isEditableOnSlackOnly: true,
          permissions: {
            ui: true,
          },
        };
      }

      return f;
    });

    // * Turn back initial user custom fields
    const redundantFieldsIds = difference(
      Object.keys(testProfileFields),
      Object.keys(currentUserCustomFields ?? {})
    );
    const fieldsToCleanUp = redundantFieldsIds.reduce<
      UpdateMember["customFields"]
    >((acc, fieldId) => {
      return { ...acc, [fieldId]: { value: null } };
    }, {});

    await this.updateMember(
      workspaceId,
      {
        memberId,
        customFields: {
          ...currentUserCustomFields,
          ...fieldsToCleanUp,
        },
      },
      policy,
      userToken,
      { throwIfHaventChangedFields: false }
    );

    return updatedFields;
  }
}

interface RawMember {
  id?: string;
  name?: string;
  is_bot?: boolean;
  is_restricted?: boolean;
  is_ultra_restricted?: boolean;
  profile?: {
    title?: string;
    phone?: string;
    email?: string;
    image_original?: string;
    image_512?: string;
    image_72?: string;
    display_name?: string;
    status_text?: string;
    status_emoji?: string;
    status_expiration?: number;
  };
  real_name?: string;
  is_admin?: boolean;
  is_owner?: boolean;
  is_primary_owner?: boolean;
  updated?: number;
  deleted?: boolean;
  tz?: string;
  tz_offset?: number;
}

const SLACK_INACTIVITY_DURATION_IN_MILLISECONDS_TO_BECOME_NON_BILLABLE =
  28 * 24 * 60 * 60 * 1000; // 28 days, see: https://slack.com/intl/en-gb/help/articles/218915077-Slacks-Fair-Billing-Policy

export function parseMember(member: RawMember): SlackMember {
  assert(member.id, `Expected member.id to exist`);
  assert(
    member.name,
    `Expected member.name to exist (member id: "${member.id}")`
  );
  assert(
    member.profile,
    `Expected member.profile to exist (member id: "${member.id}")`
  );

  return {
    id: member.id,
    isSlackWorkspaceAdmin: Boolean(member.is_admin),
    isSlackWorkspaceOwner: Boolean(member.is_owner),
    isSlackWorkspacePrimaryOwner: Boolean(member.is_primary_owner),
    name: member.name,
    photoUrl: member.profile.image_original,
    photo512Url: member.profile.image_512,
    photo72Url: member.profile.image_72,
    realName: member.real_name,
    displayName: member.profile.display_name,
    title: member.profile.title,
    phone: member.profile.phone,
    email: member.profile.email,
    tz: member.tz,
    tzOffset: member.tz_offset,
    status:
      member.profile.status_text === "" && member.profile.status_emoji === ""
        ? null
        : {
            text: member.profile.status_text ?? "",
            emoji: member.profile.status_emoji ?? "",
            expiresAt: member.profile.status_expiration
              ? new Date(member.profile.status_expiration * 1000)
              : undefined,
          },
    isSlackBillable: member.updated
      ? Date.now() - member.updated * 1000 <=
        SLACK_INACTIVITY_DURATION_IN_MILLISECONDS_TO_BECOME_NON_BILLABLE
      : undefined,
  };
}

export const parseCustomFields = (
  obj: Record<string, Field | undefined> | null | undefined
): Record<string, string> => {
  const flattened: Record<string, string> = {};

  if (!obj || Object.keys(obj).length === 0) {
    return {};
  }

  Object.keys(obj).forEach((key) => {
    const value = obj[key]?.value;

    if (typeof value === "object" && !Array.isArray(value)) {
      Object.assign(flattened, parseCustomFields(value));
    } else {
      flattened[key] = value!;
    }
  });

  return flattened;
};

function validateCustomFieldsUpdate(
  expectedCustomFields: UpdateMemberCustomFields,
  actualCustomFields: Record<string, string>
): void {
  for (const [id, value] of Object.entries(expectedCustomFields)) {
    if (value) {
      if (
        (value.value === null && id in actualCustomFields) ||
        (value.value && actualCustomFields[id] !== value.value)
      ) {
        throw new CustomFieldHasntBeenUpdatedError(
          expectedCustomFields,
          actualCustomFields
        );
      }
    }
  }
}

function getMembersFromTeam(
  root: ReadonlyDeep<Department | Position | RootNode>,
  workspace: ReadonlyDeep<Workspace>,
  teamId: string,
  managerId?: string
): ReadonlyDeep<UIMember[]> {
  if (!root.subordinates.length) return [];

  const newManagerPosition =
    root.type === "position" && root.memberId
      ? getUIMemberById(workspace, root.memberId).id
      : managerId;

  return root.subordinates.flatMap((node) => {
    if (node.type === "position") {
      if (
        node.memberId &&
        node.teamIds.length &&
        node.teamIds.includes(teamId)
      ) {
        return [
          getUIMemberById(workspace, node.memberId),

          ...getMembersFromTeam(node, workspace, teamId, newManagerPosition),
        ];
      }
    }

    return getMembersFromTeam(node, workspace, teamId, newManagerPosition);
  });
}

function getMembersFromDepartment(
  rootNode: ReadonlyDeep<Department | Position>,
  workspace: ReadonlyDeep<Workspace>
): ReadonlyDeep<UIMember[]> {
  if (!rootNode.subordinates.length) return [];

  const departmentManager = getNodeManager(workspace, rootNode);

  return rootNode.subordinates.flatMap((node) => {
    if (node.type === "position" && node.memberId) {
      const currentDepartmentManager =
        departmentManager?.position &&
        departmentManager.position.id === node.parentId
          ? departmentManager
          : getNodeManager(workspace, node);

      return (
        [
          {
            ...getUIMemberById(workspace, node.memberId),
            position: node,
            manager: currentDepartmentManager,
          },
        ] as ReadonlyDeep<UIMember[]>
      ).concat(getMembersFromDepartment(node, workspace));
    }

    return getMembersFromDepartment(node, workspace);
  });
}

export default SentrySpanMethods(SlackAdapterImpl);
