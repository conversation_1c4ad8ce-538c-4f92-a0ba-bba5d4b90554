/**
 * WARNING! It's a script for local development only. Production HTTP version of it
 * is located in organice-admin-site at route /api/slack/events
 */
import assert from "node:assert";

import {
  WorkspaceRepository,
  NoOpProductOwnerNotifier,
  ProductOwnerNotifier,
  SlackBotToken,
  ReadonlyDeep,
  SlackAdapter,
  SlackBotApplication,
} from "@organice/core/domain";
import { ActivityLog } from "@organice/core/domain/activityLog";
import {
  <PERSON>dosQueryHandler,
  KudosRepository,
} from "@organice/core/domain/kudos";
import LoggerImpl from "@organice/core/logger/LoggerImpl";
import PrismaActivityLog from "@organice/core/prisma/PrismaActivityLog";
import { prismaClient } from "@organice/core/prisma/PrismaClient";
import PrismaInstallationStore from "@organice/core/prisma/PrismaInstallationStore";
import PrismaKudosQueryHandler from "@organice/core/prisma/PrismaKudosQueryHandler";
import PrismaKudosRepository from "@organice/core/prisma/PrismaKudosRepository";
import PrismaWorkspaceRepository from "@organice/core/prisma/PrismaWorkspaceRepository";
import SlackWebhookProductOwnerNotifier from "@organice/core/slack/SlackWebhookProductOwnerNotifier";
import { SocketModeReceiver } from "@slack/bolt";
import { WebClient } from "@slack/web-api";

import PrismaSlackBotQueryHandler from "./prisma/PrismaSlackBotQueryHandler";
import SlackAdapterImpl from "./slack/SlackAdapterImpl";
import SlackAppManifest from "./slack/SlackAppManifest";
import SlackBot from "./slack/SlackBot";
import SlackBotQueryHandler from "./slack/SlackBotQueryHandler";

assert(
  process.env.ORGANICE_SLACK_APP_ID,
  "ORGANICE_SLACK_APP_ID env variable should be defined"
);
assert(
  process.env.ORGANICE_SLACK_APP_TOKEN,
  "ORGANICE_SLACK_APP_TOKEN env variable should be defined"
);
assert(
  process.env.ORGANICE_SLACK_CLIENT_ID,
  "ORGANICE_SLACK_CLIENT_ID env variable should be defined"
);
assert(
  process.env.ORGANICE_SLACK_CLIENT_SECRET,
  "ORGANICE_SLACK_CLIENT_SECRET env variable should be defined"
);
assert(
  process.env.ORGANICE_SLACK_SIGNING_SECRET,
  "ORGANICE_SLACK_SIGNING_SECRET env variable should be defined"
);
assert(
  process.env.KUDOS_SLACK_APP_ID,
  "KUDOS_SLACK_APP_ID env variable should be defined"
);
assert(
  process.env.KUDOS_SLACK_APP_TOKEN,
  "KUDOS_SLACK_APP_TOKEN env variable should be defined"
);
assert(
  process.env.KUDOS_SLACK_CLIENT_ID,
  "KUDOS_SLACK_CLIENT_ID env variable should be defined"
);
assert(
  process.env.KUDOS_SLACK_CLIENT_SECRET,
  "KUDOS_SLACK_CLIENT_SECRET env variable should be defined"
);
assert(
  process.env.KUDOS_SLACK_SIGNING_SECRET,
  "KUDOS_SLACK_SIGNING_SECRET env variable should be defined"
);
assert(
  process.env.SLACK_STATE_SECRET,
  "SLACK_STATE_SECRET env variable should be defined"
);
assert(
  process.env.SLACK_ADMIN_SITE_URL,
  "SLACK_ADMIN_SITE_URL env variable should be defined"
);

const adminSiteUrl = process.env.SLACK_ADMIN_SITE_URL;

function getRepository(): WorkspaceRepository {
  return new PrismaWorkspaceRepository(prismaClient);
}

function getProductOwnerNotifier(): ProductOwnerNotifier {
  return process.env.SLACK_INSTALLATION_HANDLER_WEBHOOK_URL
    ? new SlackWebhookProductOwnerNotifier(
        process.env.SLACK_INSTALLATION_HANDLER_WEBHOOK_URL
      )
    : new NoOpProductOwnerNotifier();
}

function getTime(): Date {
  return new Date();
}

function getActivityLog(): ActivityLog {
  return new PrismaActivityLog(prismaClient);
}

function getSlackBotQueryHandler(): SlackBotQueryHandler {
  return new PrismaSlackBotQueryHandler(prismaClient);
}

function getKudosQueryHandler(): KudosQueryHandler {
  return new PrismaKudosQueryHandler(prismaClient);
}

function getKudosRepository(): KudosRepository {
  return new PrismaKudosRepository(prismaClient);
}

function getSlackAdapter(
  slackBotToken: ReadonlyDeep<SlackBotToken>
): SlackAdapter {
  assert(
    process.env.ORGANICE_SLACK_APP_ID,
    "ORGANICE_SLACK_APP_ID env variable should be defined"
  );
  assert(
    process.env.KUDOS_SLACK_APP_ID,
    "KUDOS_SLACK_APP_ID env variable should be defined"
  );
  assert(
    process.env.SLACK_ADMIN_SITE_URL,
    "SLACK_ADMIN_SITE_URL env variable should be defined"
  );

  const slackAdapter = new SlackAdapterImpl({
    client: new WebClient(slackBotToken.token, {
      slackApiUrl: process.env.SLACK_API_URL
        ? process.env.SLACK_API_URL
        : undefined,
    }),
    adminSiteUrl,
    scopes: slackBotToken.scopes,
    queryHandler: getSlackBotQueryHandler(),
    appIds: {
      [SlackBotApplication.ORGANICE]: process.env.ORGANICE_SLACK_APP_ID,
      [SlackBotApplication.KUDOS]: process.env.KUDOS_SLACK_APP_ID,
    },
  });

  return slackAdapter;
}

function getSlackAdapterFromClient(client: WebClient): SlackAdapter {
  assert(
    process.env.ORGANICE_SLACK_APP_ID,
    "ORGANICE_SLACK_APP_ID env variable should be defined"
  );
  assert(
    process.env.KUDOS_SLACK_APP_ID,
    "KUDOS_SLACK_APP_ID env variable should be defined"
  );

  return new SlackAdapterImpl({
    client,
    adminSiteUrl,
    queryHandler: getSlackBotQueryHandler(),
    appIds: {
      [SlackBotApplication.ORGANICE]: process.env.ORGANICE_SLACK_APP_ID,
      [SlackBotApplication.KUDOS]: process.env.KUDOS_SLACK_APP_ID,
    },
  });
}

const logger = new LoggerImpl(
  {},
  {
    sentry: !!process.env.NEXT_PUBLIC_SENTRY_DSN_ADMIN_SITE,
    mixpanel: process.env.NEXT_PUBLIC_MIXPANEL_AUTH_TOKEN
      ? { token: process.env.NEXT_PUBLIC_MIXPANEL_AUTH_TOKEN }
      : undefined,
  }
);

for (const app of [SlackBotApplication.ORGANICE, SlackBotApplication.KUDOS]) {
  const appToken = {
    [SlackBotApplication.ORGANICE]: process.env.ORGANICE_SLACK_APP_TOKEN,
    [SlackBotApplication.KUDOS]: process.env.KUDOS_SLACK_APP_TOKEN,
  }[app];
  const appId = {
    [SlackBotApplication.ORGANICE]: process.env.ORGANICE_SLACK_APP_ID,
    [SlackBotApplication.KUDOS]: process.env.KUDOS_SLACK_APP_ID,
  }[app];
  const clientId = {
    [SlackBotApplication.ORGANICE]: process.env.ORGANICE_SLACK_CLIENT_ID,
    [SlackBotApplication.KUDOS]: process.env.KUDOS_SLACK_CLIENT_ID,
  }[app];
  const clientSecret = {
    [SlackBotApplication.ORGANICE]: process.env.ORGANICE_SLACK_CLIENT_SECRET,
    [SlackBotApplication.KUDOS]: process.env.KUDOS_SLACK_CLIENT_SECRET,
  }[app];
  const signingSecret = {
    [SlackBotApplication.ORGANICE]: process.env.ORGANICE_SLACK_SIGNING_SECRET,
    [SlackBotApplication.KUDOS]: process.env.KUDOS_SLACK_SIGNING_SECRET,
  }[app];
  const stateSecret = process.env.SLACK_STATE_SECRET;
  const slackApiUrl = process.env.SLACK_API_URL
    ? process.env.SLACK_API_URL
    : undefined;
  const port = {
    [SlackBotApplication.ORGANICE]: 3007,
    [SlackBotApplication.KUDOS]: 3008,
  }[app];

  const installationStore = new PrismaInstallationStore({
    app,
    appId,
    clientId,
    clientSecret,
    logger,
    prismaClient,
    getRepository,
    getSlackAdapter,
    getProductOwnerNotifier,
  });

  const receiver = new SocketModeReceiver({
    appToken,
    clientId,
    clientSecret,
    stateSecret,
    scopes: SlackAppManifest.botScopes,
    installerOptions: {
      userScopes: SlackAppManifest.userScopes,
      port,
    },
    installationStore,
  });

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const slackBot = new SlackBot({
    clientId,
    clientSecret,
    signingSecret,
    stateSecret,
    slackApiUrl,
    logger,
    adminSiteUrl,

    installationStore,
    receiver,
    getActivityLog,
    getRepository,
    getSlackAdapter: getSlackAdapterFromClient,
    getProductOwnerNotifier,
    getKudosQueryHandler,
    getSlackBotQueryHandler,
    getKudosRepository,
    getTime,
  });

  // eslint-disable-next-line no-console
  slackBot.start().catch(console.error);
}
