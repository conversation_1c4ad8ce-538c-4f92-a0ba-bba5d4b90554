/* eslint-disable @typescript-eslint/no-unused-vars */
import assert from "assert";
import { spawnSync, SpawnSyncReturns } from "child_process";

import * as digitalocean from "@pulumi/digitalocean";
import * as pulumi from "@pulumi/pulumi";

const dockerBuildRun = spawnSync("docker", ["build", "--help"], {
  encoding: "utf-8",
}) as unknown as SpawnSyncReturns<string>;

if (dockerBuildRun.status !== 0) {
  throw new Error(dockerBuildRun.stderr);
}

if (!dockerBuildRun.stdout.includes("buildx")) {
  throw new Error("Buildx must be enabled! Please run: docker buildx install");
}

const config = new pulumi.Config();

const STACKNAME = config.require("stack_name");
const DOMAIN = config.require("domain");
const BOTDOMAIN = config.require("bot-domain");

let postgresAdminSiteConnectionPoolString: pulumi.Output<string> | undefined;
let postgresWorkerConnectionPoolString: pulumi.Output<string> | undefined;
let postgresConnectionString: pulumi.Output<string> | undefined;

export const adminSiteUrl = pulumi.interpolate`${DOMAIN}`;
export const slackBotUrl = pulumi.interpolate`${BOTDOMAIN}`;

if (STACKNAME === "prod") {
  const postgres = new digitalocean.DatabaseCluster(
    `organice-${STACKNAME}`,
    {
      engine: "pg",
      name: `organice-${STACKNAME}`.slice(0, 32),
      nodeCount: 1,
      projectId: config.require("digitalocean_project_id"),
      region: "nyc1",
      size: digitalocean.DatabaseSlug.DB_2VPCU4GB,
      version: "16",
    },
    {
      ignoreChanges: ["size", "storageSizeMib"],
    }
  );

  const maxConnectionsCount = 22;
  const connectionsReserve = 4;
  const pool = new digitalocean.DatabaseConnectionPool(
    `organice-${STACKNAME}`,
    {
      clusterId: postgres.id,
      dbName: postgres.database,
      mode: "transaction",
      size: maxConnectionsCount - connectionsReserve,
      user: postgres.user,
    }
  );

  const username = config.require("digitalocean_postgres_username");
  const password = config.require("digitalocean_postgres_password");

  postgresConnectionString = pulumi.interpolate`postgresql://${username}:${password}@${postgres.host}:${postgres.port}/${postgres.database}?sslmode=require`;
  postgresAdminSiteConnectionPoolString = pulumi.interpolate`postgresql://${username}:${password}@${pool.host}:${pool.port}/${pool.name}?sslmode=require&pgbouncer=true&connection_limit=5`;
  postgresWorkerConnectionPoolString = pulumi.interpolate`postgresql://${username}:${password}@${pool.host}:${pool.port}/${pool.name}?sslmode=require&pgbouncer=true&connection_limit=10`;
}

const GIT_BRANCH = process.env.GIT_BRANCH;
const GITHUB_TOKEN = process.env.GITHUB_TOKEN;
const NONCE = Math.round(Number.MAX_SAFE_INTEGER * Math.random());

assert(GIT_BRANCH, "Expected GIT_BRANCH env variable to be defined");
assert(GITHUB_TOKEN, "Expected GITHUB_TOKEN env variable to be defined");

const app = new digitalocean.App(`organice-${STACKNAME}`, {
  projectId: config.require("digitalocean_project_id"),
  spec: {
    name: `organice-${STACKNAME}`.slice(0, 32),
    region: "nyc",
    domainNames: [
      {
        name: new URL(DOMAIN).hostname,
        zone: config.require("digitalocean_domain_zone"),
      },
      {
        name: new URL(BOTDOMAIN).hostname,
        zone: config.require("digitalocean_domain_zone"),
      },
    ],
    databases: postgresConnectionString
      ? []
      : [
          {
            engine: "PG",
            name: "organice-postgres",
          },
        ],
    ingress: {
      rules: [
        {
          match: {
            path: {
              prefix: "/slack",
            },
          },
          component: {
            name: "slack-bot",
            preservePathPrefix: true,
          },
        },
        {
          match: {
            path: {
              prefix: "/",
            },
          },
          component: {
            name: "admin-site",
            preservePathPrefix: true,
          },
        },
      ],
    },
    services: [
      {
        name: "admin-site",
        git: {
          repoCloneUrl: `https://${NONCE}:${GITHUB_TOKEN}@github.com/startupmillio/organice.git`,
          branch: GIT_BRANCH,
        },
        instanceCount: 1,
        instanceSizeSlug:
          STACKNAME === "prod" ? "apps-s-1vcpu-1gb" : "apps-s-1vcpu-0.5gb",
        dockerfilePath: "./organice-admin-site/Dockerfile",
        sourceDir: ".",
        httpPort: 80,
        alerts: [
          {
            rule: "CPU_UTILIZATION",
            operator: "GREATER_THAN",
            value: 86,
            window: "FIVE_MINUTES",
          },
          {
            rule: "MEM_UTILIZATION",
            operator: "GREATER_THAN",
            value: 96,
            window: "FIVE_MINUTES",
          },
        ],
        envs: [
          {
            key: "INTERCOM_SECRET_KEY",
            value: config.requireSecret("intercom_secret_key"),
          },
          {
            key: "POSTGRES_CONNECTION_URL",
            value:
              postgresAdminSiteConnectionPoolString ??
              `\${organice-postgres.DATABASE_URL}`,
            scope: "RUN_TIME",
          },
          {
            key: "SLACK_ADMIN_SITE_URL",
            value: DOMAIN,
          },
          {
            key: "ORGANICE_SLACK_APP_ID",
            value: config.require("organice_slack_app_id"),
          },
          {
            key: "ORGANICE_SLACK_CLIENT_ID",
            value: config.require("organice_slack_client_id"),
          },
          {
            key: "ORGANICE_SLACK_CLIENT_SECRET",
            value: config.requireSecret("organice_slack_client_secret"),
          },
          {
            key: "ORGANICE_SLACK_SIGNING_SECRET",
            value: config.requireSecret("organice_slack_signing_secret"),
          },
          {
            key: "KUDOS_SLACK_APP_ID",
            value: config.requireSecret("kudos_slack_app_id"),
          },
          {
            key: "KUDOS_SLACK_CLIENT_ID",
            value: config.require("kudos_slack_client_id"),
          },
          {
            key: "KUDOS_SLACK_CLIENT_SECRET",
            value: config.requireSecret("kudos_slack_client_secret"),
          },
          {
            key: "KUDOS_SLACK_SIGNING_SECRET",
            value: config.requireSecret("kudos_slack_signing_secret"),
          },
          {
            key: "NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY",
            value: config.requireSecret("next_public_stripe_publishable_key"),
          },
          {
            key: "STRIPE_SECRET_KEY",
            value: config.requireSecret("stripe_secret_key"),
          },
          {
            key: "STRIPE_WEBHOOK_SIGNING_SECRET",
            value:
              process.env.FORCE_STRIPE_WEBHOOK_SIGNING_SECRET ??
              config.requireSecret("stripe_webhook_signing_secret"),
          },
          {
            key: "STRIPE_PRODUCT_ID",
            value: config.requireSecret("stripe_product_id"),
          },
          {
            key: "SLACK_STATE_SECRET",
            value: config.requireSecret("slack_state_secret"),
          },
          {
            key: "SLACK_API_URL",
            value: process.env.FORCE_SLACK_TEST_SERVER
              ? `${BOTDOMAIN}/api`
              : "",
          },
          {
            key: "SLACK_AUTHORIZATION_URL",
            value: process.env.FORCE_SLACK_TEST_SERVER
              ? `${BOTDOMAIN}/authorize`
              : "",
          },
          {
            key: "NOTIFICATION_PERIOD_IN_MINUTES",
            value: process.env.FORCE_NOTIFICATION_PERIOD_IN_MINUTES ?? "",
          },
          {
            key: "COOKIE_ENCRYPTION_KEY",
            value: config.requireSecret("cookie_encryption_key"),
          },
          {
            key: "TRIAL_DURATION_IN_MINUTES",
            value: process.env.FORCE_TRIAL_DURATION_IN_MINUTES ?? "",
          },
          {
            key: "PRICING_MESSAGE_DELAY_IN_MINUTES",
            value: process.env.FORCE_PRICING_MESSAGE_DELAY_IN_MINUTES ?? "",
          },
          {
            key: "MASK_GRAPHQL_ERROR",
            value: config.getSecret("mask_graphql_error") ?? "",
          },
          {
            key: "NEXT_PUBLIC_SENTRY_ENVIRONMENT",
            value: config.require("sentry_environment"),
          },
          {
            key: "SENTRY_URL",
            value: config.require("sentry_url"),
          },
          {
            key: "SENTRY_ORG",
            value: config.require("sentry_org"),
          },
          {
            key: "SENTRY_PROJECT",
            value: config.require("sentry_project_admin_site"),
          },
          {
            key: "SENTRY_AUTH_TOKEN",
            value: config.requireSecret("sentry_auth_token"),
          },
          {
            key: "NEXT_PUBLIC_SENTRY_DSN_ADMIN_SITE",
            value: config.requireSecret("sentry_dsn_admin_site"),
          },
          {
            key: "SLACK_INSTALLATION_HANDLER_WEBHOOK_URL",
            value:
              config.getSecret("slack_installation_handler_webhook_url") ?? "",
          },
          {
            key: "CHATGPT_API_KEY",
            value: config.requireSecret("chatgpt_api_key"),
          },
          {
            key: "BIRTHDAY_GENERATOR_API_TOKEN",
            value: config.getSecret("birthday_generator_api_token") ?? "",
          },
          {
            key: "NEXT_PUBLIC_INTERCOM_ONBOARDING_FOLLOW_UP_DELAY_IN_MINUTES",
            value:
              process.env
                .FORCE_INTERCOM_ONBOARDING_FOLLOW_UP_DELAY_IN_MINUTES ?? "",
          },
          {
            key: "NEXT_PUBLIC_MIXPANEL_AUTH_TOKEN",
            value: config.getSecret("next_public_mixpanel_auth_token") ?? "",
          },
          {
            key: "NEXT_PUBLIC_INTERCOM_APP_ID",
            value: config.getSecret("next_public_intercom_app_id") ?? "",
          },
          {
            key: "NEXT_PUBLIC_LOGROCKET_APP_ID",
            value: config.getSecret("next_public_logrocket_app_id") ?? "",
          },
        ],
      },
      {
        name: "slack-bot",
        git: {
          repoCloneUrl: `https://${NONCE}:${GITHUB_TOKEN}@github.com/startupmillio/organice.git`,
          branch: GIT_BRANCH,
        },
        instanceCount: 1,
        instanceSizeSlug:
          STACKNAME === "prod" ? "apps-s-1vcpu-1gb" : "apps-s-1vcpu-0.5gb",
        dockerfilePath: "./organice-admin-site/Dockerfile",
        sourceDir: ".",
        httpPort: 80,
        alerts: [
          {
            rule: "CPU_UTILIZATION",
            operator: "GREATER_THAN",
            value: 86,
            window: "FIVE_MINUTES",
          },
          {
            rule: "MEM_UTILIZATION",
            operator: "GREATER_THAN",
            value: 96,
            window: "FIVE_MINUTES",
          },
        ],
        envs: [
          {
            key: "INTERCOM_SECRET_KEY",
            value: config.requireSecret("intercom_secret_key"),
          },
          {
            key: "POSTGRES_CONNECTION_URL",
            value:
              postgresAdminSiteConnectionPoolString ??
              `\${organice-postgres.DATABASE_URL}`,
            scope: "RUN_TIME",
          },
          {
            key: "SLACK_ADMIN_SITE_URL",
            value: DOMAIN,
          },
          {
            key: "ORGANICE_SLACK_APP_ID",
            value: config.require("organice_slack_app_id"),
          },
          {
            key: "ORGANICE_SLACK_CLIENT_ID",
            value: config.require("organice_slack_client_id"),
          },
          {
            key: "ORGANICE_SLACK_CLIENT_SECRET",
            value: config.requireSecret("organice_slack_client_secret"),
          },
          {
            key: "ORGANICE_SLACK_SIGNING_SECRET",
            value: config.requireSecret("organice_slack_signing_secret"),
          },
          {
            key: "KUDOS_SLACK_APP_ID",
            value: config.requireSecret("kudos_slack_app_id"),
          },
          {
            key: "KUDOS_SLACK_CLIENT_ID",
            value: config.require("kudos_slack_client_id"),
          },
          {
            key: "KUDOS_SLACK_CLIENT_SECRET",
            value: config.requireSecret("kudos_slack_client_secret"),
          },
          {
            key: "KUDOS_SLACK_SIGNING_SECRET",
            value: config.requireSecret("kudos_slack_signing_secret"),
          },
          {
            key: "NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY",
            value: config.requireSecret("next_public_stripe_publishable_key"),
          },
          {
            key: "STRIPE_SECRET_KEY",
            value: config.requireSecret("stripe_secret_key"),
          },
          {
            key: "STRIPE_WEBHOOK_SIGNING_SECRET",
            value:
              process.env.FORCE_STRIPE_WEBHOOK_SIGNING_SECRET ??
              config.requireSecret("stripe_webhook_signing_secret"),
          },
          {
            key: "STRIPE_PRODUCT_ID",
            value: config.requireSecret("stripe_product_id"),
          },
          {
            key: "SLACK_STATE_SECRET",
            value: config.requireSecret("slack_state_secret"),
          },
          {
            key: "SLACK_API_URL",
            value: process.env.FORCE_SLACK_TEST_SERVER
              ? `${BOTDOMAIN}/api`
              : "",
          },
          {
            key: "SLACK_AUTHORIZATION_URL",
            value: process.env.FORCE_SLACK_TEST_SERVER
              ? `${BOTDOMAIN}/authorize`
              : "",
          },
          {
            key: "NOTIFICATION_PERIOD_IN_MINUTES",
            value: process.env.FORCE_NOTIFICATION_PERIOD_IN_MINUTES ?? "",
          },
          {
            key: "COOKIE_ENCRYPTION_KEY",
            value: config.requireSecret("cookie_encryption_key"),
          },
          {
            key: "TRIAL_DURATION_IN_MINUTES",
            value: process.env.FORCE_TRIAL_DURATION_IN_MINUTES ?? "",
          },
          {
            key: "PRICING_MESSAGE_DELAY_IN_MINUTES",
            value: process.env.FORCE_PRICING_MESSAGE_DELAY_IN_MINUTES ?? "",
          },
          {
            key: "MASK_GRAPHQL_ERROR",
            value: config.getSecret("mask_graphql_error") ?? "",
          },
          {
            key: "NEXT_PUBLIC_SENTRY_ENVIRONMENT",
            value: config.require("sentry_environment"),
          },
          {
            key: "SENTRY_URL",
            value: config.require("sentry_url"),
          },
          {
            key: "SENTRY_ORG",
            value: config.require("sentry_org"),
          },
          {
            key: "SENTRY_PROJECT",
            value: config.require("sentry_project_admin_site"),
          },
          {
            key: "SENTRY_AUTH_TOKEN",
            value: config.requireSecret("sentry_auth_token"),
          },
          {
            key: "NEXT_PUBLIC_SENTRY_DSN_ADMIN_SITE",
            value: config.requireSecret("sentry_dsn_admin_site"),
          },
          {
            key: "SLACK_INSTALLATION_HANDLER_WEBHOOK_URL",
            value:
              config.getSecret("slack_installation_handler_webhook_url") ?? "",
          },
          {
            key: "CHATGPT_API_KEY",
            value: config.requireSecret("chatgpt_api_key"),
          },
          {
            key: "BIRTHDAY_GENERATOR_API_TOKEN",
            value: config.getSecret("birthday_generator_api_token") ?? "",
          },
          {
            key: "NEXT_PUBLIC_INTERCOM_ONBOARDING_FOLLOW_UP_DELAY_IN_MINUTES",
            value:
              process.env
                .FORCE_INTERCOM_ONBOARDING_FOLLOW_UP_DELAY_IN_MINUTES ?? "",
          },
          {
            key: "NEXT_PUBLIC_MIXPANEL_AUTH_TOKEN",
            value: config.getSecret("next_public_mixpanel_auth_token") ?? "",
          },
          {
            key: "NEXT_PUBLIC_INTERCOM_APP_ID",
            value: config.getSecret("next_public_intercom_app_id") ?? "",
          },
          {
            key: "NEXT_PUBLIC_LOGROCKET_APP_ID",
            value: config.getSecret("next_public_logrocket_app_id") ?? "",
          },
        ],
      },
    ],
    workers: [
      {
        name: "background-worker",
        git: {
          repoCloneUrl: `https://${NONCE}:${GITHUB_TOKEN}@github.com/startupmillio/organice.git`,
          branch: GIT_BRANCH,
        },
        instanceCount: 1,
        instanceSizeSlug:
          STACKNAME === "prod" ? "apps-s-1vcpu-1gb" : "apps-s-1vcpu-0.5gb",
        dockerfilePath: "./organice-background-worker/Dockerfile",
        sourceDir: ".",
        alerts: [
          {
            rule: "CPU_UTILIZATION",
            operator: "GREATER_THAN",
            value: 85,
            window: "FIVE_MINUTES",
          },
          {
            rule: "MEM_UTILIZATION",
            operator: "GREATER_THAN",
            value: 95,
            window: "FIVE_MINUTES",
          },
        ],
        envs: [
          {
            key: "POSTGRES_CONNECTION_URL",
            value:
              postgresWorkerConnectionPoolString ??
              `\${organice-postgres.DATABASE_URL}`,
            scope: "RUN_TIME",
          },
          {
            key: "SMTP_CONNECTION_URL",
            value: config.requireSecret("smtp_connection_url"),
          },
          {
            key: "SLACK_ADMIN_SITE_URL",
            value: DOMAIN,
          },
          {
            key: "CHATGPT_API_KEY",
            value: config.requireSecret("chatgpt_api_key"),
          },
          {
            key: "ORGANICE_SLACK_APP_ID",
            value: config.requireSecret("organice_slack_app_id"),
          },
          {
            key: "KUDOS_SLACK_APP_ID",
            value: config.requireSecret("kudos_slack_app_id"),
          },
          {
            key: "SLACK_TEST_SERVER",
            value: process.env.FORCE_SLACK_TEST_SERVER ?? "",
          },
          {
            key: "SLACK_API_URL",
            value: process.env.FORCE_SLACK_TEST_SERVER
              ? `${BOTDOMAIN}/api`
              : "",
          },
          {
            key: "NOTIFICATION_PERIOD_IN_MINUTES",
            value: process.env.FORCE_NOTIFICATION_PERIOD_IN_MINUTES ?? "",
          },
          {
            key: "TRIAL_DURATION_IN_MINUTES",
            value: process.env.FORCE_TRIAL_DURATION_IN_MINUTES ?? "",
          },
          {
            key: "NEXT_PUBLIC_SENTRY_ENVIRONMENT",
            value: config.require("sentry_environment"),
          },
          {
            key: "SENTRY_DSN_BACKGROUND_WORKER",
            value: config.requireSecret("sentry_dsn_background_worker"),
          },
          {
            key: "MIXPANEL_AUTH_TOKEN",
            value: config.getSecret("next_public_mixpanel_auth_token") ?? "",
          },
          {
            key: "STRIPE_SECRET_KEY",
            value: config.requireSecret("stripe_secret_key"),
          },
          {
            key: "STRIPE_WEBHOOK_SIGNING_SECRET",
            value:
              process.env.FORCE_STRIPE_WEBHOOK_SIGNING_SECRET ??
              config.requireSecret("stripe_webhook_signing_secret"),
          },
          {
            key: "STRIPE_PRODUCT_ID",
            value: config.requireSecret("stripe_product_id"),
          },
        ],
      },
    ],
    jobs: [
      {
        kind: "PRE_DEPLOY",
        name: "migrate-postgres",
        git: {
          repoCloneUrl: `https://${NONCE}:${GITHUB_TOKEN}@github.com/startupmillio/organice.git`,
          branch: GIT_BRANCH,
        },
        dockerfilePath: "./organice-core/Dockerfile.migrate-postgres",
        sourceDir: ".",
        envs: [
          {
            key: "POSTGRES_CONNECTION_URL",
            value:
              // It's not a mistake that we use a direct connection here instead of pool connection.
              // Prisma cannot run migrations using pooled connections, see:
              // https://www.prisma.io/docs/orm/prisma-client/setup-and-configuration/databases-connections/pgbouncer#prisma-migrate-and-pgbouncer-workaround
              postgresConnectionString ?? `\${organice-postgres.DATABASE_URL}`,
          },
        ],
      },
    ],
  },
});
