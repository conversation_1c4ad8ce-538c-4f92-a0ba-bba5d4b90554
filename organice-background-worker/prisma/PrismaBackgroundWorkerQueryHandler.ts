import { PrismaClient } from "@prisma/client";

import {
  BackgroundWorkerWorkspaceMeta,
  BackgroundWorkerQueryHandler,
} from "../domain/background-worker";

class PrismaBackgroundWorkerQueryHandler
  implements BackgroundWorkerQueryHandler
{
  private updatedAtCache = new Map<string, Date | null>();

  constructor(private client: PrismaClient) {}

  async getBackgroundWorkerWorkspacesMeta(): Promise<
    BackgroundWorkerWorkspaceMeta[]
  > {
    const rawWorkspaces = await this.client.workspace.findMany({
      select: {
        id: true,
        onboarding: true,
        updatedAt: true,
        pricing: {
          select: {
            status: true,
          },
        },
      },
      where: {
        wasDeleted: {
          equals: false,
        },
      },
    });

    for (const rawWorkspace of rawWorkspaces) {
      this.updatedAtCache.set(rawWorkspace.id, rawWorkspace.updatedAt);
    }

    return rawWorkspaces
      .filter((rawWorkspace) => {
        const onboarding = JSON.parse(rawWorkspace.onboarding) as {
          completed: boolean;
        };

        return onboarding.completed;
      })
      .map(
        (rawWorkspace): BackgroundWorkerWorkspaceMeta => ({
          id: rawWorkspace.id,
          billing: {
            subscription: {
              ok: rawWorkspace.pricing?.status === "ok",
            },
          },
        })
      );
  }

  // eslint-disable-next-line @typescript-eslint/require-await
  async getWorkspaceUpdatedAt(workspaceId: string): Promise<Date | null> {
    return this.updatedAtCache.get(workspaceId) ?? null;
  }
}

export default PrismaBackgroundWorkerQueryHandler;
