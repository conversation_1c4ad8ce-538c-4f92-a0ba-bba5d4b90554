/* eslint-disable @typescript-eslint/unbound-method */
import { expect, test } from "@jest/globals";
import {
  AccrualsFrequency,
  Color,
  Logger,
  Member,
  ReadonlyDeep,
  replaceMember,
  SlackAdapter,
  TimeOffRequest,
  Workspace,
  YearStart,
  Holiday,
} from "@organice/core/domain";
import {
  ActivityLog,
  NoOpActivityLog,
} from "@organice/core/domain/activityLog";
import { countries } from "@organice/core/domain/data-completion";
import { newHoliday, deleteHoliday } from "@organice/core/domain/holidays";
import {
  approveTimeOffRequest,
  deleteTimeOffRequest,
  requestTimeOff,
  replaceTimeOffRequest,
} from "@organice/core/domain/time-offs";
import { createMember, createWorkspace } from "@organice/core/utils/testUtils";

import { changeSlackStatus as applyChangeSlackStatuses } from "./background-worker";

const initialRequester = createMember({
  overrides: {
    id: "U1",
    isAdmin: false,
    timeOffs: {
      VACATION: { balance: 30, nextResetAt: null },
    },
  },
});
const initialApprover = createMember({
  overrides: {
    id: "U2",
    isAdmin: false,
  },
});
const initialAdmin = createMember({
  overrides: {
    id: "U3",
    isAdmin: true,
    isSlackWorkspaceAdmin: true,
  },
});

const initialWorkspace = createWorkspace({
  overrides: {
    id: "T1",
    timeOffs: {
      requests: [],
      types: [
        {
          id: "VACATION",
          color: Color.Amber,
          label: "Vacation",
          emoji: "🌴",
          slackEmoji: ":palm_tree:",
        },
      ],
      isEnable: false,
      createDiscussionChannelWhenMultipleApprovers: false,
      changeSlackStatus: true,
      policies: [
        {
          id: "DEFAULT",
          title: "DEFAULT",
          isDefault: true,
          typePolicies: [
            {
              typeId: "VACATION",
              yearStart: YearStart.Calendar,
              onStartQuota: 0,
              rollOverToNextYear: false,
              accrualsQuota: 0,
              accuralsFrequency: AccrualsFrequency.Month,
              nextAccruals: new Date("2032-05-05"),
              maxCapacity: 100,
            },
          ],
          workDays: [1, 2, 3, 4, 5],
          includedWeekendDays: [],
          notifyAccruals: false,
        },
      ],
    },
    members: [initialRequester, initialApprover, initialAdmin],
    slackBotToken: {
      appId: "A1",
      botId: "UX",
      botMemberId: "UX",
      scopes: ["groups:write"],
      token: "xoxb-1",
    },
    slackMemberTokens: {
      [initialAdmin.id]: {
        memberId: initialAdmin.id,
        scopes: ["users.profile:write"],
        token: "xoxp-1",
      },
    },
  },
});

describe("Slack Status Changes", () => {
  let workspace: ReadonlyDeep<Workspace>;
  let slackAdapter: SlackAdapter;
  let activityLog: ActivityLog;
  let logger: Logger;
  let now: Date;

  beforeEach(() => {
    workspace = initialWorkspace;
    activityLog = new NoOpActivityLog();
    slackAdapter = {
      sendTimeOffRequest: jest
        .fn()
        .mockImplementation((_timeOff, channel: string) => ({
          channel,
          ts: "111.000",
        })),
      updateTimeOffRequest: jest.fn().mockResolvedValue(undefined),
      isDirectMessageChannel: (channelId: string) =>
        channelId.startsWith("D") || channelId.startsWith("U"),
      notifyRequesterAboutTimeOff: jest
        .fn()
        .mockResolvedValue({ ts: "222.000" }),
      notifyMemberMangerAboutTimeOffStatus: jest
        .fn()
        .mockResolvedValue({ ts: "333.000" }),
      notifyThatApprovedTimeOffWasDeleted: jest
        .fn()
        .mockResolvedValue(undefined),
      sendReminderAboutTimeOffRequest: jest
        .fn()
        .mockResolvedValue({ ts: "444.00", channel: "D2" }),
      postTimeOffChangeInChannel: jest
        .fn()
        .mockResolvedValue({ ts: "555.00", channel: "D3" }),
      notifyRequesterAboutTimeOffChange: jest
        .fn()
        .mockResolvedValue({ ts: "555.00", channel: "D3" }),
      postTimeOffInChannel: jest.fn().mockResolvedValue(undefined),
      archiveChannel: jest.fn().mockResolvedValue(undefined),
      createPrivateChannel: jest.fn().mockResolvedValue({ channel: "C1" }),
      updateMember: jest.fn().mockResolvedValue(undefined),
      deleteReminder: jest.fn(),
    } as unknown as SlackAdapter;
    logger = { info: () => {}, error: () => {} } as Logger;
    now = new Date("2024-06-17T00:00:00");
  });

  describe("when status depends on time off", () => {
    let requester: ReadonlyDeep<Member>;
    let approver: ReadonlyDeep<Member>;
    let timeOff: ReadonlyDeep<TimeOffRequest>;

    beforeEach(async () => {
      requester = initialRequester;
      approver = initialApprover;

      [workspace, timeOff] = await requestTimeOff(
        activityLog,
        slackAdapter,
        logger,
        now,
        workspace,
        requester.id,
        {
          type: "VACATION",
          approversIds: [approver.id],
          startDate: "2024-06-20",
          endDate: "2024-06-21",
        }
      );
    });

    describe("when the time off begins", () => {
      test("don't change the requester's status if time off hasn't begun", async () => {
        [workspace, timeOff] = await approveTimeOffRequest(
          workspace,
          activityLog,
          slackAdapter,
          logger,
          now,
          timeOff,
          approver
        );
        workspace = replaceTimeOffRequest(workspace, timeOff);
        now = new Date("2024-06-19T22:33:00");

        workspace = await applyChangeSlackStatuses(
          activityLog,
          slackAdapter,
          workspace,
          requester,
          now
        );

        expect(slackAdapter.updateMember).toHaveBeenCalledTimes(0);
      });

      test("don't change the requester's status if time off hasn't been approved", async () => {
        now = new Date("2024-06-20T00:00:00");
        workspace = await applyChangeSlackStatuses(
          activityLog,
          slackAdapter,
          workspace,
          requester,
          now
        );

        expect(slackAdapter.updateMember).toHaveBeenCalledTimes(0);
      });

      test("change the requester's status", async () => {
        [workspace, timeOff] = await approveTimeOffRequest(
          workspace,
          activityLog,
          slackAdapter,
          logger,
          now,
          timeOff,
          approver
        );
        workspace = replaceTimeOffRequest(workspace, timeOff);
        now = new Date("2024-06-20T00:00:00");

        workspace = await applyChangeSlackStatuses(
          activityLog,
          slackAdapter,
          workspace,
          requester,
          now
        );

        expect(slackAdapter.updateMember).toHaveBeenCalledTimes(1);
        expect(slackAdapter.updateMember).toHaveBeenCalledWith(
          workspace.id,
          {
            memberId: requester.id,
            status: {
              statusEmoji: ":palm_tree:",
              statusExpiration: new Date("2024-06-21T23:59:59"),
              statusText: "Vacation",
            },
          },
          expect.anything(),
          expect.anything()
        );
      });
    });

    describe("when the time off ends", () => {
      test("revert the requester's status", async () => {
        [workspace, timeOff] = await approveTimeOffRequest(
          workspace,
          activityLog,
          slackAdapter,
          logger,
          now,
          timeOff,
          approver
        );
        workspace = replaceTimeOffRequest(workspace, timeOff);
        requester = {
          ...requester,
          status: {
            text: "Chilling",
            emoji: ":relax:",
          },
        };
        workspace = replaceMember(workspace, requester);
        now = new Date("2024-06-20T00:00:00");

        workspace = await applyChangeSlackStatuses(
          activityLog,
          slackAdapter,
          workspace,
          requester,
          now
        );

        expect(slackAdapter.updateMember).toHaveBeenCalledTimes(1);

        now = new Date("2024-06-22T00:00:00");

        workspace = await applyChangeSlackStatuses(
          activityLog,
          slackAdapter,
          workspace,
          requester,
          now
        );

        expect(slackAdapter.updateMember).toHaveBeenCalledTimes(2);
        expect(slackAdapter.updateMember).toHaveBeenCalledWith(
          workspace.id,
          {
            memberId: requester.id,
            status: {
              statusEmoji: ":relax:",
              statusText: "Chilling",
            },
          },
          expect.anything(),
          expect.anything()
        );
      });
    });

    describe("when deleting a time off", () => {
      test("revert the requester's status", async () => {
        [workspace, timeOff] = await approveTimeOffRequest(
          workspace,
          activityLog,
          slackAdapter,
          logger,
          now,
          timeOff,
          approver
        );
        workspace = replaceTimeOffRequest(workspace, timeOff);
        requester = {
          ...requester,
          status: {
            text: "Chilling",
            emoji: ":relax:",
          },
        };
        workspace = replaceMember(workspace, requester);
        now = new Date("2024-06-20T00:00:00");

        workspace = await applyChangeSlackStatuses(
          activityLog,
          slackAdapter,
          workspace,
          requester,
          now
        );

        expect(slackAdapter.updateMember).toHaveBeenCalledTimes(1);

        now = new Date("2024-06-22T00:00:00");

        workspace = await deleteTimeOffRequest(
          activityLog,
          slackAdapter,
          logger,
          now,
          workspace,
          approver,
          timeOff
        );
        workspace = await applyChangeSlackStatuses(
          activityLog,
          slackAdapter,
          workspace,
          requester,
          now
        );

        expect(slackAdapter.updateMember).toHaveBeenCalledTimes(2);
        expect(slackAdapter.updateMember).toHaveBeenCalledWith(
          workspace.id,
          {
            memberId: requester.id,
            status: {
              statusEmoji: ":relax:",
              statusText: "Chilling",
            },
          },
          expect.anything(),
          expect.anything()
        );
      });
    });
  });

  describe("when status depends on holiday", () => {
    let memberWithHoliday: ReadonlyDeep<Member>;
    let holiday: ReadonlyDeep<Holiday>;

    beforeEach(() => {
      const country = countries[0].name;

      memberWithHoliday = workspace.members.find(
        (m) => m.id === initialRequester.id
      )!;

      memberWithHoliday = {
        ...memberWithHoliday,
        country,
      };

      workspace = replaceMember(workspace, memberWithHoliday);
      [workspace, holiday] = newHoliday(now, workspace, {
        country,
        name: "Happy holiday!",
        description: "description",
        startDate: "2024-06-20",
        endDate: "2024-06-21",
        isOfficial: true,
      });
    });
    describe("when the holiday begins", () => {
      test("don't change the requester's status if holiday hasn't begun", async () => {
        now = new Date("2024-06-19T22:33:00");

        workspace = await applyChangeSlackStatuses(
          activityLog,
          slackAdapter,
          workspace,
          memberWithHoliday,
          now
        );

        expect(slackAdapter.updateMember).toHaveBeenCalledTimes(0);
      });

      test("change the requester's status", async () => {
        now = new Date("2024-06-20T00:00:00");

        workspace = await applyChangeSlackStatuses(
          activityLog,
          slackAdapter,
          workspace,
          memberWithHoliday,
          now
        );

        expect(slackAdapter.updateMember).toHaveBeenCalledTimes(1);
        expect(slackAdapter.updateMember).toHaveBeenCalledWith(
          workspace.id,
          {
            memberId: memberWithHoliday.id,
            status: {
              statusEmoji: countries[0].emoji,
              statusExpiration: new Date("2024-06-21T23:59:59"),
              statusText: "Happy holiday!",
            },
          },
          expect.anything(),
          expect.anything()
        );
      });
    });

    describe("when the holiday ends", () => {
      test("revert the requester's status", async () => {
        memberWithHoliday = {
          ...memberWithHoliday,
          status: {
            text: "Chilling",
            emoji: ":relax:",
          },
        };
        workspace = replaceMember(workspace, memberWithHoliday);
        now = new Date("2024-06-20T00:00:00");
        workspace = await applyChangeSlackStatuses(
          activityLog,
          slackAdapter,
          workspace,
          memberWithHoliday,
          now
        );

        expect(slackAdapter.updateMember).toHaveBeenCalledTimes(1);

        now = new Date("2024-06-22T00:00:00");

        workspace = await applyChangeSlackStatuses(
          activityLog,
          slackAdapter,
          workspace,
          memberWithHoliday,
          now
        );

        expect(slackAdapter.updateMember).toHaveBeenCalledTimes(2);
        expect(slackAdapter.updateMember).toHaveBeenCalledWith(
          workspace.id,
          {
            memberId: memberWithHoliday.id,
            status: {
              statusEmoji: ":relax:",
              statusText: "Chilling",
            },
          },
          expect.anything(),
          expect.anything()
        );
      });
    });

    describe("when deleting a holiday", () => {
      test("revert the requester's status", async () => {
        memberWithHoliday = {
          ...memberWithHoliday,
          status: {
            text: "Chilling",
            emoji: ":relax:",
          },
        };
        workspace = replaceMember(workspace, memberWithHoliday);

        now = new Date("2024-06-20T00:00:00");

        workspace = await applyChangeSlackStatuses(
          activityLog,
          slackAdapter,
          workspace,
          memberWithHoliday,
          now
        );

        expect(slackAdapter.updateMember).toHaveBeenCalledTimes(1);

        now = new Date("2024-06-22T00:00:00");
        workspace = await deleteHoliday(slackAdapter, workspace, holiday.id);
        workspace = await applyChangeSlackStatuses(
          activityLog,
          slackAdapter,
          workspace,
          memberWithHoliday,
          now
        );

        expect(slackAdapter.updateMember).toHaveBeenCalledTimes(2);
        expect(slackAdapter.updateMember).toHaveBeenCalledWith(
          workspace.id,
          {
            memberId: memberWithHoliday.id,
            status: {
              statusEmoji: ":relax:",
              statusText: "Chilling",
            },
          },
          expect.anything(),
          expect.anything()
        );
      });
    });
  });

  describe("when status depends on multiple events", () => {
    let approver: ReadonlyDeep<Member>;
    let memberWithHoliday: ReadonlyDeep<Member>;
    let timeOff: ReadonlyDeep<TimeOffRequest>;
    let holiday: ReadonlyDeep<Holiday>;

    beforeEach(async () => {
      const country = countries[0].name;

      memberWithHoliday = workspace.members.find(
        (m) => m.id === initialRequester.id
      )!;

      memberWithHoliday = {
        ...memberWithHoliday,
        country,
      };

      workspace = replaceMember(workspace, memberWithHoliday);
      [workspace, holiday] = newHoliday(now, workspace, {
        country,
        name: "Happy holiday!",
        description: "description",
        startDate: "2024-06-20",
        endDate: "2024-06-23",
        isOfficial: true,
      });

      approver = initialApprover;

      [workspace, timeOff] = await requestTimeOff(
        activityLog,
        slackAdapter,
        logger,
        now,
        workspace,
        memberWithHoliday.id,
        {
          type: "VACATION",
          approversIds: [approver.id],
          startDate: "2024-06-21",
          endDate: "2024-06-24",
        }
      );
    });

    describe("when the holiday begins", () => {
      test("change the members's status if have an intersection with a time off", async () => {
        [workspace, timeOff] = await approveTimeOffRequest(
          workspace,
          activityLog,
          slackAdapter,
          logger,
          now,
          timeOff,
          approver
        );
        workspace = replaceTimeOffRequest(workspace, timeOff);
        now = new Date("2024-06-21T00:00:00");

        workspace = await applyChangeSlackStatuses(
          activityLog,
          slackAdapter,
          workspace,
          memberWithHoliday,
          now
        );

        expect(slackAdapter.updateMember).toHaveBeenCalledTimes(1);
        expect(slackAdapter.updateMember).toHaveBeenCalledWith(
          workspace.id,
          {
            memberId: memberWithHoliday.id,
            status: {
              statusEmoji: ":palm_tree:",
              statusExpiration: new Date("2024-06-24T23:59:59"),
              statusText: "Vacation",
            },
          },
          expect.anything(),
          expect.anything()
        );
      });

      test("change the members's status if a time off begins after", async () => {
        now = new Date("2024-06-20T00:00:00");

        workspace = await applyChangeSlackStatuses(
          activityLog,
          slackAdapter,
          workspace,
          memberWithHoliday,
          now
        );

        expect(slackAdapter.updateMember).toHaveBeenCalledTimes(1);
        expect(slackAdapter.updateMember).toHaveBeenLastCalledWith(
          workspace.id,
          {
            memberId: memberWithHoliday.id,
            status: {
              statusEmoji: "🇦🇫",
              statusExpiration: new Date("2024-06-23T23:59:59"),
              statusText: "Happy holiday!",
            },
          },
          expect.anything(),
          expect.anything()
        );

        memberWithHoliday = {
          ...memberWithHoliday,
          status: {
            emoji: "🇦🇫",
            expiresAt: new Date("2024-06-23T23:59:59"),
            text: "Happy holiday!",
          },
        };
        workspace = replaceMember(workspace, memberWithHoliday);
        [workspace, timeOff] = await approveTimeOffRequest(
          workspace,
          activityLog,
          slackAdapter,
          logger,
          now,
          timeOff,
          approver
        );
        workspace = replaceTimeOffRequest(workspace, timeOff);

        now = new Date("2024-06-22T00:00:00");
        workspace = await applyChangeSlackStatuses(
          activityLog,
          slackAdapter,
          workspace,
          memberWithHoliday,
          now
        );

        expect(slackAdapter.updateMember).toHaveBeenCalledTimes(2);
      });
    });

    describe("when the holiday ends", () => {
      test("don't change the members's status if have an intersection with a time off", async () => {
        now = new Date("2024-06-20T00:00:00");

        workspace = await applyChangeSlackStatuses(
          activityLog,
          slackAdapter,
          workspace,
          memberWithHoliday,
          now
        );

        expect(slackAdapter.updateMember).toHaveBeenCalledTimes(1);

        [workspace, timeOff] = await approveTimeOffRequest(
          workspace,
          activityLog,
          slackAdapter,
          logger,
          now,
          timeOff,
          approver
        );
        memberWithHoliday = {
          ...memberWithHoliday,
          status: {
            text: holiday.name,
            emoji: countries[0].emoji,
            expiresAt: holiday.endDate,
          },
        };
        workspace = replaceMember(workspace, memberWithHoliday);
        workspace = replaceTimeOffRequest(workspace, timeOff);
        now = new Date("2024-06-22T00:00:00");
        workspace = await applyChangeSlackStatuses(
          activityLog,
          slackAdapter,
          workspace,
          memberWithHoliday,
          now
        );

        expect(slackAdapter.updateMember).toHaveBeenCalledTimes(2);

        now = new Date("2024-06-24T00:00:00");
        workspace = await applyChangeSlackStatuses(
          activityLog,
          slackAdapter,
          workspace,
          memberWithHoliday,
          now
        );

        expect(slackAdapter.updateMember).toHaveBeenCalledTimes(2);
      });

      test("change the members's status when time off ends", async () => {
        now = new Date("2024-06-20T00:00:00");

        workspace = await applyChangeSlackStatuses(
          activityLog,
          slackAdapter,
          workspace,
          memberWithHoliday,
          now
        );

        [workspace, timeOff] = await approveTimeOffRequest(
          workspace,
          activityLog,
          slackAdapter,
          logger,
          now,
          timeOff,
          approver
        );
        memberWithHoliday = {
          ...memberWithHoliday,
          status: {
            text: holiday.name,
            emoji: countries[0].emoji,
            expiresAt: holiday.endDate,
          },
        };
        workspace = replaceMember(workspace, memberWithHoliday);
        workspace = replaceTimeOffRequest(workspace, timeOff);

        now = new Date("2024-06-22T00:00:00");

        workspace = await applyChangeSlackStatuses(
          activityLog,
          slackAdapter,
          workspace,
          memberWithHoliday,
          now
        );

        now = new Date("2024-06-24T00:00:00");

        workspace = await applyChangeSlackStatuses(
          activityLog,
          slackAdapter,
          workspace,
          memberWithHoliday,
          now
        );

        now = new Date("2024-06-25T00:00:00");
        workspace = await applyChangeSlackStatuses(
          activityLog,
          slackAdapter,
          workspace,
          memberWithHoliday,
          now
        );

        expect(slackAdapter.updateMember).toHaveBeenCalledTimes(3);
      });
    });

    describe("when the time off ends", () => {
      test("change the members's status if a holiday still lasts", async () => {
        now = new Date("2024-06-20T00:00:00");

        workspace = await applyChangeSlackStatuses(
          activityLog,
          slackAdapter,
          workspace,
          memberWithHoliday,
          now
        );
        [workspace, timeOff] = await requestTimeOff(
          activityLog,
          slackAdapter,
          logger,
          now,
          workspace,
          memberWithHoliday.id,
          {
            type: "VACATION",
            approversIds: [approver.id],
            startDate: "2024-06-21",
            endDate: "2024-06-21",
          }
        );

        [workspace, timeOff] = await approveTimeOffRequest(
          workspace,
          activityLog,
          slackAdapter,
          logger,
          now,
          timeOff,
          approver
        );

        memberWithHoliday = {
          ...memberWithHoliday,
          status: {
            text: holiday.name,
            emoji: countries[0].emoji,
            expiresAt: holiday.endDate,
          },
        };
        workspace = replaceMember(workspace, memberWithHoliday);
        workspace = replaceTimeOffRequest(workspace, timeOff);
        now = new Date("2024-06-21T00:00:00");
        workspace = await applyChangeSlackStatuses(
          activityLog,
          slackAdapter,
          workspace,
          memberWithHoliday,
          now
        );

        now = new Date("2024-06-22T00:00:00");
        workspace = await applyChangeSlackStatuses(
          activityLog,
          slackAdapter,
          workspace,
          memberWithHoliday,
          now
        );

        expect(slackAdapter.updateMember).toHaveBeenCalledTimes(3);
        expect(slackAdapter.updateMember).toHaveBeenCalledWith(
          workspace.id,
          {
            memberId: memberWithHoliday.id,
            status: {
              statusEmoji: countries[0].emoji,
              statusExpiration: holiday.endDate,
              statusText: holiday.name,
            },
          },
          expect.anything(),
          expect.anything()
        );
      });
    });
  });
});
