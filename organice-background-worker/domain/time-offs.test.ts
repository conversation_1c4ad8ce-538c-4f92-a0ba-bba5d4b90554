/* eslint-disable @typescript-eslint/unbound-method */
import { expect, test } from "@jest/globals";
import {
  <PERSON><PERSON>ly<PERSON>eep,
  SlackAdapter,
  Workspace,
  Color,
  Logger,
  TimeOffPolicy,
  AccrualsFrequency,
  Member,
  YearStart,
  replaceMember,
} from "@organice/core/domain";
import { ActivityLog } from "@organice/core/domain/activityLog";
import LoggerImpl from "@organice/core/logger/LoggerImpl";
import { createMember, createWorkspace } from "@organice/core/utils/testUtils";
import { addDays, addYears, subMinutes } from "date-fns";

import { refreshWorkspaceChannels } from "./background-worker";
import { runTimeOffPolicies } from "./time-offs";

const admin = createMember({
  overrides: {
    id: "U057C3HL0KH",
    isAdmin: true,
    isSlackWorkspaceAdmin: true,
    isSlackWorkspacePrimaryOwner: true,
  },
});

const initialMember = createMember({
  overrides: {
    id: "U058A2JMZFF",
  },
});

const members = [initialMember, admin];

const initialWorkspace = createWorkspace({
  overrides: {
    id: "T054D509V5J",
    members,
    timeOffs: {
      policies: [],
      requests: [],
      types: [
        {
          id: "VACATION",
          color: Color.Amber,
          label: "Vacation",
          emoji: "🌴",
          slackEmoji: ":palm_tree:",
        },
        {
          id: "SICK_DAYS",
          color: Color.Red,
          label: "Sick Days",
          emoji: "🤒",
          slackEmoji: ":face_with_thermometer:",
        },
        {
          id: "UNPAID_LEAVE",
          color: Color.Orange,
          label: "Unpaid Leave",
          emoji: "💸",
          slackEmoji: ":money_with_wings:",
        },
        {
          id: "BUSINESS_TRIP",
          color: Color.Blue,
          label: "Business Trip",
          emoji: "✈️",
          slackEmoji: ":airplane:",
        },
        {
          id: "WORK_FROM_HOME",
          color: Color.Emerald,
          emoji: "🏠",
          label: "Work from Home",
          slackEmoji: ":house:",
        },
      ],
      isEnable: false,
      createDiscussionChannelWhenMultipleApprovers: false,
      changeSlackStatus: true,
    },
    slackBotToken: {
      appId: "A1",
      scopes: ["channels:read"],
      token: "*********************************************************",
      botMemberId: "U05DXMTC7SN",
      botId: "B05DXJ8AJEP",
    },
    slackMemberTokens: {
      U057C3HL0KH: {
        token:
          "*******************************************************************************",
        scopes: [],
        memberId: "U0586V41P2N",
      },
    },
  },
});

describe("Time Offs", () => {
  let slackAdapter: SlackAdapter;
  let workspace: ReadonlyDeep<Workspace>;
  let logger: Logger;

  beforeEach(() => {
    slackAdapter = {
      updateMember: jest.fn(),
    } as unknown as SlackAdapter;
    workspace = initialWorkspace;
    logger = new LoggerImpl({}, {});
  });

  test("should send a reminder when time offs channel was archived", async () => {
    const time = new Date();

    workspace = setTimeOffsChannel(workspace, "C058DK7HT7V");
    workspace = turnOnTimeOffs(workspace);

    slackAdapter = {
      getChannels: jest.fn().mockResolvedValue(workspace.channels),
      sendTimeOffsChannelWasArchivedReminder: jest.fn().mockResolvedValue({}),
    } as unknown as SlackAdapter;

    workspace = await refreshWorkspaceChannels(
      slackAdapter,
      logger,
      workspace,
      time
    );
    expect(
      slackAdapter.sendTimeOffsChannelWasArchivedReminder
    ).toHaveBeenCalledTimes(0);

    slackAdapter = {
      getChannels: jest
        .fn()
        .mockResolvedValue(
          workspace.channels.filter((channel) => channel.id !== "C058DK7HT7V")
        ),
      sendTimeOffsChannelWasArchivedReminder: jest.fn().mockResolvedValue({}),
    } as unknown as SlackAdapter;

    workspace = await refreshWorkspaceChannels(
      slackAdapter,
      logger,
      workspace,
      time
    );
    expect(
      slackAdapter.sendTimeOffsChannelWasArchivedReminder
    ).toHaveBeenCalledTimes(1);
    expect(
      slackAdapter.sendTimeOffsChannelWasArchivedReminder
    ).toHaveBeenCalledWith(workspace.id, admin.id);
    expect(workspace.timeOffs.channelId).toBe(null);
  });

  test("should not send a reminder when time offs channel was archived if time offs are turned off", async () => {
    const time = new Date();

    workspace = setTimeOffsChannel(workspace, "C058DK7HT7V");
    workspace = turnOffTimeOffs(workspace);

    slackAdapter = {
      getChannels: jest
        .fn()
        .mockResolvedValue(
          workspace.channels.filter((channel) => channel.id !== "C058DK7HT7V")
        ),
      sendTimeOffsChannelWasArchivedReminder: jest.fn().mockResolvedValue({}),
    } as unknown as SlackAdapter;

    workspace = await refreshWorkspaceChannels(
      slackAdapter,
      logger,
      workspace,
      time
    );
    expect(
      slackAdapter.sendTimeOffsChannelWasArchivedReminder
    ).toHaveBeenCalledTimes(0);
    expect(workspace.timeOffs.channelId).toBe(null);
  });
});

describe("Time Off Policies", () => {
  let workspace: ReadonlyDeep<Workspace>;
  let member: ReadonlyDeep<Member>;
  let activityLog: ActivityLog;
  let slackAdapter: SlackAdapter;
  let logger: Logger;
  let now: Date;

  beforeEach(() => {
    workspace = initialWorkspace;
    member = initialMember;
    activityLog = {
      add: jest.fn(),
    };
    slackAdapter = {
      notifyAboutReceivingAccruals: jest.fn(),
    } as unknown as SlackAdapter;
    logger = new LoggerImpl({}, {});
    now = new Date("2024-01-01T00:00:00");
  });

  describe("when weekly accruals enabled", () => {
    test("sets next accrual to the closest Monday", async () => {
      const sunday = new Date("2024-05-19T00:00:00");
      const monday = new Date("2024-05-20T00:00:00");

      let defaultPolicy: ReadonlyDeep<TimeOffPolicy> = {
        id: "1",
        title: "default",
        isDefault: true,
        workDays: [1, 2, 3, 4, 5],
        includedWeekendDays: [],
        notifyAccruals: false,
        typePolicies: [
          {
            typeId: "VACATION",
            yearStart: YearStart.Calendar,
            maxCapacity: 0,
            accrualsQuota: 2,
            accuralsFrequency: AccrualsFrequency.Week,
            onStartQuota: 0,
            rollOverToNextYear: false,
            nextAccruals: null,
          },
        ],
      };

      workspace = addTimeOffPolicy(workspace, defaultPolicy);

      now = sunday;
      workspace = await runTimeOffPolicies(
        workspace,
        activityLog,
        slackAdapter,
        logger,
        now
      );
      member = workspace.members[0];
      defaultPolicy = workspace.timeOffs.policies[0];

      expect(member.timeOffs.VACATION?.balance ?? 0).toEqual(0);
      expect(defaultPolicy.typePolicies[0].nextAccruals).toEqual(monday);
    });

    test("increments balance each Monday", async () => {
      const nextMonday = new Date("2024-05-20T00:00:00");
      let defaultPolicy: ReadonlyDeep<TimeOffPolicy> = {
        id: "1",
        title: "default",
        isDefault: true,
        workDays: [1, 2, 3, 4, 5],
        includedWeekendDays: [],
        notifyAccruals: false,
        typePolicies: [
          {
            typeId: "VACATION",
            yearStart: YearStart.Calendar,
            maxCapacity: 0,
            accrualsQuota: 2,
            accuralsFrequency: AccrualsFrequency.Week,
            onStartQuota: 0,
            rollOverToNextYear: false,
            nextAccruals: nextMonday,
          },
        ],
      };

      workspace = addTimeOffPolicy(workspace, defaultPolicy);

      expect(member.timeOffs.VACATION?.balance ?? 0).toEqual(0);

      now = nextMonday;
      workspace = await runTimeOffPolicies(
        workspace,
        activityLog,
        slackAdapter,
        logger,
        now
      );
      member = workspace.members[0];
      defaultPolicy = workspace.timeOffs.policies[0];

      expect(member.timeOffs.VACATION?.balance ?? 0).toEqual(2);
      expect(defaultPolicy.typePolicies[0].nextAccruals).toEqual(
        addDays(nextMonday, 7)
      );
    });

    test("clamps balance if max capacity is set", async () => {
      const nextMonday = new Date("2024-05-20T00:00:00");
      const maxCapacity = 11;
      const defaultPolicy: ReadonlyDeep<TimeOffPolicy> = {
        id: "1",
        title: "default",
        isDefault: true,
        workDays: [1, 2, 3, 4, 5],
        includedWeekendDays: [],
        notifyAccruals: false,
        typePolicies: [
          {
            typeId: "VACATION",
            yearStart: YearStart.Calendar,
            maxCapacity,
            accrualsQuota: 2,
            accuralsFrequency: AccrualsFrequency.Week,
            onStartQuota: 0,
            rollOverToNextYear: false,
            nextAccruals: nextMonday,
          },
        ],
      };

      workspace = addTimeOffPolicy(workspace, defaultPolicy);
      member = {
        ...member,
        timeOffs: {
          VACATION: { balance: 10, nextResetAt: null },
        },
      };
      workspace = replaceMember(workspace, member);

      now = nextMonday;
      workspace = await runTimeOffPolicies(
        workspace,
        activityLog,
        slackAdapter,
        logger,
        now
      );
      member = workspace.members[0];

      expect(member.timeOffs.VACATION?.balance).toEqual(maxCapacity);
    });

    test("doesn't increment balance before accrual period end", async () => {
      const nextMonday = new Date("2024-05-20T00:00:00");
      let defaultPolicy: ReadonlyDeep<TimeOffPolicy> = {
        id: "1",
        title: "default",
        isDefault: true,
        workDays: [1, 2, 3, 4, 5],
        includedWeekendDays: [],
        notifyAccruals: false,
        typePolicies: [
          {
            typeId: "VACATION",
            yearStart: YearStart.Calendar,
            maxCapacity: 0,
            accrualsQuota: 2,
            accuralsFrequency: AccrualsFrequency.Week,
            onStartQuota: 0,
            rollOverToNextYear: false,
            nextAccruals: nextMonday,
          },
        ],
      };

      workspace = addTimeOffPolicy(workspace, defaultPolicy);

      expect(member.timeOffs.VACATION?.balance ?? 0).toEqual(0);

      now = subMinutes(nextMonday, 10);
      workspace = await runTimeOffPolicies(
        workspace,
        activityLog,
        slackAdapter,
        logger,
        now
      );
      member = workspace.members[0];
      defaultPolicy = workspace.timeOffs.policies[0];

      expect(member.timeOffs.VACATION?.balance ?? 0).toEqual(0);
      expect(defaultPolicy.typePolicies[0].nextAccruals).toEqual(nextMonday);
    });
  });

  describe("when roll-over is enabled", () => {
    test("preserves balance in the beginning of the next year", async () => {
      const firstOfJan = new Date("2025-01-01T00:00:00");

      const defaultPolicy: ReadonlyDeep<TimeOffPolicy> = {
        id: "1",
        title: "default",
        isDefault: true,
        workDays: [1, 2, 3, 4, 5],
        includedWeekendDays: [],
        notifyAccruals: false,
        typePolicies: [
          {
            typeId: "VACATION",
            yearStart: YearStart.Calendar,
            maxCapacity: 10,
            accrualsQuota: 2,
            accuralsFrequency: AccrualsFrequency.Week,
            onStartQuota: 0,
            rollOverToNextYear: true,
            nextAccruals: null,
          },
          {
            typeId: "ILLNESS",
            yearStart: YearStart.Calendar,
            maxCapacity: 10,
            accrualsQuota: 3,
            accuralsFrequency: AccrualsFrequency.Week,
            onStartQuota: 0,
            rollOverToNextYear: true,
            nextAccruals: null,
          },
        ],
      };

      workspace = addTimeOffPolicy(workspace, defaultPolicy);
      member = {
        ...member,
        timeOffs: {
          VACATION: { balance: 10, nextResetAt: firstOfJan },
          ILLNESS: { balance: 3, nextResetAt: firstOfJan },
        },
      };
      workspace = replaceMember(workspace, member);

      now = firstOfJan;
      workspace = await runTimeOffPolicies(
        workspace,
        activityLog,
        slackAdapter,
        logger,
        now
      );
      member = workspace.members[0];

      expect(member.timeOffs.VACATION?.balance).toEqual(10);
      expect(member.timeOffs.ILLNESS?.balance).toEqual(3);
      expect(member.timeOffs.VACATION?.nextResetAt).toEqual(
        new Date("2026-01-01T00:00:00")
      );
      expect(member.timeOffs.ILLNESS?.nextResetAt).toEqual(
        new Date("2026-01-01T00:00:00")
      );
    });
  });

  describe("when roll-over is disabled", () => {
    test("resets balance in the beginning of the next year", async () => {
      const januaryFirst = new Date("2025-01-01T00:00:00");

      const defaultPolicy: ReadonlyDeep<TimeOffPolicy> = {
        id: "1",
        title: "default",
        isDefault: true,
        workDays: [1, 2, 3, 4, 5],
        includedWeekendDays: [],
        notifyAccruals: false,
        typePolicies: [
          {
            typeId: "VACATION",
            yearStart: YearStart.Calendar,
            maxCapacity: 10,
            accrualsQuota: 2,
            accuralsFrequency: AccrualsFrequency.Week,
            onStartQuota: 5,
            rollOverToNextYear: false,
            nextAccruals: null,
          },
          {
            typeId: "ILLNESS",
            yearStart: YearStart.Calendar,
            maxCapacity: 3,
            accrualsQuota: 0,
            accuralsFrequency: AccrualsFrequency.Week,
            onStartQuota: 3,
            rollOverToNextYear: false,
            nextAccruals: null,
          },
        ],
      };

      workspace = addTimeOffPolicy(workspace, defaultPolicy);
      member = {
        ...member,
        timeOffs: {
          VACATION: { balance: 10, nextResetAt: januaryFirst },
          ILLNESS: { balance: 0, nextResetAt: januaryFirst },
        },
      };
      workspace = replaceMember(workspace, member);

      now = januaryFirst;
      workspace = await runTimeOffPolicies(
        workspace,
        activityLog,
        slackAdapter,
        logger,
        now
      );
      member = workspace.members[0];

      expect(member.timeOffs.VACATION?.balance).toEqual(5);
      expect(member.timeOffs.ILLNESS?.balance).toEqual(3);
      expect(member.timeOffs.VACATION?.nextResetAt).toEqual(
        new Date("2026-01-01T00:00:00")
      );
      expect(member.timeOffs.ILLNESS?.nextResetAt).toEqual(
        new Date("2026-01-01T00:00:00")
      );
    });
  });

  describe("when join quota is set", () => {
    test("initialize balance for newly joined members", async () => {
      const joinedAt = new Date("2024-05-20T00:00:00");
      const defaultPolicy: ReadonlyDeep<TimeOffPolicy> = {
        id: "1",
        title: "default",
        isDefault: true,
        workDays: [1, 2, 3, 4, 5],
        includedWeekendDays: [],
        notifyAccruals: false,
        typePolicies: [
          {
            typeId: "VACATION",
            yearStart: YearStart.Calendar,
            accrualsQuota: 2,
            maxCapacity: 0,
            accuralsFrequency: AccrualsFrequency.Week,
            onStartQuota: 10,
            rollOverToNextYear: false,
            nextAccruals: addYears(joinedAt, 1),
          },
        ],
      };

      workspace = addTimeOffPolicy(workspace, defaultPolicy);
      member = {
        ...member,
        joinedAt,
      };
      workspace = replaceMember(workspace, member);

      now = joinedAt;
      workspace = await runTimeOffPolicies(
        workspace,
        activityLog,
        slackAdapter,
        logger,
        now
      );
      member = workspace.members[0];

      expect(member.timeOffs.VACATION?.balance).toEqual(10);
    });

    test("don't increase balance later on", async () => {
      const joinedAt = new Date("2024-05-20T00:00:00");
      const defaultPolicy: ReadonlyDeep<TimeOffPolicy> = {
        id: "1",
        title: "default",
        isDefault: true,
        workDays: [1, 2, 3, 4, 5],
        includedWeekendDays: [],
        notifyAccruals: false,
        typePolicies: [
          {
            typeId: "VACATION",
            yearStart: YearStart.Calendar,
            accrualsQuota: 2,
            maxCapacity: 0,
            accuralsFrequency: AccrualsFrequency.Week,
            onStartQuota: 10,
            rollOverToNextYear: false,
            nextAccruals: addYears(joinedAt, 1),
          },
        ],
      };

      workspace = addTimeOffPolicy(workspace, defaultPolicy);
      member = {
        ...member,
        joinedAt,
        timeOffs: {
          VACATION: { balance: 0, nextResetAt: null },
        },
      };
      workspace = replaceMember(workspace, member);

      now = joinedAt;
      workspace = await runTimeOffPolicies(
        workspace,
        activityLog,
        slackAdapter,
        logger,
        now
      );
      member = workspace.members[0];

      expect(member.timeOffs.VACATION?.balance).toEqual(0);
    });
  });

  test("logs balance changes", async () => {
    const firstOfJan = new Date("2025-01-01T00:00:00");
    const mondayInJan = new Date("2025-01-06T00:00:00");

    const defaultPolicy: ReadonlyDeep<TimeOffPolicy> = {
      id: "1",
      title: "default",
      isDefault: true,
      workDays: [1, 2, 3, 4, 5],
      includedWeekendDays: [],
      notifyAccruals: false,
      typePolicies: [
        {
          typeId: "VACATION",
          yearStart: YearStart.Calendar,
          maxCapacity: 10,
          accrualsQuota: 2,
          accuralsFrequency: AccrualsFrequency.Week,
          onStartQuota: 0,
          rollOverToNextYear: false,
          nextAccruals: mondayInJan,
        },
      ],
    };

    workspace = addTimeOffPolicy(workspace, defaultPolicy);
    member = {
      ...member,
      timeOffs: {
        VACATION: { balance: 10, nextResetAt: firstOfJan },
      },
    };
    workspace = replaceMember(workspace, member);
    workspace = replaceMember(workspace, {
      ...workspace.members[1],
      timeOffs: {
        ...workspace.members[1].timeOffs,
        VACATION: {
          ...workspace.members[1].timeOffs.VACATION!,
          nextResetAt: firstOfJan,
        },
      },
    });

    now = firstOfJan;
    workspace = await runTimeOffPolicies(
      workspace,
      activityLog,
      slackAdapter,
      logger,
      now
    );

    now = mondayInJan;
    workspace = await runTimeOffPolicies(
      workspace,
      activityLog,
      slackAdapter,
      logger,
      now
    );

    expect(activityLog.add).toHaveBeenCalledWith({
      data: {
        absolute: 0,
        increase: undefined,
        initiatorId: undefined,
        memberId: "U058A2JMZFF",
        prevBalance: 10,
        typeId: "VACATION",
        updateType: "RESET",
      },
      type: "balanceChanged",
      workspaceId: workspace.id,
    });
    expect(activityLog.add).toHaveBeenCalledWith({
      data: {
        absolute: 0,
        increase: undefined,
        initiatorId: undefined,
        memberId: "U057C3HL0KH",
        prevBalance: 0,
        typeId: "VACATION",
        updateType: "RESET",
      },
      type: "balanceChanged",
      workspaceId: workspace.id,
    });
    expect(activityLog.add).toHaveBeenCalledWith({
      data: {
        absolute: undefined,
        increase: 2,
        initiatorId: undefined,
        memberId: "U058A2JMZFF",
        prevBalance: 0,
        typeId: "VACATION",
        updateType: "ACCRUALS",
      },
      type: "balanceChanged",
      workspaceId: workspace.id,
    });
    expect(activityLog.add).toHaveBeenCalledWith({
      data: {
        absolute: undefined,
        increase: 2,
        initiatorId: undefined,
        memberId: "U057C3HL0KH",
        prevBalance: 0,
        typeId: "VACATION",
        updateType: "ACCRUALS",
      },
      type: "balanceChanged",
      workspaceId: workspace.id,
    });
  });

  test("uses custom policy for members who have it specified", async () => {
    const nextMonday = new Date("2024-05-20T00:00:00");
    const nextMonth = new Date("2024-06-01T00:00:00");
    let defaultPolicy: ReadonlyDeep<TimeOffPolicy> = {
      id: "1",
      title: "default",
      isDefault: true,
      workDays: [1, 2, 3, 4, 5],
      includedWeekendDays: [],
      notifyAccruals: false,
      typePolicies: [
        {
          typeId: "VACATION",
          yearStart: YearStart.Calendar,
          maxCapacity: 0,
          accrualsQuota: 2,
          accuralsFrequency: AccrualsFrequency.Month,
          onStartQuota: 0,
          rollOverToNextYear: false,
          nextAccruals: nextMonth,
        },
      ],
    };
    let customPolicy: ReadonlyDeep<TimeOffPolicy> = {
      id: "2",
      title: "custom",
      isDefault: false,
      workDays: [1, 2, 3, 4, 5],
      includedWeekendDays: [],
      notifyAccruals: false,
      typePolicies: [
        {
          typeId: "VACATION",
          yearStart: YearStart.Calendar,
          maxCapacity: 0,
          accrualsQuota: 2,
          accuralsFrequency: AccrualsFrequency.Week,
          onStartQuota: 0,
          rollOverToNextYear: false,
          nextAccruals: nextMonday,
        },
      ],
    };

    workspace = addTimeOffPolicy(workspace, defaultPolicy);
    workspace = addTimeOffPolicy(workspace, customPolicy);
    member = {
      ...member,
      timeOffTypePolicyId: customPolicy.id,
    };
    workspace = replaceMember(workspace, member);

    expect(member.timeOffs.VACATION?.balance ?? 0).toEqual(0);

    now = nextMonday;
    workspace = await runTimeOffPolicies(
      workspace,
      activityLog,
      slackAdapter,
      logger,
      now
    );
    member = workspace.members[0];
    defaultPolicy = workspace.timeOffs.policies[0];
    customPolicy = workspace.timeOffs.policies[1];

    expect(member.timeOffs.VACATION?.balance ?? 0).toEqual(2);
    expect(customPolicy.typePolicies[0].nextAccruals).toEqual(
      addDays(nextMonday, 7)
    );
  });

  test("doesn't change anything if none of the criteria is met", async () => {
    const today = new Date("2024-05-19T00:00:00");
    const tomorrow = new Date("2024-05-20T00:00:00");

    const defaultPolicy: ReadonlyDeep<TimeOffPolicy> = {
      id: "1",
      title: "default",
      isDefault: true,
      workDays: [1, 2, 3, 4, 5],
      includedWeekendDays: [],
      notifyAccruals: false,
      typePolicies: [
        {
          typeId: "VACATION",
          yearStart: YearStart.Calendar,
          maxCapacity: 10,
          accrualsQuota: 0,
          accuralsFrequency: null,
          onStartQuota: 0,
          rollOverToNextYear: false,
          nextAccruals: tomorrow,
        },
      ],
    };

    workspace = addTimeOffPolicy(workspace, defaultPolicy);
    now = today;

    expect(
      await runTimeOffPolicies(
        workspace,
        activityLog,
        slackAdapter,
        logger,
        now
      )
    ).toEqual(workspace);
  });
});

function setTimeOffsChannel(
  workspace: ReadonlyDeep<Workspace>,
  channelId: string
): ReadonlyDeep<Workspace> {
  return {
    ...workspace,
    timeOffs: {
      ...workspace.timeOffs,
      channelId,
    },
  };
}

function turnOnTimeOffs(
  workspace: ReadonlyDeep<Workspace>
): ReadonlyDeep<Workspace> {
  return {
    ...workspace,
    timeOffs: {
      ...workspace.timeOffs,
      isEnable: true,
    },
  };
}

function turnOffTimeOffs(
  workspace: ReadonlyDeep<Workspace>
): ReadonlyDeep<Workspace> {
  return {
    ...workspace,
    timeOffs: {
      ...workspace.timeOffs,
      isEnable: false,
    },
  };
}

function addTimeOffPolicy(
  workspace: ReadonlyDeep<Workspace>,
  policy: ReadonlyDeep<TimeOffPolicy>
): ReadonlyDeep<Workspace> {
  return {
    ...workspace,
    timeOffs: {
      ...workspace.timeOffs,
      policies: [...workspace.timeOffs.policies, policy],
    },
  };
}
