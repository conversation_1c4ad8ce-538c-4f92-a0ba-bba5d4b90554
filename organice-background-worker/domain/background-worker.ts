import assert from "assert";

import {
  findTokenForSlackAdapter<PERSON><PERSON>ber<PERSON>p<PERSON>,
  <PERSON><PERSON>,
  Member,
  Position,
  ReadonlyDeep,
  replaceMember,
  Schedule,
  SlackAdapter,
  UpdateMember,
  Workspace,
  WorkspaceRepository,
  PresetPolicyId,
  StripeAdapter,
  OrgChartPredictor,
  pullChannelsFromSlack,
  TimeOffNotificationType,
  formatCustomFieldsForSlackUpdate,
  UpdateMemberCustomFields,
  SlackStatusType,
  SlackStatusChange,
  SlackBotToken,
} from "@organice/core/domain";
import {
  SlackStatusChangedType,
  ActivityLog,
  logSlackStatusChangeActivity,
} from "@organice/core/domain/activityLog";
import {
  getAnniversaryDateThisYear,
  getBirthdayDateThisYear,
} from "@organice/core/domain/announcements";
import {
  sendDiscountedSubscriptionOfferIfWorkspaceDoesntWantToPay,
  suspendAccessIfReachedSubscriptionLimits,
} from "@organice/core/domain/billing";
import {
  refreshWorkspace<PERSON><PERSON><PERSON>,
  **********************,
  collectMemberPolicyViolations,
  refreshWorkspaceCustomFields,
  getMemberNextNotificationMedium,
  hasMemberRunOutOfNotificationAttempts,
  get**********************,
  countMembersCompletion,
  countries,
  askMemberToPopulateProfileFields,
  parseCustomFieldFromSlackField,
  collectMemberData,
  formatPolicyFields,
} from "@organice/core/domain/data-completion";
import {
  fillHolidaysForCalendarYear,
  HolidayDescriptionGenerator,
  HolidayGateway,
} from "@organice/core/domain/holidays";
import {
  getNode,
  replaceNode,
  predictOrgChart,
  getMemberPosition,
  moveMemberToManager,
  createPosition,
} from "@organice/core/domain/org-chart";
import { sendDigestsPosition } from "@organice/core/domain/referrals";
import {
  deleteTimeOffReminder,
  replaceTimeOffRequest,
  getMemberOfficialHolidays,
} from "@organice/core/domain/time-offs";
import {
  formatPhoneNumber,
  isValidPhoneNumber,
} from "@organice/core/utils/phone";
import { isValid, isWithinInterval, isSameDay, addDays } from "date-fns";
import { isEqual, sortBy, isEmpty } from "lodash";

import { sendAnnouncements } from "./announcements";
import {
  onChannelsArchived as onChannelsArchivedNotifications,
  postNotifications,
} from "./notifications";
import {
  sendUnresolvedCandidates,
  setDefaultReferralsChannel,
} from "./referrals";
import {
  onChannelsArchived as onChannelsArchivedTimeOffs,
  runTimeOffPolicies,
} from "./time-offs";

export interface EmailAdapter {
  sendFieldAskMessage(
    workspace: ReadonlyDeep<Workspace>,
    member: ReadonlyDeep<Member>
  ): Promise<void>;
}

export interface BackgroundWorkerWorkspaceMeta {
  id: Workspace["id"];
  billing: {
    subscription: {
      ok: boolean;
    };
  };
}

export interface BackgroundWorkerQueryHandler {
  getBackgroundWorkerWorkspacesMeta(): Promise<BackgroundWorkerWorkspaceMeta[]>;
  getWorkspaceUpdatedAt(workspaceId: string): Promise<Date | null>;
}

export async function runBackgroundWorker(
  backgroundWorkerQueryHandler: BackgroundWorkerQueryHandler,
  repository: WorkspaceRepository,
  logger: Logger,
  activityLog: ActivityLog,
  emailAdapter: EmailAdapter,
  holidayGateway: HolidayGateway,
  holidayDescriptionGenerator: HolidayDescriptionGenerator,
  stripeAdapter: StripeAdapter,
  orgChartPredictor: OrgChartPredictor,
  getSlackAdapter: (slackBotToken: ReadonlyDeep<SlackBotToken>) => SlackAdapter,
  getTime: () => Date,
  workspaceId: string
): Promise<void> {
  let workspace = await repository.getWorkspace(workspaceId);
  let previousUpdatedAt =
    await backgroundWorkerQueryHandler.getWorkspaceUpdatedAt(workspaceId);

  assert(workspace, "Workspace not found");
  assert(
    !workspace.wasDeleted,
    "Expected WorkspaceRepository#getActiveWorkspacesIds to return non-deleted workspaces"
  );
  assert(
    workspace.onboarding.completed,
    "Expected WorkspaceRepository#getActiveWorkspacesIds to return workspaces with completed onboarding"
  );

  logger = logger.withContext({ workspace });

  if (!workspace.slackBotToken) {
    return;
  }

  const slackAdapter = getSlackAdapter(workspace.slackBotToken);

  const botTokenResponse = await slackAdapter.checkToken(
    workspace.slackBotToken.token
  );

  if (!botTokenResponse.ok) {
    return;
  }

  workspace = await refreshSlackPlan(
    logger,
    slackAdapter,
    getTime(),
    workspace
  );
  await repository.setWorkspace(workspace);

  workspace = await sendDigestsPosition(slackAdapter, workspace, getTime);
  workspace = await sendUnresolvedCandidates(slackAdapter, workspace, logger);
  workspace = await removeRemindersIfFreePlanOrTrialEnded(
    workspace,
    slackAdapter,
    logger,
    getTime
  );
  workspace = await fillHolidaysForNextCalendarYear(
    holidayGateway,
    holidayDescriptionGenerator,
    logger,
    workspace,
    getTime()
  );
  await repository.setWorkspace(workspace);

  workspace = await refreshWorkspaceMembers(
    logger,
    stripeAdapter,
    getTime,
    slackAdapter,
    workspace
  );
  await repository.setWorkspace(workspace);

  workspace = suspendAccessIfReachedSubscriptionLimits(workspace, getTime());
  workspace = await sendDiscountedSubscriptionOfferIfWorkspaceDoesntWantToPay(
    logger,
    slackAdapter,
    stripeAdapter,
    workspace,
    getTime()
  );
  await repository.setWorkspace(workspace);

  workspace = await refreshWorkspaceCustomFields(slackAdapter, workspace);

  workspace = await runTimeOffPolicies(
    workspace,
    activityLog,
    slackAdapter,
    logger,
    getTime()
  );

  workspace = await refreshWorkspaceChannels(
    slackAdapter,
    logger,
    workspace,
    getTime()
  );
  await repository.setWorkspace(workspace);

  workspace = await postNotifications(
    slackAdapter,
    workspace,
    logger,
    getTime()
  );
  await repository.setWorkspace(workspace);

  const prioritisedMembers = sortBy(workspace.members, (member) =>
    prioritiseMemberProcessing(workspace!, member)
  );

  let shouldRefetchWorkspace = false;

  {
    const updatedAt = await backgroundWorkerQueryHandler.getWorkspaceUpdatedAt(
      workspaceId
    );

    if (!isEqual(updatedAt, previousUpdatedAt)) {
      shouldRefetchWorkspace = true;
      previousUpdatedAt = updatedAt;
    }
  }

  // eslint-disable-next-line prefer-const
  for (let [index, member] of prioritisedMembers.entries()) {
    const memberLogger: Logger = logger
      .withContext({ member })
      .withPrefix(`[${index + 1}/${workspace.members.length}]`);

    try {
      if (shouldRefetchWorkspace) {
        workspace = (await repository.getWorkspace(workspaceId))!;
        assert(workspace, "Workspace disappeared");

        shouldRefetchWorkspace = false;
      }

      member = workspace.members.find((m) => m.id === member.id)!;
      assert(member, "Member disappeared");

      workspace = await sendAnnouncements(
        workspace,
        slackAdapter,
        memberLogger,
        getTime(),
        member
      );

      const customFields = await slackAdapter.getMemberCustomFields(
        workspaceId,
        member.id,
        workspace.policy
      );

      [workspace, member] = addMemberToOrgTree(
        memberLogger,
        workspace,
        member,
        customFields
      );

      [workspace, member] = flushSlackMemberToOrganice(
        memberLogger,
        workspace,
        member,
        customFields
      );

      if (workspace.slackIntegration) {
        await flushMemberOrgTreeChangesToSlack(
          slackAdapter,
          memberLogger,
          workspace,
          member
        );
      }

      [workspace, member] = await notifyMemberToPopulateProfileFields(
        slackAdapter,
        emailAdapter,
        memberLogger,
        workspace,
        member,
        getTime()
      );

      [workspace, member] = await sendOrgTreeProgress(
        slackAdapter,
        workspace,
        getTime(),
        memberLogger,
        member
      );

      [workspace, member] = await askAdminToHelpFillFields(
        workspace,
        slackAdapter,
        getTime(),
        memberLogger,
        member
      );

      [workspace, member] = await notifyAboutTrialOrFreePlanStatus(
        workspace,
        member,
        slackAdapter,
        logger,
        getTime
      );

      workspace = await changeSlackStatus(
        activityLog,
        slackAdapter,
        workspace,
        member,
        getTime()
      );

      workspace = await repository.setWorkspace(workspace);
    } catch (error) {
      memberLogger.error(error instanceof Error ? error : String(error));
    } finally {
      const updatedAt =
        await backgroundWorkerQueryHandler.getWorkspaceUpdatedAt(workspaceId);

      if (!isEqual(updatedAt, previousUpdatedAt)) {
        shouldRefetchWorkspace = true;
        previousUpdatedAt = updatedAt;
      }

      workspace = replaceMember(workspace, member);
      workspace = await repository.setWorkspace(workspace);
    }
  }

  if (
    workspace.predictedOrgTree.requested &&
    !workspace.predictedOrgTree.rootNode
  ) {
    workspace = await predictOrgChart(orgChartPredictor, workspace);
    await repository.setWorkspace({
      ...workspace,
      orgTree: {
        rootNode: {
          ...workspace.orgTree.rootNode,
          subordinates: [],
        },
      },
    });
    await repository.setWorkspace(workspace);
  }
}

async function refreshSlackPlan(
  logger: Logger,
  slackAdapter: SlackAdapter,
  time: Date,
  workspace: ReadonlyDeep<Workspace>
): Promise<ReadonlyDeep<Workspace>> {
  const { plan } = await slackAdapter.getSlackBillingInfo(workspace.id);

  if (workspace.slackPlan.plan !== plan) {
    logger.info("Slack plan changed", {
      plan_prev: workspace.slackPlan.plan,
      plan_new: plan,
    });

    return {
      ...workspace,
      slackPlan: { plan, updatedAt: time },
    };
  }

  return workspace;
}

export async function refreshWorkspaceChannels(
  slackAdapter: SlackAdapter,
  logger: Logger,
  workspace: ReadonlyDeep<Workspace>,
  time: Date
): Promise<ReadonlyDeep<Workspace>> {
  assert(workspace.slackBotToken, "Expected Slack bot token to exist");

  if (!workspace.slackBotToken.scopes.includes("channels:read")) {
    return workspace;
  }

  const prevChannels = workspace.channels;

  workspace = await pullChannelsFromSlack(slackAdapter, workspace);

  if (!workspace.reference.channel) {
    workspace = setDefaultReferralsChannel(workspace);
  }

  const nextChannelIds = new Set(
    workspace.channels.map((channel) => channel.id)
  );
  const archivedChannels = prevChannels.filter(
    (channel) => !nextChannelIds.has(channel.id)
  );

  if (archivedChannels.length > 0) {
    workspace = await onChannelsArchivedTimeOffs(
      slackAdapter,
      logger,
      workspace,
      archivedChannels,
      time
    );

    workspace = await onChannelsArchivedNotifications(
      slackAdapter,
      logger,
      workspace,
      archivedChannels,
      time
    );
  }

  return workspace;
}

// The less the number the higher the priority
function prioritiseMemberProcessing(
  workspace: ReadonlyDeep<Workspace>,
  member: ReadonlyDeep<Member>
): number {
  if (member.id === workspace.onboarding.finishedBy) {
    return 0;
  }

  if (member.isSlackWorkspacePrimaryOwner) {
    return 1;
  }

  return 2;
}

function isMemberHasEmptyOrganiceField(
  workspace: ReadonlyDeep<Workspace>,
  member: ReadonlyDeep<Member>,
  field: PresetPolicyId
): boolean {
  switch (field) {
    case PresetPolicyId.PHONE: {
      return !member.organicePhone;
    }

    case PresetPolicyId.ANNIVERSARY: {
      return !member.joinedAt;
    }

    case PresetPolicyId.COUNTRY: {
      return !member.country;
    }

    case PresetPolicyId.BIRTHDAY: {
      return !member.birthday;
    }

    case PresetPolicyId.TEAMS: {
      const position = getNode(
        workspace.orgTree.rootNode,
        (n): n is Position => n.type === "position" && n.memberId === member.id
      );

      return position == null || position.teamIds.length === 0;
    }

    default: {
      assert(true, `Unhandled PresetPolicyId field = ${field} has been met`);

      return false;
    }
  }
}

// If member already fill some slack fields, but we don't have them in Organice
// Initial manager and job title setted by addMemberToOrgTree
export function flushSlackMemberToOrganice(
  logger: Logger,
  workspace: ReadonlyDeep<Workspace>,
  member: ReadonlyDeep<Member>,
  customFields: Record<string, string | undefined>
): ReadonlyDeep<[Workspace, Member]> {
  const policy = workspace.policy;

  if (member.botState.wasInitiallySyncedWithSlack) {
    return [workspace, member];
  }

  const phoneFieldSlackId = policy[PresetPolicyId.PHONE].slackFieldId;
  const slackPhoneValue = phoneFieldSlackId && customFields[phoneFieldSlackId];

  if (
    isMemberHasEmptyOrganiceField(workspace, member, PresetPolicyId.PHONE) &&
    slackPhoneValue &&
    isValidPhoneNumber(slackPhoneValue)
  ) {
    member = {
      ...member,
      organicePhone: formatPhoneNumber(slackPhoneValue),
    };
  }

  const birthdayFieldSlackId = policy[PresetPolicyId.BIRTHDAY].slackFieldId;
  const slackBirthdayValue =
    birthdayFieldSlackId && customFields[birthdayFieldSlackId];

  if (
    isMemberHasEmptyOrganiceField(workspace, member, PresetPolicyId.BIRTHDAY) &&
    slackBirthdayValue
  ) {
    const slackBirthdayDate = new Date(slackBirthdayValue);

    if (isValid(slackBirthdayDate)) {
      member = {
        ...member,
        birthday: slackBirthdayDate,
      };
    }
  }

  const anniversaryFieldSlackId =
    policy[PresetPolicyId.ANNIVERSARY].slackFieldId;
  const slackAnniversaryValue =
    anniversaryFieldSlackId && customFields[anniversaryFieldSlackId];

  if (
    isMemberHasEmptyOrganiceField(
      workspace,
      member,
      PresetPolicyId.ANNIVERSARY
    ) &&
    slackAnniversaryValue
  ) {
    const slackAnniversaryDate = new Date(slackAnniversaryValue);

    if (isValid(slackAnniversaryDate)) {
      member = {
        ...member,
        joinedAt: slackAnniversaryDate,
      };
    }
  }

  const countryFieldSlackId = policy[PresetPolicyId.COUNTRY].slackFieldId;
  const slackCountryValue =
    countryFieldSlackId && customFields[countryFieldSlackId];

  if (
    isMemberHasEmptyOrganiceField(workspace, member, PresetPolicyId.COUNTRY) &&
    slackCountryValue
  ) {
    if (countries.some((country) => country.name === slackCountryValue)) {
      const countryPolicy = formatPolicyFields(workspace).find(
        (fp) => fp.id === PresetPolicyId.COUNTRY
      );

      assert(countryPolicy, "Unknown policy field");

      member = {
        ...member,
        country: parseCustomFieldFromSlackField(
          countryPolicy,
          slackCountryValue
        ),
      };
    }
  }

  let position = getNode(
    workspace.orgTree.rootNode,
    (n): n is Position => n.type === "position" && n.memberId === member.id
  );

  if (
    isMemberHasEmptyOrganiceField(workspace, member, PresetPolicyId.TEAMS) &&
    position != null
  ) {
    const teamsFieldSlackId = policy[PresetPolicyId.TEAMS].slackFieldId;

    const teamsSlackValue =
      teamsFieldSlackId != null && customFields[teamsFieldSlackId] != null
        ? customFields[teamsFieldSlackId]
        : undefined;

    const teamIds =
      teamsSlackValue != null
        ? getTeamIdsBasedOnSlackValue(workspace, teamsSlackValue)
        : undefined;

    if (teamIds != null && teamIds.length > 0) {
      position = {
        ...position,
        teamIds,
      };
      workspace = {
        ...workspace,
        orgTree: {
          rootNode: replaceNode(workspace.orgTree.rootNode, position),
        },
      };
    }
  }

  const customPolicyFields = policy.customPolicyFields.filter(
    (customPolicyField) => customPolicyField.slackFieldId
  );

  customPolicyFields.forEach((customPolicyField) => {
    if (
      !member.organiceCustomFileds[customPolicyField.id] &&
      customFields[customPolicyField.slackFieldId!]
    ) {
      member = {
        ...member,
        organiceCustomFileds: {
          ...member.organiceCustomFileds,
          [customPolicyField.id]: customFields[customPolicyField.slackFieldId!],
        },
      };
    }
  });

  member = {
    ...member,
    botState: {
      ...member.botState,
      wasInitiallySyncedWithSlack: true,
    },
  };

  workspace = replaceMember(workspace, member);

  logger.debug("Member slack fields synced with Organice");

  return [workspace, member];
}

function addMemberToOrgTree(
  logger: Logger,
  workspace: ReadonlyDeep<Workspace>,
  member: ReadonlyDeep<Member>,
  customFields: Record<string, string | undefined>
): [ReadonlyDeep<Workspace>, ReadonlyDeep<Member>] {
  if (member.botState.wasInitialOrgTreePositionSet) {
    return [workspace, member];
  }

  let rootNode = workspace.orgTree.rootNode;
  let position = getNode(
    rootNode,
    (n): n is Position => n.type === "position" && n.memberId === member.id
  );

  const policy = workspace.policy;

  // Handling the case when the position was set as someone's manager,
  // but the title wasn't there at the moment, so we set it one more time.
  if (position) {
    const jobTitleFieldSlackId =
      workspace.policy[PresetPolicyId.JOB_TITLE].slackFieldId;
    const slackJobTitleValue =
      jobTitleFieldSlackId && customFields[jobTitleFieldSlackId];

    position = {
      ...position,
      title: position.title ? position.title : slackJobTitleValue ?? "",
    };
    rootNode = replaceNode(rootNode, position);
    workspace = { ...workspace, orgTree: { rootNode } };
  }

  const managerFieldSlackId = policy[PresetPolicyId.MANAGER].slackFieldId;
  const managerId = managerFieldSlackId && customFields[managerFieldSlackId];

  if (
    !managerId ||
    member.id === managerId ||
    member.botState.wasAskedAboutManager
  ) {
    member = {
      ...member,
      botState: {
        ...member.botState,
        wasInitialOrgTreePositionSet: true,
      },
    };
    workspace = replaceMember(workspace, member);

    return [workspace, member];
  }

  if (!position) {
    [workspace, position] = createPosition(workspace, member.id);
  }

  const moveResult = moveMemberToManager(workspace, member, position, {
    memberId: managerId,
  });

  workspace = moveResult.workspace;
  member = moveResult.member;
  position = moveResult.node;

  member = {
    ...member,
    botState: {
      ...member.botState,
      wasInitialOrgTreePositionSet: true,
    },
  };
  workspace = replaceMember(workspace, member);

  logger.info("Member position added on the org-tree");

  return [workspace, member];
}

async function prepareWorderCustomFieldsUpdate(
  workspace: ReadonlyDeep<Workspace>,
  member: ReadonlyDeep<Member>,
  slackAdapter: SlackAdapter
): Promise<UpdateMemberCustomFields> {
  const customFields = await formatCustomFieldsForSlackUpdate(
    workspace,
    member,
    slackAdapter
  );

  const memberData = collectMemberData(workspace, member);

  const wasRemovedFromOrgTree =
    !!memberData[PresetPolicyId.MANAGER] &&
    getMemberPosition(workspace, member) == null &&
    !member.botState.wasAskedAboutManager &&
    member.botState.wasInitialOrgTreePositionSet === true;

  const shouldUpdateTitle =
    member.botState.wasAskedAboutManager === true || wasRemovedFromOrgTree;

  if (shouldUpdateTitle) {
    return customFields;
  }

  const jobTitleSlackFieldId =
    workspace.policy[PresetPolicyId.JOB_TITLE].slackFieldId;

  return Object.fromEntries(
    Object.entries(customFields).filter(
      ([slackFieldId]) => slackFieldId !== jobTitleSlackFieldId
    )
  );
}

async function flushMemberOrgTreeChangesToSlack(
  slackAdapter: SlackAdapter,
  logger: Logger,
  workspace: ReadonlyDeep<Workspace>,
  member: ReadonlyDeep<Member>
): Promise<void> {
  if (!workspace.billing.subscription.ok) {
    return;
  }

  const userToken = findTokenForSlackAdapterMemberUpdate(workspace, member);

  if (!userToken) {
    return;
  }

  const slackUpdate: UpdateMember = {
    memberId: member.id,
    customFields: await prepareWorderCustomFieldsUpdate(
      workspace,
      member,
      slackAdapter
    ),
  };

  if (isEmpty(slackUpdate.customFields)) {
    return;
  }

  await slackAdapter.updateMember(
    workspace.id,
    slackUpdate,
    workspace.policy,
    userToken
  );
}

async function notifyMemberToPopulateProfileFields(
  slackAdapter: SlackAdapter,
  emailAdapter: EmailAdapter,
  logger: Logger,
  workspace: ReadonlyDeep<Workspace>,
  member: ReadonlyDeep<Member>,
  time: Date
): Promise<[ReadonlyDeep<Workspace>, ReadonlyDeep<Member>]> {
  const sender =
    workspace.members.find((m) => m.id === workspace.onboarding.finishedBy) ??
    workspace.members.find((m) => m.isAdmin);

  assert(sender, "Cannot find a candidate for sender");

  if (
    !workspace.policy.notifications.enable ||
    !workspace.billing.subscription.ok
  ) {
    return [workspace, member];
  }

  const policyViolations = collectMemberPolicyViolations(workspace, member);

  const isPolicyRecentlyChanged =
    member.botState.type === "ok" && policyViolations.length > 0;

  const lastNotifiedAt = member.botState.updateProfileRequestedAt;

  const enoughTimeHasPassed =
    !lastNotifiedAt ||
    time.getTime() - lastNotifiedAt >
      getNotificationPeriod(workspace.policy.notifications.schedule);

  if (policyViolations.length === 0 || !enoughTimeHasPassed) {
    return [workspace, member];
  }

  const askMessageResponse = await askMemberToPopulateProfileFields(
    slackAdapter,
    logger,
    workspace,
    member,
    sender
  );

  member = {
    ...member,
    botState: {
      ...member.botState,
      updateProfileRequestedAt: time.valueOf(),
    },
  };

  if (member.botState.updateProfileMessageTs == null) {
    member = {
      ...member,
      botState: {
        ...askMessageResponse.botState,
        sentNotifications: [
          ...(askMessageResponse.botState.sentNotifications ?? []),
          {
            tag: "field-request",
            medium: "slack",
            timestamp: time.valueOf(),
            ts: askMessageResponse.ts,
          },
        ],
      },
    };

    assert(
      member.botState.updateProfileMessageTs,
      "updateProfileMessageTs should be set in a member botState"
    );

    workspace = replaceMember(workspace, member);

    return [workspace, member];
  }

  const medium = getMemberNextNotificationMedium(member);

  if (medium === "slack") {
    const { ts } = await slackAdapter.sendFollowUpMessage(
      member.id,
      member.botState.updateProfileMessageTs,
      {
        violations: [...policyViolations],
        isPolicyRecentlyChanged,
        installedBy: workspace.installedBy ?? "",
      }
    );

    logger.info("Sent an Follow Up message");

    member = {
      ...member,
      botState: {
        ...askMessageResponse.botState,
        sentNotifications: [
          ...(askMessageResponse.botState.sentNotifications ?? []),
          {
            tag: "field-request",
            medium: "slack",
            timestamp: time.valueOf(),
            ts: askMessageResponse.ts,
          },
          {
            tag: "follow-up-message",
            medium: "slack",
            timestamp: time.valueOf(),
            ts,
          },
        ],
      },
    };
    workspace = replaceMember(workspace, member);

    return [workspace, member];
  }

  if (medium === "email") {
    await emailAdapter.sendFieldAskMessage(workspace, member);

    logger.info("Sent an email");

    member = {
      ...member,
      botState: {
        ...askMessageResponse.botState,
        sentNotifications: [
          ...(askMessageResponse.botState.sentNotifications ?? []),
          {
            tag: "field-request",
            medium: "email",
            timestamp: time.valueOf(),
          },
          {
            tag: "follow-up-message",
            medium: "email",
            timestamp: time.valueOf(),
          },
        ],
      },
    };
    workspace = replaceMember(workspace, member);

    return [workspace, member];
  }

  return [workspace, member];
}

async function sendOrgTreeProgress(
  slackAdapter: SlackAdapter,
  workspace: ReadonlyDeep<Workspace>,
  time: Date,
  logger: Logger,
  member: ReadonlyDeep<Member>
): Promise<[ReadonlyDeep<Workspace>, ReadonlyDeep<Member>]> {
  if (
    !workspace.billing.subscription.ok ||
    !workspace.policy.notifications.enable
  ) {
    return [workspace, member];
  }

  const ONE_DAY = 1000 * 60 * 60 * 24;

  if (
    workspace.onboarding.finishedAt &&
    time.getTime() - workspace.onboarding.finishedAt > ONE_DAY &&
    time.getTime() - workspace.onboarding.finishedAt <= 2 * ONE_DAY &&
    member.isAdmin &&
    !member.botState.haveBeenAdminsNotificationOrgTreeSent
  ) {
    const memberGroups = countMembersCompletion(workspace, time);

    await slackAdapter.notifyAdminAboutOrgTree(
      member.id,
      memberGroups,
      workspace.id
    );

    logger.info(`24h after onboarding notification has been sent`);

    member = {
      ...member,
      botState: {
        ...member.botState,
        haveBeenAdminsNotificationOrgTreeSent: true,
      },
    };

    workspace = replaceMember(workspace, member);

    return [workspace, member];
  }

  return [workspace, member];
}

async function askAdminToHelpFillFields(
  workspace: ReadonlyDeep<Workspace>,
  slackAdapter: SlackAdapter,
  time: Date,
  logger: Logger,
  member: ReadonlyDeep<Member>
): Promise<[ReadonlyDeep<Workspace>, ReadonlyDeep<Member>]> {
  if (!member.isAdmin || !workspace.billing.subscription.ok) {
    return [workspace, member];
  }

  const notificationPeriod = getNotificationPeriod(Schedule.WEEKLY);

  const sentAdminNotifications =
    member.botState.sentNotifications?.filter(
      (n) => n.tag === "admin-need-help"
    ) ?? [];

  const lastAdminNotifyDate =
    sentAdminNotifications[sentAdminNotifications.length - 1]?.timestamp;
  const latestMemberLastNotificationDate = workspace.members
    .filter((m) => {
      const completionStatus = get**********************(
        workspace,
        m,
        time,
        Schedule.DAY
      );

      return (
        (completionStatus === **********************.IGNORED_US ||
          completionStatus === **********************.IN_PROGRESS) &&
        hasMemberRunOutOfNotificationAttempts(m)
      );
    })
    .map((m) => {
      const fieldRequests =
        m.botState.sentNotifications?.filter(
          (n) => n.tag === "field-request"
        ) ?? [];

      return fieldRequests[fieldRequests.length - 1]?.timestamp;
    })
    .reduce((acc, x) => Math.max(acc, x), 0);

  const passedTimeAfterAdminNotified = time.getTime() - lastAdminNotifyDate;
  const passedTimeAfterMemberNotified =
    time.getTime() - latestMemberLastNotificationDate;

  const isNotificationPeriodOver =
    passedTimeAfterAdminNotified > notificationPeriod &&
    passedTimeAfterMemberNotified > notificationPeriod;

  const shouldAdminNotified = !lastAdminNotifyDate || isNotificationPeriodOver;

  const areReportsEnabled = workspace.reports.enabled;

  if (areReportsEnabled && shouldAdminNotified) {
    const needHelpMemberIds = [];

    // eslint-disable-next-line @typescript-eslint/prefer-for-of
    for (let i = 0; i < workspace.members.length; i += 1) {
      const current = workspace.members[i];

      if (
        get**********************(workspace, current, time, Schedule.DAY) ===
        **********************.IGNORED_US
      ) {
        needHelpMemberIds.push(current.id);
      }
    }

    if (needHelpMemberIds.length === 0) {
      return [workspace, member];
    }

    const memberGroups = countMembersCompletion(workspace, time);

    await slackAdapter.askAdminToHelpFillFields(
      member.id,
      memberGroups,
      needHelpMemberIds,
      workspace.id
    );

    logger.info("In progress members has been sent");

    member = {
      ...member,
      botState: {
        ...member.botState,
        sentNotifications: [
          ...(member.botState.sentNotifications ?? []),
          {
            tag: "admin-need-help",
            medium: "slack",
            timestamp: time.valueOf(),
          } as const,
        ],
      },
    };

    workspace = replaceMember(workspace, member);

    return [workspace, member];
  }

  return [workspace, member];
}

function getNotificationPeriod(schedule: Schedule): number {
  const MINUTE_TIME = 1000 * 60;
  const DAY_TIME = 1000 * 60 * 60 * 24;
  const WEEK_TIME = DAY_TIME * 7;

  const periods: Record<Schedule, number> = process.env
    .NOTIFICATION_PERIOD_IN_MINUTES
    ? {
        [Schedule.HALF_HOUR]: MINUTE_TIME * 5,
        [Schedule.DAY]:
          MINUTE_TIME * Number(process.env.NOTIFICATION_PERIOD_IN_MINUTES),
        [Schedule.TWO_DAYS]:
          MINUTE_TIME * 2 * Number(process.env.NOTIFICATION_PERIOD_IN_MINUTES),
        [Schedule.WEEKLY]:
          MINUTE_TIME * 7 * Number(process.env.NOTIFICATION_PERIOD_IN_MINUTES),
      }
    : {
        [Schedule.HALF_HOUR]: MINUTE_TIME * 30,
        [Schedule.DAY]: DAY_TIME,
        [Schedule.TWO_DAYS]: DAY_TIME * 2,
        [Schedule.WEEKLY]: WEEK_TIME,
      };

  return periods[schedule];
}

export async function notifyAboutTrialOrFreePlanStatus(
  workspace: ReadonlyDeep<Workspace>,
  member: ReadonlyDeep<Member>,
  slackAdapter: SlackAdapter,
  logger: Logger,
  getTime: () => Date
): Promise<[ReadonlyDeep<Workspace>, ReadonlyDeep<Member>]> {
  let sentNotifications = member.botState.sentNotifications ?? [];
  const isNotifiedAboutTrialOrFreePlanEnded = sentNotifications.some(
    (nt) => nt.tag === "free-plan-ended" || nt.tag === "trial-expired"
  );

  if (!member.isAdmin || isNotifiedAboutTrialOrFreePlanEnded) {
    return [workspace, member];
  }

  const medium = "slack";
  const isNotifiedAboutAppSumoExpiration = sentNotifications.some(
    (nt) => nt.tag === "appsumo-subscription-ends-soon"
  );

  if (
    workspace.billing.subscription.type === "trial" &&
    workspace.billing.subscription.ok &&
    doesTrialEndSoon(workspace, getTime())
  ) {
    await slackAdapter.notifyAdminsAboutTrialExpiration(member.id);
    logger.info(`Notified admin about trial period end`);
    sentNotifications = [
      ...sentNotifications,
      { tag: "trial-expired", medium, timestamp: getTime().valueOf() },
    ];
  } else if (
    workspace.billing.subscription.type === "appsumo" &&
    workspace.billing.subscription.ok &&
    doesAppSumoSubscriptionEndSoon(workspace, getTime()) &&
    !isNotifiedAboutAppSumoExpiration
  ) {
    await slackAdapter.notifyAdminsAboutAppSumoSubscriptionSoonExpiration(
      member.id
    );
    logger.info(`Notified admin about AppSumo subscription ending soon`);
    sentNotifications = [
      ...sentNotifications,
      {
        tag: "appsumo-subscription-ends-soon",
        medium,
        timestamp: getTime().valueOf(),
      },
    ];
  }

  member = {
    ...member,
    botState: {
      ...member.botState,
      sentNotifications,
    },
  };

  workspace = replaceMember(workspace, member);

  return [workspace, member];
}

function doesTrialEndSoon(
  workspace: ReadonlyDeep<Workspace>,
  now: Date
): boolean {
  assert(
    workspace.billing.subscription.type === "trial",
    "Cannot tell if trial ends soon if workspace isn't on trial subscription"
  );

  const trialExpired =
    (workspace.billing.subscription.endsAt as Date).valueOf() < now.valueOf();

  if (trialExpired) {
    return true;
  }

  const expirationDate = (
    workspace.billing.subscription.endsAt as Date
  ).getDate();
  const oneDayBeforeTrialExpires = new Date(
    workspace.billing.subscription.endsAt as Date
  ).setDate(expirationDate - 1);

  return oneDayBeforeTrialExpires
    ? oneDayBeforeTrialExpires <= now.getTime()
    : false;
}

function doesAppSumoSubscriptionEndSoon(
  workspace: ReadonlyDeep<Workspace>,
  now: Date
): boolean {
  assert(
    workspace.billing.subscription.type === "appsumo",
    "Cannot tell if AppSumo subscription ends soon if workspace isn't on AppSumo subscription"
  );

  const appSumoSubscriptionEnded =
    (workspace.billing.subscription.endsAt as Date).valueOf() < now.valueOf();

  if (appSumoSubscriptionEnded) {
    return true;
  }

  const expirationDate = (
    workspace.billing.subscription.endsAt as Date
  ).getDate();
  const oneDayBeforeExpiration = new Date(
    workspace.billing.subscription.endsAt as Date
  ).setDate(expirationDate - 1);

  return oneDayBeforeExpiration
    ? oneDayBeforeExpiration <= now.getTime()
    : false;
}

export async function removeRemindersIfFreePlanOrTrialEnded(
  workspace: ReadonlyDeep<Workspace>,
  slackAdapter: SlackAdapter,
  logger: Logger,
  getTime: () => Date
): Promise<ReadonlyDeep<Workspace>> {
  let newWorkspace = { ...workspace };

  if (workspace.billing.subscription.ok) {
    return workspace;
  }

  for (let timeOffRequest of workspace.timeOffs.requests) {
    const timeOffReminders = timeOffRequest.notifications.filter(
      (nt) => nt.type === TimeOffNotificationType.TimeOffReminder
    );

    for (const timeOffReminder of timeOffReminders) {
      timeOffRequest = await deleteTimeOffReminder(
        slackAdapter,
        getTime(),
        timeOffRequest,
        timeOffReminder
      );
    }

    newWorkspace = replaceTimeOffRequest(workspace, timeOffRequest);
  }

  return newWorkspace;
}

async function fillHolidaysForNextCalendarYear(
  holidayGateway: HolidayGateway,
  holidayDescriptionGenerator: HolidayDescriptionGenerator,
  logger: Logger,
  workspace: ReadonlyDeep<Workspace>,
  now: Date
): Promise<ReadonlyDeep<Workspace>> {
  const thisYear = now.getFullYear();
  const nextYear = thisYear + 1;

  if (
    workspace.holidaysSettings.countries.length > 0 &&
    !workspace.holidays.some(
      (holiday) => holiday.startDate.getFullYear() === nextYear
    )
  ) {
    const countriesMissingHolidays =
      workspace.holidaysSettings.countries.filter(
        (country) =>
          // Heuristic: if there are no holidays for this year,
          // it means that data provider has no data for the specified country.
          workspace.holidays.some(
            (holiday) =>
              holiday.startDate.getFullYear() === thisYear &&
              holiday.country === country
          ) &&
          !workspace.holidays.some(
            (holiday) =>
              holiday.startDate.getFullYear() === nextYear &&
              holiday.country === country
          )
      );

    if (countriesMissingHolidays.length > 0) {
      workspace = await fillHolidaysForCalendarYear(
        holidayGateway,
        holidayDescriptionGenerator,
        workspace,
        countriesMissingHolidays,
        nextYear,
        now
      );

      logger.info("Filled holidays for the next year", {
        countriesMissingHolidays,
      });
    }
  }

  return workspace;
}

function getTeamIdsBasedOnSlackValue(
  workspace: ReadonlyDeep<Workspace>,
  slackValue: string
): string[] {
  const slackTeamsValue = slackValue.split(", ");

  return workspace.teams
    .filter((t) => slackTeamsValue.includes(t.label))
    .map((t) => t.id);
}

export async function changeSlackStatus(
  activityLog: ActivityLog,
  slackAdapter: SlackAdapter,
  workspace: ReadonlyDeep<Workspace>,
  member: ReadonlyDeep<Member>,
  now: Date
): Promise<ReadonlyDeep<Workspace>> {
  const userToken = findTokenForSlackAdapterMemberUpdate(workspace, member);

  if (!userToken) {
    return workspace;
  }

  let statusChanges = workspace.slackStatusChanges.filter(
    (change) => change.memberId === member.id
  );

  for (let statusChange of statusChanges) {
    const index = statusChanges.findIndex(
      (change) =>
        change.type === statusChange.type && change.refId === statusChange.refId
    );

    statusChange = statusChanges[index];
    const nextChange: ReadonlyDeep<SlackStatusChange> | undefined =
      statusChanges[index + 1] ? statusChanges[index + 1] : undefined;

    if (shouldRevertSlackStatusChange(workspace, member, now, statusChange)) {
      workspace = {
        ...workspace,
        slackStatusChanges: workspace.slackStatusChanges.filter(
          (c) => c !== statusChange
        ),
      };
      statusChanges = workspace.slackStatusChanges.filter(
        (c) => c.memberId === member.id
      );

      /**
       * NOTE: if we remove the last change we have to revert the status in Slack
       * otherwise we just squeeze the changes array and update the status
       * of the nextChange
       */
      if (!nextChange) {
        const status = statusChange.stashedStatus
          ? {
              statusText: statusChange.stashedStatus.text,
              statusEmoji: statusChange.stashedStatus.emoji,
              statusExpiration: statusChange.stashedStatus.expiresAt as Date,
            }
          : {
              statusText: "",
              statusEmoji: "",
              statusExpiration: undefined,
            };

        await slackAdapter.updateMember(
          workspace.id,
          {
            memberId: member.id,
            status,
          },
          workspace.policy,
          userToken
        );

        await logSlackStatusChangeActivity(activityLog, workspace, {
          ...status,
          memberId: member.id,
          updateType: SlackStatusChangedType.revert,
        });
        statusChanges = workspace.slackStatusChanges.filter(
          (c) => c.memberId === member.id
        );
      } else {
        workspace = {
          ...workspace,
          slackStatusChanges: workspace.slackStatusChanges.map((c) =>
            c === nextChange
              ? {
                  ...nextChange,
                  stashedStatus: statusChange.stashedStatus,
                }
              : c
          ),
        };
        statusChanges = workspace.slackStatusChanges.filter(
          (c) => c.memberId === member.id
        );
      }
    }
  }

  /**
   * NOTE: there were status changes before, as member's status sync
   * happens in another place we have to wait for the sync to finish
   */
  if (
    workspace.slackStatusChanges.some(
      (change) => change.memberId === member.id
    ) &&
    !member.status
  ) {
    return workspace;
  }

  const [newStatus, newStatusChange] = getRequiredSlackStatusChange(
    workspace,
    member,
    now
  );

  if (newStatus && newStatusChange) {
    workspace = {
      ...workspace,
      slackStatusChanges: [...workspace.slackStatusChanges, newStatusChange],
    };

    await slackAdapter.updateMember(
      workspace.id,
      {
        memberId: member.id,
        status: newStatus,
      },
      workspace.policy,
      userToken
    );

    await logSlackStatusChangeActivity(activityLog, workspace, {
      ...newStatus,
      memberId: member.id,
      updateType: SlackStatusChangedType.set,
    });
  }

  return workspace;
}

function getRequiredSlackStatusChange(
  workspace: ReadonlyDeep<Workspace>,
  member: ReadonlyDeep<Member>,
  now: Date
): [
  {
    statusText: string;
    statusEmoji: string;
    statusExpiration?: Date;
  } | null,
  SlackStatusChange | null
] {
  const statusChanges = workspace.slackStatusChanges.filter(
    (change) => change.memberId === member.id
  );

  let newStatus: {
    statusText: string;
    statusEmoji: string;
    statusExpiration?: Date;
  } | null = null;
  let newStatusChange: SlackStatusChange | null = null;

  const requests = workspace.timeOffs.requests.filter(
    (request) => request.memberId === member.id && request.status === "APPROVED"
  );
  const birthday = getBirthdayDateThisYear(member, now.getFullYear());
  const anniversary = getAnniversaryDateThisYear(member, now.getFullYear());
  const officialHolidays = getMemberOfficialHolidays(member, workspace);
  const ongoingOfficialHoliday = officialHolidays.find((holiday) =>
    isWithinInterval(now, {
      start: holiday.startDate as Date,
      end: holiday.endDate as Date,
    })
  );
  const ongoingTimeOff = requests.find((request) =>
    isWithinInterval(now, {
      start: request.startDate as Date,
      end: request.endDate as Date,
    })
  );

  if (
    workspace.celebrationSettings.birthday.changeSlackStatus &&
    birthday &&
    isSameDay(birthday, now)
  ) {
    if (
      !statusChanges.some((change) => change.type === SlackStatusType.Birthday)
    ) {
      newStatus = {
        statusText: "Happy Birthday!",
        statusEmoji: "🎂",
        statusExpiration: addDays(birthday, 1),
      };
      newStatusChange = {
        type: SlackStatusType.Birthday,
        refId: null,
        memberId: member.id,
        stashedStatus: null,
      };
    }
  } else if (
    workspace.celebrationSettings.anniversary.changeSlackStatus &&
    anniversary &&
    isSameDay(anniversary, now)
  ) {
    if (
      !statusChanges.some(
        (change) => change.type === SlackStatusType.Anniversary
      )
    ) {
      newStatus = {
        statusText: "Happy Anniversary!",
        statusEmoji: "🎉",
        statusExpiration: addDays(anniversary, 1),
      };
      newStatusChange = {
        type: SlackStatusType.Anniversary,
        refId: null,
        memberId: member.id,
        stashedStatus: null,
      };
    }
  } else if (workspace.timeOffs.changeSlackStatus && ongoingTimeOff) {
    if (
      !statusChanges.some(
        (change) =>
          change.type === SlackStatusType.TimeOff ||
          change.refId === ongoingTimeOff.id
      )
    ) {
      const type = workspace.timeOffs.types.find(
        (t) => t.id === ongoingTimeOff.type
      );

      assert(type, `Expected "${ongoingTimeOff.type}" time off type to exist`);

      newStatus = {
        statusText: type.label,
        statusEmoji: type.slackEmoji,
        statusExpiration: ongoingTimeOff.endDate as Date,
      };
      newStatusChange = {
        type: SlackStatusType.TimeOff,
        refId: ongoingTimeOff.id,
        memberId: member.id,
        stashedStatus: null,
      };
    }
  } else if (workspace.timeOffs.changeSlackStatus && ongoingOfficialHoliday) {
    if (
      !statusChanges.some(
        (change) =>
          change.type === SlackStatusType.Holiday ||
          change.refId === ongoingOfficialHoliday.id
      )
    ) {
      newStatus = {
        statusText: ongoingOfficialHoliday.name,
        statusEmoji:
          countries.find(
            (country) => country.name === ongoingOfficialHoliday.country
          )?.emoji ?? "",
        statusExpiration: ongoingOfficialHoliday.endDate as Date,
      };
      newStatusChange = {
        type: SlackStatusType.Holiday,
        refId: ongoingOfficialHoliday.id,
        memberId: member.id,
        stashedStatus: null,
      };
    }
  }

  if (newStatusChange && member.status) {
    newStatusChange.stashedStatus = {
      text: member.status.text,
      emoji: member.status.emoji,
      expiresAt: member.status.expiresAt as Date | undefined,
    };
  }

  return [newStatus, newStatusChange];
}

function shouldRevertSlackStatusChange(
  workspace: ReadonlyDeep<Workspace>,
  member: ReadonlyDeep<Member>,
  now: Date,
  statusChange: ReadonlyDeep<SlackStatusChange>
): boolean {
  if (statusChange.type === SlackStatusType.TimeOff) {
    const requests = workspace.timeOffs.requests.filter(
      (request) =>
        request.memberId === member.id && request.status === "APPROVED"
    );
    const request = requests.find((r) => r.id === statusChange.refId);

    return (
      !workspace.timeOffs.changeSlackStatus ||
      !request ||
      !isWithinInterval(now, {
        start: request.startDate as Date,
        end: request.endDate as Date,
      })
    );
  }

  if (statusChange.type === SlackStatusType.Holiday) {
    const officialHolidays = getMemberOfficialHolidays(member, workspace);
    const holiday = officialHolidays.find((h) => h.id === statusChange.refId);

    return (
      !workspace.timeOffs.changeSlackStatus ||
      !holiday ||
      !isWithinInterval(now, {
        start: holiday.startDate as Date,
        end: holiday.endDate as Date,
      })
    );
  }

  if (statusChange.type === SlackStatusType.Birthday) {
    const birthday = getBirthdayDateThisYear(member, now.getFullYear());

    return (
      !workspace.celebrationSettings.birthday.changeSlackStatus ||
      !birthday ||
      !isSameDay(birthday, now)
    );
  }

  const anniversary = getAnniversaryDateThisYear(member, now.getFullYear());

  return (
    !workspace.celebrationSettings.anniversary.changeSlackStatus ||
    !anniversary ||
    !isSameDay(anniversary, now)
  );
}
