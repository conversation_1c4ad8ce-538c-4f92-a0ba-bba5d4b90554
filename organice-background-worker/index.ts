/* eslint-disable no-console */
import assert from "assert";

import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  SlackBotToken,
  Workspace,
  NoOpStripeAdapter,
  WorkspaceRepository,
  SlackAdapter,
  SlackBotApplication,
} from "@organice/core/domain";
import LoggerImpl from "@organice/core/logger/LoggerImpl";
import { NagerHolidayGateway } from "@organice/core/nager/NagerHolidayGateway";
import ChatGPTHolidayDescriptionGenerator from "@organice/core/openai/ChatGPTHolidayDescriptionGenerator";
import ChatGPTOrgChartPredictor from "@organice/core/openai/ChatGPTOrgChartPredictor";
import { ChatGPTAPI } from "@organice/core/openai/chatgpt";
import PrismaActivityLog from "@organice/core/prisma/PrismaActivityLog";
import { prismaClient } from "@organice/core/prisma/PrismaClient";
import PrismaWorkspaceRepository from "@organice/core/prisma/PrismaWorkspaceRepository";
import StripeAdapterImpl from "@organice/core/stripe/StripeAdapterImpl";
import StripeClient from "@organice/core/stripe/StripeClient";
import PrismaSlackBotQueryHandler from "@organice/slack-bot/prisma/PrismaSlackBotQueryHandler";
import SlackAdapterImpl from "@organice/slack-bot/slack/SlackAdapterImpl";
import * as Sentry from "@sentry/node";
import { WebClient } from "@slack/web-api";

import {
  EmailAdapter,
  BackgroundWorkerWorkspaceMeta,
  runBackgroundWorker,
} from "./domain/background-worker";
import SmtpEmailAdapter from "./email/SmtpEmailAdapter";
import PrismaBackgroundWorkerQueryHandler from "./prisma/PrismaBackgroundWorkerQueryHandler";

Sentry.init({
  environment: process.env.NEXT_PUBLIC_SENTRY_ENVIRONMENT,
  dsn: process.env.SENTRY_DSN_BACKGROUND_WORKER,
  integrations: [
    Sentry.captureConsoleIntegration({
      levels: ["error", "assert", "warn"],
    }),
  ],
  // Set tracesSampleRate to 1.0 to capture 100%
  // of transactions for performance monitoring.
  // We recommend adjusting this value in production
  tracesSampleRate: 0.0,
});

assert(
  process.env.ORGANICE_SLACK_APP_ID,
  "ORGANICE_SLACK_APP_ID env variable should be defined"
);
assert(
  process.env.KUDOS_SLACK_APP_ID,
  "KUDOS_SLACK_APP_ID env variable should be defined"
);
assert(
  process.env.SLACK_ADMIN_SITE_URL,
  "SLACK_ADMIN_SITE_URL env variable should be defined"
);
assert(
  process.env.SMTP_CONNECTION_URL,
  "SMTP_CONNECTION_URL env variable should be defined"
);
assert(
  process.env.CHATGPT_API_KEY,
  "CHATGPT_API_KEY env variable should be defined"
);

const organiceSlackAppId = process.env.ORGANICE_SLACK_APP_ID;
const kudosSlackAppId = process.env.KUDOS_SLACK_APP_ID;

function getRepository(): WorkspaceRepository {
  return new PrismaWorkspaceRepository(prismaClient);
}

function getEmailAdapter(): EmailAdapter {
  return new SmtpEmailAdapter(process.env.SMTP_CONNECTION_URL!);
}

function getTime(): Date {
  return new Date();
}

function getSlackAdapterFromOAuthBotToken(
  slackBotToken: ReadonlyDeep<SlackBotToken>
): SlackAdapter {
  return new SlackAdapterImpl({
    client: new WebClient(slackBotToken.token, {
      slackApiUrl,
    }),
    adminSiteUrl,
    scopes: slackBotToken.scopes,
    queryHandler: new PrismaSlackBotQueryHandler(prismaClient),
    appIds: {
      [SlackBotApplication.ORGANICE]: organiceSlackAppId,
      [SlackBotApplication.KUDOS]: kudosSlackAppId,
    },
  });
}

const adminSiteUrl = process.env.SLACK_ADMIN_SITE_URL;
const slackApiUrl = process.env.SLACK_API_URL
  ? process.env.SLACK_API_URL
  : undefined;

const logger = new LoggerImpl(
  {},
  {
    sentry: !!process.env.SENTRY_DSN_BACKGROUND_WORKER,
    mixpanel: process.env.MIXPANEL_AUTH_TOKEN
      ? { token: process.env.MIXPANEL_AUTH_TOKEN }
      : undefined,
  }
);

const emailAdapter = getEmailAdapter();
const stripeAdapter =
  process.env.STRIPE_SECRET_KEY && process.env.STRIPE_PRODUCT_ID
    ? new StripeAdapterImpl(
        new StripeClient(process.env.STRIPE_SECRET_KEY),
        process.env.STRIPE_PRODUCT_ID
      )
    : new NoOpStripeAdapter();
const chatGptApi = new ChatGPTAPI({
  apiKey: process.env.CHATGPT_API_KEY,
});
const orgChartPredictor = new ChatGPTOrgChartPredictor(chatGptApi);
const holidayGateway = new NagerHolidayGateway();
const holidayDescriptionGenerator = new ChatGPTHolidayDescriptionGenerator(
  chatGptApi
);
const activityLog = new PrismaActivityLog(prismaClient);
const backgroundWorkerQueryHandler = new PrismaBackgroundWorkerQueryHandler(
  prismaClient
);

async function spawnWorker(workspaceId: Workspace["id"]): Promise<void> {
  const workspaceLogger = logger.withContext({
    workspace: { id: workspaceId },
  });
  const repository = getRepository();

  try {
    await runBackgroundWorker(
      backgroundWorkerQueryHandler,
      repository,
      workspaceLogger,
      activityLog,
      emailAdapter,
      holidayGateway,
      holidayDescriptionGenerator,
      stripeAdapter,
      orgChartPredictor,
      getSlackAdapterFromOAuthBotToken,
      getTime,
      workspaceId
    );
  } catch (error) {
    workspaceLogger.error(error);
  }
}

const MAX_CONCURRENT_WORKERS = 20;
const MAX_NEW_WORKERS_PER_ITERATION = 10;
const SPAWN_CYCLE_DURATION_IN_MILLISECONDS = 20_000; // 20 seconds

(async function rootWorker(): Promise<void> {
  console.log(`Started background worker`);

  let workspaces = new Map<Workspace["id"], BackgroundWorkerWorkspaceMeta>();
  const runningWorkers = new Map<Workspace["id"], Promise<void>>();
  const queue = new Map<Workspace["id"], Date>();

  function scheduleWorkspaceProcessing(
    workspace: BackgroundWorkerWorkspaceMeta
  ): void {
    let coolDownPeriod = workspace.billing.subscription.ok
      ? 3 * 60 * 1000 // 3 minutes
      : 4 * 60 * 60 * 1000; // 4 hours

    if (process.env.BACKGROUND_WORKER_NO_DELAY === "1") {
      coolDownPeriod = 0;
    }

    if (!queue.has(workspace.id)) {
      queue.set(
        workspace.id,
        new Date(Date.now() + Math.round(coolDownPeriod * Math.random()))
      );
    } else {
      queue.set(workspace.id, new Date(Date.now() + coolDownPeriod));
    }
  }

  function requestImmediateWorkspaceProcessing(
    workspace: BackgroundWorkerWorkspaceMeta
  ): void {
    if (!queue.has(workspace.id)) {
      queue.set(workspace.id, new Date());
    } else {
      queue.set(workspace.id, new Date());
    }
  }

  // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition, no-constant-condition
  while (true) {
    workspaces = new Map(
      (
        await backgroundWorkerQueryHandler
          .getBackgroundWorkerWorkspacesMeta()
          .catch((error) => {
            logger.error(error);

            return [];
          })
      ).map((workspace) => [workspace.id, workspace])
    );

    if (queue.size === 0) {
      for (const workspace of workspaces.values()) {
        scheduleWorkspaceProcessing(workspace);
      }
      console.log("Randomized queue order");
    } else {
      for (const workspace of workspaces.values()) {
        if (!queue.has(workspace.id)) {
          requestImmediateWorkspaceProcessing(workspace);
          console.log(
            `Requested immediate processing for new workspace (workspace: ${workspace.id})`
          );
        }
      }
    }

    const now = new Date();
    const workersToRun = [...queue.entries()]
      .filter(
        ([workspaceId, nextScheduledRunAt]) =>
          !runningWorkers.has(workspaceId) &&
          now.valueOf() >= nextScheduledRunAt.valueOf()
      )
      .sort(
        ([, nextScheduledRunAt], [, anotherNextScheduledRunAt]) =>
          nextScheduledRunAt.valueOf() - anotherNextScheduledRunAt.valueOf()
      )
      // eslint-disable-next-line @typescript-eslint/no-loop-func
      .map(([workspaceId]) => workspaces.get(workspaceId))
      .filter(
        (workspace): workspace is BackgroundWorkerWorkspaceMeta =>
          workspace != null
      )
      .slice(0, Math.max(MAX_CONCURRENT_WORKERS - runningWorkers.size, 0))
      .slice(0, MAX_NEW_WORKERS_PER_ITERATION);

    for (const workspace of workersToRun) {
      const worker = spawnWorker(workspace.id).finally(() => {
        runningWorkers.delete(workspace.id);
        scheduleWorkspaceProcessing(workspace);
      });

      runningWorkers.set(workspace.id, worker);

      await new Promise((resolve) => {
        setTimeout(
          resolve,
          SPAWN_CYCLE_DURATION_IN_MILLISECONDS / workersToRun.length
        );
      });
    }

    if (workersToRun.length === 0) {
      await new Promise((resolve) => {
        setTimeout(resolve, SPAWN_CYCLE_DURATION_IN_MILLISECONDS);
      });
    }
  }
})().catch((error) => {
  console.error(error);
  process.exit(1);
});
