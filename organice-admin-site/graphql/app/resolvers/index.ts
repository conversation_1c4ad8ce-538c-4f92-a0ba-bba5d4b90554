/* eslint-disable @typescript-eslint/no-unsafe-member-access */
import assert from "assert";
import crypto from "crypto";

import {
  Logger,
  Schedule,
  PolicyFieldType,
  ReadonlyDeep,
  Workspace as DomainWorkspace,
  CelebrationSettings as CelebrationSettingsDomain,
  Department as DomainDepartment,
  Position as DomainPosition,
  PresetPolicyId,
  replaceMember,
  pullChannelsFromSlack,
  updateMemberCustomFields,
  formatCustomFieldsForSlackUpdate,
  findTokenForSlackAdapterMemberUpdate,
  SlackBotApplication,
} from "@organice/core/domain";
import {
  countMembersCompletion,
  refreshWorkspaceCustomFields,
} from "@organice/core/domain/data-completion";
import {
  getNode,
  getNodes,
  getSubordinatesInfo,
  canPredictOrgChart,
} from "@organice/core/domain/org-chart";
import {
  sendDigestsPosition,
  getNextReferralsDigestNotificationTime,
} from "@organice/core/domain/referrals";
import { calculateWorkingDaysStats } from "@organice/core/domain/time-offs";
import { startOfDay, endOfDay, endOfMonth, startOfMonth } from "date-fns";
import { GraphQLError } from "graphql";

import {
  InvalidTokenError,
  getSessionWorkspace,
  Session,
} from "../../../domain";
import { encryptIntercomUser } from "../../../helpers/crypto";
import { getLoggerFromSession } from "../../../helpers/getLogger";
import { getSlackAdapter } from "../../../helpers/getSlackAdapter";
import PrismaActivityLogQueryResolver from "../../../prisma/PrismaActivityLogQueryResolver";
import PrismaKudosQueryResolver from "../../../prisma/PrismaKudosQueryResolver";
import {
  type Resolvers,
  type IMemberFieldResolvers,
  type LogResolvers,
  type QueryResolvers,
  type MutationResolvers,
  type ReferralSettings,
  type CelebrationSettings,
  type MemberResolvers,
} from "../../server.generated";
import {
  PredictedOrgChart,
  PredictedOrgChartStatus,
  MemberProfileInput,
  SlackPermissionsIssues,
  SlackStatusLogType,
} from "../../server.generated";

import {
  Context,
  formatMember,
  formatPosition,
  hasPermission,
  getMembers,
  formatWorkspace,
  getCountriesWMembers,
  formatPolicy,
} from "./_common";
import {
  Query as AnnouncementsQuery,
  Mutation as AnnouncementsMutation,
} from "./announcements";
import { Query as CountriesQuery } from "./countries";
import {
  Query as HolidaysQuery,
  Mutation as HolidaysMutation,
} from "./holidays";
import { Query as KudosQuery, Mutation as KudosMutation } from "./kudos";
import { Query as LinksQuery, Mutation as LinksMutation } from "./links";
import {
  NotificationBlock,
  Query as NotificationsQuery,
  Mutation as NotificationsMutation,
} from "./notifications";
import {
  Query as OnboardingQuery,
  Mutation as OnboardingMutation,
} from "./onboarding";
import {
  Subordinate,
  OrgTreeNode,
  PositionOrRootNode,
  Query as OrgTreeQuery,
  Mutation as OrgTreeMutation,
} from "./orgTree";
import { Query as PricingQuery, Mutation as PricingMutation } from "./pricing";
import { RecentMembers, Query as SearchQuery } from "./search";
import { Query as SurveysQuery, Mutation as SurveysMutation } from "./surveys";
import { Query as TeamsQuery, Mutation as TeamsMutation } from "./teams";
import {
  CalendarEvent,
  Query as TimeOffsQuery,
  Mutation as TimeOffsMutation,
} from "./timeOffs";

// eslint-disable-next-line max-classes-per-file

const Log: LogResolvers = {
  // eslint-disable-next-line no-underscore-dangle
  __resolveType(obj) {
    if ("request" in obj.data) {
      return "TimeOffLog";
    }

    if ("increase" in obj.data || "absolute" in obj.data) {
      return "BalanceLog";
    }

    if (
      obj.type === SlackStatusLogType.Set ||
      obj.type === SlackStatusLogType.Revert
    ) {
      return "SlackStatusLog";
    }

    return null;
  },
};

const IMemberField: IMemberFieldResolvers = {
  // eslint-disable-next-line no-underscore-dangle
  __resolveType(obj) {
    if ("position" in obj) {
      return "ManagerField";
    }

    if ("urlurl72" in obj || "url512" in obj) {
      return "PhotoField";
    }

    if ("teams" in obj) {
      return "TeamsField";
    }

    if ("date" in obj) {
      return "DateField";
    }

    if ("department" in obj) {
      return "DepartmentField";
    }

    if ("link" in obj) {
      return "LinkField";
    }

    if ("member" in obj) {
      return "UserField";
    }

    if ("text" in obj) {
      return "TextField";
    }

    throw new Error(`Unknown field type ${JSON.stringify(obj)}`);
  },
};

const Member: MemberResolvers<Context> = {
  workingDays: async (member, args, { repository, session }) => {
    const workspace = await getSessionWorkspace(repository, session);
    const domainMember = workspace.members.find((m) => m.id === member.id);

    assert(domainMember, "Member not found");

    const fromDate = args.from
      ? startOfDay(new Date(args.from))
      : startOfMonth(new Date());
    const toDate = args.to
      ? endOfDay(new Date(args.to))
      : endOfMonth(new Date());
    const range = { start: fromDate, end: toDate };

    return calculateWorkingDaysStats(workspace, domainMember, range);
  },
};

const Query: Required<QueryResolvers<Context>> = {
  ...AnnouncementsQuery,
  ...CountriesQuery,
  ...HolidaysQuery,
  ...KudosQuery,
  ...LinksQuery,
  ...NotificationsQuery,
  ...OnboardingQuery,
  ...OrgTreeQuery,
  ...PricingQuery,
  ...PrismaActivityLogQueryResolver,
  ...PrismaKudosQueryResolver,
  ...SearchQuery,
  ...SurveysQuery,
  ...TeamsQuery,
  ...TimeOffsQuery,

  async celebrationSettings(_parent, _args, { repository, session }) {
    const workspace = await getSessionWorkspace(repository, session);

    return formatCelebrationSettings(workspace);
  },

  async myWorkspace(_parent, _args, { repository, session }) {
    const workspace = await getSessionWorkspace(repository, session);

    return formatWorkspace(workspace, session);
  },

  async stats(_parent, _args, { repository, session }) {
    const workspace = await getSessionWorkspace(repository, session);
    const now = new Date();

    const memberCompletion = countMembersCompletion(workspace, now);
    const rootNode = workspace.orgTree.rootNode;
    const { assignedPositionNumber, allPositionNumber } = getSubordinatesInfo(
      rootNode.subordinates
    );
    let totalNumberWithBirthday = 0;
    let totalNumberWithAnniversary = 0;

    workspace.members.forEach((member) => {
      if (member.birthday) {
        totalNumberWithBirthday += 1;
      }

      if (member.joinedAt) {
        totalNumberWithAnniversary += 1;
      }
    });

    const departments = getNodes(
      workspace.orgTree.rootNode,
      (n): n is DomainDepartment => n.type === "department"
    );
    const teams = workspace.teams;

    const countriesList = getCountriesWMembers(workspace);

    return {
      id: workspace.id,
      totalMembers: memberCompletion.all,
      totalMembersWithPosition: assignedPositionNumber,
      totalPositionsOnboarded: memberCompletion.onboarded,
      totalPositionsInProgress: memberCompletion.progress,
      totalPositions: allPositionNumber,
      totalNumberWithBirthday,
      totalNumberWithAnniversary,
      totalNumberDepartments: departments.length,
      totalNumberTeams: teams.length,
      totalNumberCountries: countriesList.length,
    };
  },

  async slackCustomFields(
    _parent,
    { refreshFromSlack },
    { repository, session }
  ) {
    let workspace = await getSessionWorkspace(repository, session);

    assert(workspace.slackBotToken, "Expected Slack bot token to exist");

    if (refreshFromSlack) {
      const slackAdapter = getSlackAdapter(workspace.slackBotToken);

      workspace = await refreshWorkspaceCustomFields(slackAdapter, workspace);
      await repository.setWorkspace(workspace);
    }

    return {
      slackCustomFields: workspace.customFields.filter(
        (x) =>
          x.visible ||
          x.id === workspace.policy[PresetPolicyId.DEPARTMENT].slackFieldId ||
          x.id === workspace.policy[PresetPolicyId.COUNTRY].slackFieldId
      ),
    };
  },

  async me(_parent, _args, { repository, session }) {
    const workspace = await getSessionWorkspace(repository, session);
    const member = workspace.members.find((m) => m.id === session.memberId);

    assert(member, `Expected member with id "${session.memberId}" to exist`);

    const position = getNode(
      workspace.orgTree.rootNode,
      (n): n is DomainPosition =>
        n.type === "position" && n.memberId === member.id
    );
    const bot = workspace.slackBotToken;
    const hash = encryptIntercomUser(member.id);
    const MINUTE_TIME = 1000 * 60;
    const TWO_DAYS_TIME = 2 * 24 * 60 * MINUTE_TIME;
    const messageDelay = process.env.PRICING_MESSAGE_DELAY_IN_MINUTES
      ? Number(process.env.PRICING_MESSAGE_DELAY_IN_MINUTES) * MINUTE_TIME
      : TWO_DAYS_TIME;

    return {
      ...formatMember({ workspace, session, member }),
      __typename: "Me",
      positionId: position?.id,
      workspaceId: session.workspaceId,
      botScopes: bot?.scopes,
      intercomHash: hash,
      shouldReceiveIntercomPricingMessage:
        !!member.firstClickedProPlanAt &&
        workspace.billing.subscription.type === "trial" &&
        new Date().getTime() >
          member.firstClickedProPlanAt.getTime() + messageDelay,
    };
  },

  async policy(_parent, _args, { repository, session }) {
    const workspace = await getSessionWorkspace(repository, session);

    return formatPolicy(workspace);
  },

  async referralSettings(_parent, _args, { repository, session }) {
    const workspace = await getSessionWorkspace(repository, session);

    return formatReferralSettings(workspace);
  },

  async reports(_parent, _args, { repository, session }) {
    const workspace = await getSessionWorkspace(repository, session);

    return { enabledReports: workspace.reports.enabled, id: workspace.id };
  },

  async slackIntegration(_parent, _args, { repository, session }) {
    const workspace = await getSessionWorkspace(repository, session);

    return {
      enabledSlackIntegration: workspace.slackIntegration,
      id: workspace.id,
    };
  },

  async slackPermissionsIssues(_parent, _args, { repository, session }) {
    const workspace = await getSessionWorkspace(repository, session);

    return formatSlackPermissionsIssues(workspace, session);
  },

  async channels(_parent, _args, { logger, repository, session }) {
    let workspace = await getSessionWorkspace(repository, session);

    // This handles the case if our user is the fastest hand in the West,
    // and reaches a channels select before our background worker pulls
    // the channels list out from Slack.
    //
    // `repository.setWorkspace` isn't called intentionally, because queries
    // should remain immutable when possible.
    if (workspace.channels.length === 0) {
      assert(workspace.slackBotToken, "Expected Slack bot token to exist");
      const slackAdapter = getSlackAdapter(workspace.slackBotToken);

      try {
        workspace = await pullChannelsFromSlack(slackAdapter, workspace);
      } catch (error) {
        logger.error(error);
      }
    }

    return [...workspace.channels].sort((a, b) => a.name.localeCompare(b.name));
  },

  async predictedOrgTree(_parent, _args, { repository, session }) {
    const workspace = await getSessionWorkspace(repository, session);

    return formatPredictedOrgChart(workspace);
  },

  async member(_parent, { id }, { repository, session }) {
    const workspace = await getSessionWorkspace(repository, session);
    const { members } = workspace;

    const member = members.find((m) => m.id === id);

    assert(member, `Expected member with id "${id}" to exist`);
    const permission = hasPermission(workspace, session, undefined, id);
    const formatedMember = formatMember({
      workspace,
      session,
      member,
      permission,
    });
    const memberPosition = getNode(
      workspace.orgTree.rootNode,
      (n): n is DomainPosition =>
        n.type === "position" && n.memberId === member.id
    );

    return {
      ...formatedMember,
      position: memberPosition
        ? formatPosition(workspace, session, memberPosition)
        : null,
    };
  },

  members(_parent, _args, { repository, session }) {
    const missingFields = _args.missingFields as string[] | null | undefined;

    return getMembers(repository, session, _args.isAdmin, missingFields);
  },

  // eslint-disable-next-line @typescript-eslint/require-await
  async allKnownNodes() {
    throw new Error("Not implemented");
  },
};

const Mutation: Required<MutationResolvers<Context>> = {
  ...AnnouncementsMutation,
  ...HolidaysMutation,
  ...KudosMutation,
  ...LinksMutation,
  ...NotificationsMutation,
  ...OnboardingMutation,
  ...OrgTreeMutation,
  ...PricingMutation,
  ...SurveysMutation,
  ...TeamsMutation,
  ...TimeOffsMutation,

  async toggleSlackIntegration(
    _parent,
    { slackIntegration },
    { repository, session }
  ) {
    let workspace = await getSessionWorkspace(repository, session);

    workspace = {
      ...workspace,
      slackIntegration,
    };

    await repository.setWorkspace(workspace);

    return {
      enabledSlackIntegration: workspace.slackIntegration,
      id: workspace.id,
    };
  },

  async editTemplate(_parent, args, { repository, session }) {
    const workspace = await getSessionWorkspace(repository, session);
    const templates = [...workspace.celebrationSettings[args.type].templates];

    const currentTemplateIndex = templates.findIndex((t) => t.id === args.id);

    assert(currentTemplateIndex !== -1, "Expect template is exist");

    templates[currentTemplateIndex] = {
      ...templates[currentTemplateIndex],
      text: args.text,
    };

    await repository.setWorkspace({
      ...workspace,
      celebrationSettings: {
        ...workspace.celebrationSettings,
        [args.type]: {
          ...workspace.celebrationSettings[args.type],
          templates,
        },
      },
    });

    return templates[currentTemplateIndex];
  },

  async removeTemplate(_parent, args, { repository, session }) {
    const workspace = await getSessionWorkspace(repository, session);

    const currentTemplate = workspace.celebrationSettings[
      args.type
    ].templates.find((t) => t.id === args.id);

    assert(currentTemplate, "Expect template is exist");

    const newTemplates = workspace.celebrationSettings[
      args.type
    ].templates.filter((t) => t.id !== args.id);

    await repository.setWorkspace({
      ...workspace,
      celebrationSettings: {
        ...workspace.celebrationSettings,
        [args.type]: {
          ...workspace.celebrationSettings[args.type],
          templates: newTemplates,
        },
      },
    });

    return currentTemplate;
  },

  async setCelebrationSettings(_parent, { settings }, { repository, session }) {
    let workspace = await getSessionWorkspace(repository, session);

    if (settings.channel) {
      assert(
        workspace.channels.some((ch) => ch.id === settings.channel!.id),
        "Expected channel to exist"
      );
    }

    workspace = {
      ...workspace,
      celebrationSettings: {
        ...workspace.celebrationSettings,

        anniversary: {
          changeSlackStatus:
            settings.anniversaryChangeSlackStatus ??
            workspace.celebrationSettings.anniversary.changeSlackStatus,
          enabled:
            settings.anniversaryEnabled ??
            workspace.celebrationSettings.anniversary.enabled,
          includeGif:
            settings.anniversaryGifsEnabled ??
            workspace.celebrationSettings.anniversary.includeGif,
          templates: settings.anniversaryTemplate
            ? [
                ...workspace.celebrationSettings.anniversary.templates,
                {
                  id: crypto.randomBytes(10).toString("hex"),
                  text: settings.anniversaryTemplate,
                },
              ]
            : workspace.celebrationSettings.anniversary.templates,
        },

        birthday: {
          changeSlackStatus:
            settings.birthdaysChangeSlackStatus ??
            workspace.celebrationSettings.birthday.changeSlackStatus,
          enabled:
            settings.birthdaysEnabled ??
            workspace.celebrationSettings.birthday.enabled,
          includeGif:
            settings.birthdaysGifsEnabled ??
            workspace.celebrationSettings.birthday.includeGif,
          templates: settings.birthdayTemplate
            ? [
                ...workspace.celebrationSettings.birthday.templates,
                {
                  id: crypto.randomBytes(10).toString("hex"),
                  text: settings.birthdayTemplate,
                },
              ]
            : workspace.celebrationSettings.birthday.templates,
        },

        common: {
          channel: settings.channel
            ? settings.channel.id
            : workspace.celebrationSettings.common.channel,
          timeOfPosting:
            settings.timeOfPosting ??
            workspace.celebrationSettings.common.timeOfPosting,
          timezone:
            settings.timeZone ??
            workspace.celebrationSettings.common.timezone ??
            "GMT +00:00",
          whenToPostIfHappensOnWeekend:
            (settings.whenToPostIfHappensOnWeekend as
              | CelebrationSettingsDomain["common"]["whenToPostIfHappensOnWeekend"]
              | undefined) ??
            workspace.celebrationSettings.common.whenToPostIfHappensOnWeekend,
        },
        reminders: [
          ...workspace.celebrationSettings.reminders,
        ] as CelebrationSettingsDomain["reminders"],
        sentAnnouncements: [
          ...(workspace.celebrationSettings
            .sentAnnouncements as CelebrationSettingsDomain["sentAnnouncements"]),
        ],
      },
    };

    await repository.setWorkspace(workspace);

    return formatCelebrationSettings(workspace);
  },

  async toggleRequired(_parent, { id: fieldId }, { repository, session }) {
    let workspace = await getSessionWorkspace(repository, session);

    assert(fieldId !== PresetPolicyId.MANAGER, "Can't toggle manager required");
    assert(
      fieldId !== PresetPolicyId.JOB_TITLE,
      "Can't toggle Job title required"
    );

    if (
      fieldId === PresetPolicyId.PHOTO_URL ||
      fieldId === PresetPolicyId.PHONE ||
      fieldId === PresetPolicyId.BIRTHDAY ||
      fieldId === PresetPolicyId.ANNIVERSARY ||
      fieldId === PresetPolicyId.COUNTRY
    ) {
      workspace = {
        ...workspace,
        policy: {
          ...workspace.policy,
          [fieldId]: {
            ...workspace.policy[fieldId],
            required: !workspace.policy[fieldId].required,
          },
        },
      };
    } else {
      workspace = {
        ...workspace,
        policy: {
          ...workspace.policy,
          customPolicyFields: workspace.policy.customPolicyFields.map(
            (customPolicyField) =>
              customPolicyField.id === fieldId
                ? {
                    ...customPolicyField,
                    required: !customPolicyField.required,
                  }
                : customPolicyField
          ),
        },
      };
    }
    await repository.setWorkspace(workspace);

    const policy = formatPolicy(workspace);

    return policy.fields.find((field) => field.id === fieldId)!;
  },

  async addField(
    _parent,
    { label, type, publiclyAvailable },
    { repository, session }
  ) {
    let workspace = await getSessionWorkspace(repository, session);

    const maxOrder = workspace.policy.customPolicyFields.reduce(
      (accOrder, field) => (field.order > accOrder ? field.order : accOrder),
      0
    );

    workspace = {
      ...workspace,
      policy: {
        ...workspace.policy,
        customPolicyFields: [
          ...workspace.policy.customPolicyFields,
          {
            id: crypto.randomUUID(),
            label,
            type: type as PolicyFieldType,
            publiclyAvailable,
            required: true,
            order: maxOrder + 1,
            slackFieldId: null,
          },
        ],
      },
    };

    await repository.setWorkspace(workspace);

    return formatPolicy(workspace);
  },

  async editField(
    _parent,
    { id, label, type, publiclyAvailable },
    { repository, session }
  ) {
    let workspace = await getSessionWorkspace(repository, session);

    workspace = {
      ...workspace,
      policy: {
        ...workspace.policy,
        customPolicyFields: workspace.policy.customPolicyFields.map((field) =>
          field.id !== id
            ? field
            : {
                ...field,
                label,
                type: type as PolicyFieldType,
                publiclyAvailable,
              }
        ),
      },
    };

    /*
      Private field must not be visible for sharing and links
    */
    if (!publiclyAvailable) {
      workspace = {
        ...workspace,
        links: workspace.links.map((link) => ({
          ...link,
          visibleFields: link.visibleFields.filter((fieldId) => fieldId !== id),
        })),
      };
    }

    await repository.setWorkspace(workspace);

    return formatPolicy(workspace);
  },

  async deleteField(_parent, { id }, { repository, session }) {
    let workspace = await getSessionWorkspace(repository, session);

    workspace = {
      ...workspace,
      policy: {
        ...workspace.policy,
        customPolicyFields: workspace.policy.customPolicyFields.filter(
          (field) => field.id !== id
        ),
      },
    };

    await repository.setWorkspace(workspace);

    return formatPolicy(workspace);
  },

  async changeSlackField(
    _parent,
    { id: fieldId, slackFieldId },
    { repository, session }
  ) {
    let workspace = await getSessionWorkspace(repository, session);

    assert(
      fieldId !== PresetPolicyId.PHOTO_URL,
      "Can't change photo slack field"
    );

    if (
      fieldId === PresetPolicyId.MANAGER ||
      fieldId === PresetPolicyId.JOB_TITLE ||
      fieldId === PresetPolicyId.PHONE ||
      fieldId === PresetPolicyId.BIRTHDAY ||
      fieldId === PresetPolicyId.ANNIVERSARY ||
      fieldId === PresetPolicyId.DEPARTMENT ||
      fieldId === PresetPolicyId.TEAMS ||
      fieldId === PresetPolicyId.COUNTRY
    ) {
      workspace = {
        ...workspace,
        policy: {
          ...workspace.policy,
          [fieldId]: {
            ...workspace.policy[fieldId],
            slackFieldId: slackFieldId ?? null,
          },
        },
      };
    } else {
      workspace = {
        ...workspace,
        policy: {
          ...workspace.policy,
          customPolicyFields: workspace.policy.customPolicyFields.map(
            (customPolicyField) =>
              customPolicyField.id === fieldId
                ? {
                    ...customPolicyField,
                    slackFieldId: (slackFieldId ??
                      null) as unknown as ReadonlyDeep<string | null>,
                  }
                : customPolicyField
          ),
        },
      };
    }

    await repository.setWorkspace(workspace);

    return formatPolicy(workspace).fields.find(
      (field) => field.id === fieldId
    )!;
  },

  async notifySlackAboutReferralPosition(
    _parent,
    _args,
    { repository, session }
  ) {
    let workspace = await getSessionWorkspace(repository, session);

    assert(workspace.slackBotToken, "Expected Slack bot token to exist");

    const slackAdapter = getSlackAdapter(workspace.slackBotToken);

    const getTime = (): Date => new Date();

    workspace = await sendDigestsPosition(slackAdapter, workspace, getTime);
    await repository.setWorkspace(workspace);
  },
  async askPrimaryOwnerToApprove(_parent, _args, { repository, session }) {
    let member = null;
    let workspace = null;
    const senderId = session.memberId;

    workspace = await getSessionWorkspace(repository, session);

    assert(workspace.slackBotToken, "Expected Slack bot token to exist");

    const slackAdapter = getSlackAdapter(workspace.slackBotToken);
    const app = slackAdapter.getSlackBotApplicationByAppId(
      workspace.slackBotToken.appId
    );

    member = workspace.members.find((m) => m.isSlackWorkspacePrimaryOwner);
    const senderMember = workspace.members.find((m) => m.id === senderId);

    assert(member, "Expected admin member to exist");
    assert(senderMember, "Expected sender member to exist");

    const result = await slackAdapter.askPrimaryOwnerToApprove(
      workspace.id,
      member.id,
      senderId,
      app
    );

    member = {
      ...member,
      botState: {
        ...member.botState,
        sentNotifications: [
          ...(member.botState.sentNotifications ?? []),
          {
            tag: "owner-approve" as const,
            medium: "slack" as const,
            timestamp: +new Date(),
            ts: result.ts,
          },
        ],
      },
    };

    workspace = replaceMember(workspace, member);

    await repository.setWorkspace(workspace);

    return formatSlackPermissionsIssues(workspace, session);
  },

  async updateMemberProfile(
    _parent,
    { profile: rawProfile },
    { logger, repository, session }
  ) {
    let workspace = await getSessionWorkspace(repository, session);

    const profile = normalizeProfile(rawProfile);
    const {
      memberId,
      title,
      realName,
      email,
      notes,
      customField,
      hideBirthday,
    } = profile;

    logger = logger.withContext({ workspace, profile });

    let member = workspace.members.find((m) => m.id === memberId);

    assert(member, `Member not found (id: "${memberId}")`);

    const userToken = findTokenForSlackAdapterMemberUpdate(workspace, member);

    const slackAdapter = workspace.slackBotToken
      ? getSlackAdapter(workspace.slackBotToken)
      : null;

    if (customField) {
      const data = updateMemberCustomFields(
        workspace,
        member,
        customField,
        logger,
        slackAdapter
      );

      workspace = data.workspace;
      member = data.member;
    }

    // * Updating member slack profile
    if (workspace.slackIntegration) {
      if (title || realName || email || customField) {
        if (userToken && slackAdapter) {
          void (async () => {
            try {
              await slackAdapter.updateMember(
                workspace.id,
                {
                  memberId,
                  ...(realName && { realName }),
                  ...(email && { email }),
                  customFields: await formatCustomFieldsForSlackUpdate(
                    workspace,
                    member,
                    slackAdapter
                  ),
                },
                workspace.policy,
                userToken
              );
            } catch (error) {
              logger.error(error);
            }
          })();
        }
      }
    }

    member = {
      ...member,
      hideBirthday: hideBirthday ?? member.hideBirthday,
      realName: realName ?? member.realName,
      email: email ?? member.email,
      notes: notes ?? member.notes,
    };

    workspace = replaceMember(workspace, member);

    await repository.setWorkspace(workspace);

    const formattedMember = formatMember({
      workspace,
      session,
      member,
      permission: hasPermission(workspace, session, undefined, member.id),
    });

    const memberPosition = getNode(
      workspace.orgTree.rootNode,
      (n): n is DomainPosition =>
        n.type === "position" && n.memberId === member.id
    );

    return {
      ...formattedMember,
      position: memberPosition
        ? formatPosition(workspace, session, memberPosition)
        : null,
    };
  },

  async makeNonAdmin(_parent, { memberId }, { repository, session }) {
    let workspace = await getSessionWorkspace(repository, session);

    assert(
      session.memberId !== memberId,
      "You cannot take away your administrator rights"
    );

    let member = workspace.members.find((m) => m.id === memberId);

    assert(member, `Member with id "${memberId}" does not exist`);

    member = {
      ...member,
      isAdmin: false,
    };

    workspace = replaceMember(workspace, member);

    await repository.setWorkspace(workspace);

    return formatMember({ workspace, session, member });
  },

  async makeAdmin(_parent, { memberId }, { repository, session }) {
    let workspace = await getSessionWorkspace(repository, session);

    const me = workspace.members.find((m) => {
      return m.id === session.memberId;
    });

    assert(me, `Expected me to exist (id: "${session.memberId}`);
    assert(workspace.slackBotToken, "Expected Slack bot token to exist");

    const slackAdapter = getSlackAdapter(workspace.slackBotToken);

    let member = workspace.members.find((m) => m.id === memberId);

    assert(member, `Member with id "${memberId}" does not exist`);

    const memberNotMe = me.id !== memberId;

    if (workspace.onboarding.completed && memberNotMe) {
      await slackAdapter.notifyAdminAboutAdminsRights(
        me.id,
        memberId,
        workspace.id
      );
    }

    member = {
      ...member,
      isAdmin: true,
    };

    workspace = replaceMember(workspace, member);

    await repository.setWorkspace(workspace);

    return formatMember({ workspace, session, member });
  },

  async setChannelForReferrals(_parent, args, { repository, session }) {
    let workspace = await getSessionWorkspace(repository, session);

    workspace = {
      ...workspace,
      reference: {
        ...workspace.reference,
        channel: args.channelId,
      },
    };

    await repository.setWorkspace(workspace);

    return formatReferralSettings(workspace);
  },

  async changeReferrals(_parent, args, { repository, session }) {
    let workspace = await getSessionWorkspace(repository, session);

    workspace = {
      ...workspace,
      reference: {
        ...workspace.reference,
        isEnable: args.enabledReferrals,
      },
    };

    await repository.setWorkspace(workspace);

    return formatReferralSettings(workspace);
  },

  async changeNotificationsPolicy(_parent, args, { repository, session }) {
    let workspace = await getSessionWorkspace(repository, session);

    const oldNotificationsPolicy = workspace.policy.notifications;

    workspace = {
      ...workspace,
      policy: {
        ...workspace.policy,
        notifications: {
          ...oldNotificationsPolicy,
          enable: args.enable ?? oldNotificationsPolicy.enable,
          followUpMessage:
            args.followUpMessage ?? oldNotificationsPolicy.followUpMessage,
          schedule:
            (args.schedule as Schedule | undefined) ??
            oldNotificationsPolicy.schedule,
          welcomeMessage:
            args.welcomeMessage ?? oldNotificationsPolicy.welcomeMessage,
        },
      },
    };

    await repository.setWorkspace(workspace);

    return formatPolicy(workspace);
  },

  async requestOrgTreePrediction(
    _parent,
    _args,
    { logger, repository, session }
  ) {
    let workspace = await getSessionWorkspace(repository, session);

    logger = logger.withContext({ workspace });

    assert(
      !workspace.predictedOrgTree.requested &&
        !workspace.predictedOrgTree.rootNode,
      "Cannot request org tree prediction twice"
    );
    assert(
      canPredictOrgChart(workspace),
      "Forbidden to predict org chart for workspace"
    );

    logger.info("Requested org chart prediction");

    workspace = {
      ...workspace,
      predictedOrgTree: {
        requested: true,
      },
    };

    await repository.setWorkspace(workspace);

    return formatPredictedOrgChart(workspace);
  },

  async changeReports(_parent, args, { repository, session }) {
    let workspace = await getSessionWorkspace(repository, session);

    const oldReports = workspace.reports;

    workspace = {
      ...workspace,
      reports: {
        enabled: args.enabledReports ?? oldReports.enabled,
      },
    };

    await repository.setWorkspace(workspace);

    return { enabledReports: workspace.reports.enabled, id: workspace.id };
  },

  async collectEmail(_parent, args, { productOwnerNotifier }) {
    await productOwnerNotifier.handleEmailCollection({
      fullName: args.fullName,
      email: args.email,
      companyName: args.companyName,
    });
  },
};

function formatCelebrationSettings(
  workspace: ReadonlyDeep<DomainWorkspace>
): CelebrationSettings {
  const channel = workspace.channels.find(
    (ch) => ch.id === workspace.celebrationSettings.common.channel
  );

  return {
    id: workspace.id,
    birthdaysEnabled: workspace.celebrationSettings.birthday.enabled,
    birthdaysGifsEnabled: workspace.celebrationSettings.birthday.includeGif,
    birthdaysChangeSlackStatus:
      workspace.celebrationSettings.birthday.changeSlackStatus,
    anniversaryEnabled: workspace.celebrationSettings.anniversary.enabled,
    anniversaryGifsEnabled:
      workspace.celebrationSettings.anniversary.includeGif,
    anniversaryChangeSlackStatus:
      workspace.celebrationSettings.anniversary.changeSlackStatus,
    channel: channel ? { ...channel } : null,
    timeOfPosting: workspace.celebrationSettings.common.timeOfPosting,
    timeZone: workspace.celebrationSettings.common.timezone ?? "GMT +00:00",
    whenToPostIfHappensOnWeekend:
      workspace.celebrationSettings.common.whenToPostIfHappensOnWeekend,
    birthdayTemplates: [...workspace.celebrationSettings.birthday.templates],
    anniversaryTemplates: [
      ...workspace.celebrationSettings.anniversary.templates,
    ],
  };
}

function formatReferralSettings(
  workspace: ReadonlyDeep<DomainWorkspace>
): ReferralSettings {
  return {
    channel: workspace.channels.find(
      (channel) => channel.id === workspace.reference.channel
    ),
    id: workspace.id,
    isEnable: workspace.reference.isEnable,
    nextDigestAt:
      getNextReferralsDigestNotificationTime(workspace)?.toISOString(),
    message: workspace.reference.message,
  };
}

function formatPredictedOrgChart(
  workspace: ReadonlyDeep<DomainWorkspace>
): ReadonlyDeep<PredictedOrgChart> {
  return {
    status: workspace.predictedOrgTree.requested
      ? PredictedOrgChartStatus.Requested
      : workspace.predictedOrgTree.rootNode
      ? PredictedOrgChartStatus.Ready
      : canPredictOrgChart(workspace)
      ? PredictedOrgChartStatus.NotRequested
      : PredictedOrgChartStatus.NotAvailable,
  };
}

function formatSlackPermissionsIssues(
  workspace: ReadonlyDeep<DomainWorkspace>,
  session: Session
): SlackPermissionsIssues | null {
  const primaryOwner = workspace.members.find(
    (m) => m.isSlackWorkspacePrimaryOwner
  );

  assert(primaryOwner, `Expected primary owner to exist`);

  if (findTokenForSlackAdapterMemberUpdate(workspace, primaryOwner) != null) {
    return null;
  }

  const app = workspace.slackBotToken
    ? getSlackAdapter(workspace.slackBotToken).getSlackBotApplicationByAppId(
        workspace.slackBotToken.appId
      )
    : SlackBotApplication.ORGANICE;

  return {
    cannotUpdateRegularMembers: workspace.members
      .filter(
        (member) =>
          !member.isSlackWorkspaceAdmin &&
          findTokenForSlackAdapterMemberUpdate(workspace, member) == null
      )
      .map((member) => formatMember({ workspace, session, member })),
    cannotUpdateAdmins: workspace.members
      .filter(
        (member) =>
          member.isSlackWorkspaceAdmin &&
          !member.isSlackWorkspaceOwner &&
          findTokenForSlackAdapterMemberUpdate(workspace, member) == null
      )
      .map((member) => formatMember({ workspace, session, member })),
    cannotUpdateOwners: workspace.members
      .filter(
        (member) =>
          member.isSlackWorkspaceOwner &&
          !member.isSlackWorkspacePrimaryOwner &&
          findTokenForSlackAdapterMemberUpdate(workspace, member) == null
      )
      .map((member) => formatMember({ workspace, session, member })),
    cannotUpdatePrimaryOwner:
      findTokenForSlackAdapterMemberUpdate(workspace, primaryOwner) == null,
    primaryOwner: formatMember({ workspace, session, member: primaryOwner }),
    sentPrimaryOwnerApproveNotification:
      primaryOwner.botState.sentNotifications?.some(
        (notification) => notification.tag === "owner-approve"
      ) ?? false,
    installationLink: {
      [SlackBotApplication.ORGANICE]: `/api/slack/organice/oauth?${new URLSearchParams(
        {
          workspace_id: workspace.id,
        }
      ).toString()}`,
      [SlackBotApplication.KUDOS]: `/api/slack/kudos/oauth?${new URLSearchParams(
        {
          workspace_id: workspace.id,
        }
      ).toString()}`,
    }[app],
  };
}

function normalizeProfile(profile: MemberProfileInput): MemberProfileInput {
  return {
    ...profile,
    title: profile.title?.trim(),
    realName: profile.realName?.trim(),
    email: profile.email?.trim(),
    notes: profile.notes?.trim(),
    customField: profile.customField
      ? {
          ...profile.customField,
          message: profile.customField.message.trim(),
        }
      : undefined,
  };
}

Object.entries(Query).forEach(([key, resolver]) => {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  (Query as any)[key] = (
    parent: unknown,
    args: unknown,
    context: Context,
    info: unknown
  ) => {
    const logger = getLoggerFromSession(context.session);

    // eslint-disable-next-line @typescript-eslint/no-unsafe-call, @typescript-eslint/no-unsafe-return, @typescript-eslint/no-explicit-any
    return (resolver as any)(parent, args, context, info).catch(
      (error: unknown) => handleError(error, logger)
    );
  };
});

Object.entries(Mutation).forEach(([key, resolver]) => {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  (Mutation as any)[key] = (
    parent: unknown,
    args: unknown,
    context: Context,
    info: unknown
  ) => {
    const logger = getLoggerFromSession(context.session);

    // eslint-disable-next-line @typescript-eslint/no-unsafe-call, @typescript-eslint/no-unsafe-return, @typescript-eslint/no-explicit-any
    return (resolver as any)(parent, args, context, info).catch(
      (error: unknown) => handleError(error, logger)
    );
  };
});

export function handleError(error: unknown, logger: Logger): never {
  logger.error(error);

  if (error instanceof InvalidTokenError) {
    throw new GraphQLError("Invalid token", {
      extensions: { code: "FORBIDDEN" },
    });
  }

  throw error;
}

const resolvers: Resolvers<Context> = {
  CalendarEvent,
  IMemberField,
  Log,
  Member,
  Mutation,
  NotificationBlock,
  OrgTreeNode,
  PositionOrRootNode,
  Query,
  RecentMembers,
  Subordinate,
};

export default resolvers;
