/* eslint-disable @typescript-eslint/no-var-requires */
/* eslint-disable @typescript-eslint/no-unsafe-member-access */
/* eslint-disable @typescript-eslint/no-unsafe-call */
/* eslint-disable @typescript-eslint/no-unsafe-return */

// eslint-disable-next-line @typescript-eslint/naming-convention
const { withSentryConfig: _withSentryConfig } = require("@sentry/nextjs");

/** @type {import('next').NextConfig} */
const nextConfig = {
  // eslint-disable-next-line @typescript-eslint/require-await
  async headers() {
    return [
      {
        source: "/:all*(svg|jpg|png)",
        locale: false,
        headers: [
          {
            key: "Cache-Control",
            value: "public, max-age=3600, must-revalidate",
          },
        ],
      },
    ];
  },
  // eslint-disable-next-line @typescript-eslint/require-await
  async redirects() {
    /**
     * NOTE: as we don't know how many things (our docs, slack and etc) have
     * links to these pages we should keep them for a while and redirect them
     * to new addresses.
     */
    return [
      {
        source: "/features",
        destination: "/settings",
        permanent: true,
      },
      {
        source: "/settings/profile-fields",
        destination: "/settings/org-chart/profile-fields",
        permanent: true,
      },
      {
        source: "/settings/time-offs",
        destination: "/settings/time-offs/general",
        permanent: true,
      },
      {
        source: "/settings/birthdays-and-anniversaries",
        destination: "/settings/celebrations/general",
        permanent: true,
      },
      {
        source: "/settings/kudos",
        destination: "/settings/kudos/general",
        permanent: true,
      },
      {
        source: "/notifications/templates",
        destination: "/settings/notifications/templates",
        permanent: true,
      },
      {
        source: "/notifications",
        destination: "/settings/notifications",
        permanent: true,
      },
    ];
  },
  images: {
    domains: [
      "a.slack-edge.com",
      "avatars.slack-edge.com",
      "secure.gravatar.com",
      // API for random people
      "100k-faces.glitch.me",
    ],
    minimumCacheTTL: 3600,
  },
  webpack(config, { isServer, dev }) {
    config.module.rules.push({
      test: /\.svg$/i,
      issuer: /\.[jt]sx?$/,
      use: ["@svgr/webpack"],
    });

    if (!isServer) {
      /*
       * Don't resolve 'inherits' module on the client to prevent this error on build "Error: Can't resolve 'inherits'"
       * https://stackoverflow.com/questions/64926174/module-not-found-cant-resolve-fs-in-next-js-application
       */
      // eslint-disable-next-line no-param-reassign
      config.resolve.fallback.inherits = false;
    }

    /**
     * NOTE: these lines disables next caching all node_modules
     *
     * https://github.com/vercel/next.js/discussions/33929#discussioncomment-6978515
     */
    if (dev) {
      config.watchOptions = {
        followSymlinks: true,
      };

      config.snapshot.managedPaths = [];
    }

    return config;
  },
  rewrites() {
    return {
      fallback: [
        {
          // Specified at Kudos bot app manifest (production and stands)
          source: "/slack/kudos/events",
          destination: "/api/slack/kudos/events",
        },
        {
          // Specified at OrgaNice bot app manifest (production and stands)
          source: "/slack/events",
          destination: "/api/slack/organice/events",
        },
        {
          // Used at OrgaNice's Slack Marketplace page on "Add to Slack" button:
          // https://slack.com/marketplace/A043Y3T5X2R-organice-employee-engagement
          source: "/api/slack/oauth",
          destination: "/api/slack/kudos/oauth",
        },
        // Before using GraphiQl, set ONLINE_GRAPHIQL_ENDPOINT=http://localhost:3000/api/graphql to localStorage
        {
          source: "/graphiql",
          destination: `https://graphiql-online.com/graphiql`,
        },
        {
          source: "/voyager-view",
          destination: `https://graphiql-online.com/voyager-view`,
        },
        {
          source: "/dist/:path*",
          destination: `https://graphiql-online.com/dist/:path*`,
        },
      ],
    };
  },
  experimental: {
    // this will allow nextjs to resolve files (js, ts, css)
    // outside packages/app directory.
    externalDir: true,
  },
};

// Make sure adding Sentry options is the last code to run before exporting, to
// ensure that your source maps include changes from all other Webpack plugins

const withSentryConfig =
  process.env.NEXT_PUBLIC_SENTRY_ENVIRONMENT &&
  process.env.NEXT_PUBLIC_SENTRY_DSN_ADMIN_SITE
    ? _withSentryConfig
    : (x) => x;

// eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
module.exports = withSentryConfig(nextConfig, {
  hideSourceMaps: true,
  silent: true,
});
