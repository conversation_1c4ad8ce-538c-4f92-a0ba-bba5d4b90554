import { GetServerSidePropsContext, GetServerSidePropsResult } from "next";
import Image from "next/image";
import { useRouter } from "next/router";
import * as React from "react";

import Button from "../../components/Button";
import Input from "../../components/Input";
import { NextPageWithLayout } from "../../components/layouts/Layout";
import { useRedeemAppSumoCodeMutation } from "../../graphql/client.generated";
import { getBaseUrl } from "../../helpers/getBaseUrl";
import {
  generateOAuthUrl,
  redirectIfUnauthenticated,
} from "../../helpers/redirectIfUnauthenticated";

type Props =
  | {
      installed: true;
    }
  | { installed: false; installationUrl: string };

const RedeemAppSumoCodePage: NextPageWithLayout<Props> = (props) => {
  const [code, setCode] = React.useState("");
  const [redeemAppSumoCode, { loading, error }] =
    useRedeemAppSumoCodeMutation();
  const router = useRouter();

  return (
    <>
      <main className="flex h-screen w-screen flex-col items-center justify-center bg-white">
        {props.installed ? (
          <div className="relative z-10 w-[570px] rounded-xl border border-main-300 bg-white py-6 px-8">
            <h1 className="text-4xl font-bold leading-snug">
              OrgaNice × AppSumo
            </h1>
            <p className="mt-6 text-base">
              Please enter your coupon code below and click Submit.
            </p>
            <form
              className="mt-3 flex items-start gap-2"
              onSubmit={(event) => {
                event.preventDefault();

                redeemAppSumoCode({
                  variables: {
                    code,
                  },
                })
                  .then(() => router.push("/settings/pricing"))
                  .catch(() => {});
              }}
            >
              <Input
                value={code}
                placeholder="Your coupon"
                wrapperClassnames="flex-grow"
                disabled={loading}
                onChange={(value) => {
                  setCode(value);
                }}
              />
              <Button size="s" disabled={loading} submit>
                Submit
              </Button>
            </form>
            {error ? (
              <div className="mt-3 flex gap-1.5 rounded-md bg-red-50 p-2 text-sm leading-6">
                <svg
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                  className="flex-shrink-0"
                >
                  <circle cx="12" cy="12" r="8" fill="#EF4444" />
                  <path
                    d="M11.4182 12.4548L11.2076 9.30351C11.1682 8.68947 11.1484 8.24868 11.1484 7.98114C11.1484 7.6171 11.2427 7.33421 11.4313 7.13245C11.6243 6.92631 11.8765 6.82324 12.1879 6.82324C12.5651 6.82324 12.8173 6.95482 12.9445 7.21798C13.0717 7.47675 13.1353 7.85175 13.1353 8.34298C13.1353 8.63245 13.1199 8.92631 13.0892 9.22456L12.8063 12.468C12.7756 12.8539 12.7098 13.15 12.609 13.3561C12.5081 13.5623 12.3414 13.6653 12.109 13.6653C11.8721 13.6653 11.7076 13.5667 11.6155 13.3693C11.5234 13.1675 11.4576 12.8627 11.4182 12.4548ZM12.1484 16.7838C11.8809 16.7838 11.6462 16.6982 11.4445 16.5272C11.2471 16.3518 11.1484 16.1083 11.1484 15.7969C11.1484 15.525 11.2427 15.2947 11.4313 15.1061C11.6243 14.9132 11.859 14.8167 12.1353 14.8167C12.4116 14.8167 12.6462 14.9132 12.8392 15.1061C13.0366 15.2947 13.1353 15.525 13.1353 15.7969C13.1353 16.1039 13.0366 16.3452 12.8392 16.5206C12.6419 16.696 12.4116 16.7838 12.1484 16.7838Z"
                    fill="white"
                  />
                </svg>
                {error.message}
              </div>
            ) : null}
          </div>
        ) : (
          <div className="relative z-10 w-[570px] rounded-xl border border-main-300 bg-white py-6 px-8">
            <h1 className="text-4xl font-bold leading-snug">
              OrgaNice × AppSumo
            </h1>
            <p className="mt-6 text-base">
              Hi there! 👋
              <br />
              It looks like you haven’t installed OrgaNice yet. Please press the
              button below to add the bot to your Slack workspace.
            </p>
            <a href={props.installationUrl}>
              <Button className="mt-6" size="m" fullWidth>
                Add OrgaNice to Slack
              </Button>
            </a>
          </div>
        )}
      </main>

      <Image
        alt="logo"
        src="/svg/logo-labeled.svg"
        width="140"
        height="34"
        className="absolute top-5 left-6"
      />
      <svg
        width="806"
        height="500"
        viewBox="0 0 806 500"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        className="absolute top-0 left-0"
      >
        <path
          d="M-727 -265.489C-727 -507.506 -659.719 -695.929 -525.157 -830.757C-390.594 -965.586 -203.217 -1033 36.9769 -1033C283.226 -1033 472.958 -966.597 606.175 -833.791C739.392 -701.659 806 -516.27 806 -277.623C806 -104.369 776.733 37.8755 718.198 149.109C660.336 259.668 576.235 345.958 465.894 407.979C356.226 469.327 219.309 500 55.1428 500C-111.714 500 -249.977 473.371 -359.645 420.114C-468.641 366.857 -557.115 282.589 -625.069 167.311C-693.023 52.0325 -727 -92.2341 -727 -265.489ZM-269.825 -263.466C-269.825 -113.807 -242.24 -6.28101 -187.069 59.1108C-131.226 124.503 -55.5346 157.199 40.0046 157.199C138.235 157.199 214.263 125.177 268.088 61.1333C321.912 -2.91028 348.825 -117.852 348.825 -283.691C348.825 -423.238 320.567 -525.034 264.051 -589.077C208.207 -653.795 132.18 -686.154 35.9677 -686.154C-56.2074 -686.154 -130.217 -653.458 -186.06 -588.066C-241.903 -522.674 -269.825 -414.474 -269.825 -263.466Z"
          fill="#F5F3FE"
        />
      </svg>
      <svg
        width="802"
        height="552"
        viewBox="0 0 802 552"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        className="absolute bottom-0 right-0"
      >
        <path
          d="M0 767.511C0 525.494 67.2811 337.071 201.843 202.243C336.406 67.4142 523.783 0 763.977 0C1010.23 0 1199.96 66.403 1333.18 199.209C1466.39 331.341 1533 516.73 1533 755.377C1533 928.631 1503.73 1070.88 1445.2 1182.11C1387.34 1292.67 1303.24 1378.96 1192.89 1440.98C1083.23 1502.33 946.309 1533 782.143 1533C615.286 1533 477.023 1506.37 367.355 1453.11C258.359 1399.86 169.885 1315.59 101.931 1200.31C33.977 1085.03 0 940.766 0 767.511ZM457.175 769.534C457.175 919.193 484.76 1026.72 539.931 1092.11C595.774 1157.5 671.465 1190.2 767.005 1190.2C865.235 1190.2 941.263 1158.18 995.088 1094.13C1048.91 1030.09 1075.82 915.148 1075.82 749.309C1075.82 609.762 1047.57 507.966 991.051 443.923C935.207 379.205 859.18 346.846 762.968 346.846C670.793 346.846 596.783 379.542 540.94 444.934C485.097 510.326 457.175 618.526 457.175 769.534Z"
          fill="#F5F3FE"
        />
      </svg>
    </>
  );
};

export async function getServerSideProps(
  ctx: GetServerSidePropsContext
): Promise<GetServerSidePropsResult<Props>> {
  if (await redirectIfUnauthenticated(ctx)) {
    const applyCodeUrl = new URL(
      ctx.req.url ?? "/appsumo/redeem-code",
      getBaseUrl(ctx.req)
    );

    return {
      props: {
        installed: false,
        installationUrl: (await generateOAuthUrl(ctx, applyCodeUrl)).toString(),
      },
    };
  }

  return { props: { installed: true } };
}

RedeemAppSumoCodePage.getHeader = () => null;
RedeemAppSumoCodePage.getNavigation = () => null;

export default RedeemAppSumoCodePage;
