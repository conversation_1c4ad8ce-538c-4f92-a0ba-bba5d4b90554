import { SlackBotApplication } from "@organice/core/domain";
import { prismaClient } from "@organice/core/prisma/PrismaClient";
import PrismaWorkspaceRepository from "@organice/core/prisma/PrismaWorkspaceRepository";
import { GetServerSidePropsContext, GetServerSidePropsResult } from "next";
import * as React from "react";

import { getAuthenticator } from "../helpers/getAuthenticator";
import { getBaseUrl } from "../helpers/getBaseUrl";
import { getSlackAdapter } from "../helpers/getSlackAdapter";

const GetPermissions: React.FC = () => null;

export async function getServerSideProps(
  ctx: GetServerSidePropsContext
): Promise<GetServerSidePropsResult<never>> {
  const authenticator = getAuthenticator(ctx.req, ctx.res);
  const session = await authenticator.getSession();
  let redirectUri = new URL("/api/slack/organice/oauth", getBaseUrl(ctx.req));

  const workspaceId =
    (ctx.params?.workspace as string | undefined) ?? session?.workspaceId;

  if (workspaceId) {
    const repository = new PrismaWorkspaceRepository(prismaClient);
    const workspace = await repository.getWorkspace(workspaceId);

    if (workspace?.slackBotToken) {
      const slackAdapter = getSlackAdapter(workspace.slackBotToken);
      const app = slackAdapter.getSlackBotApplicationByAppId(
        workspace.slackBotToken.appId
      );

      redirectUri = {
        [SlackBotApplication.ORGANICE]: new URL(
          "/api/slack/organice/oauth",
          getBaseUrl(ctx.req)
        ),
        [SlackBotApplication.KUDOS]: new URL(
          "/api/slack/kudos/oauth",
          getBaseUrl(ctx.req)
        ),
      }[app];
    }

    redirectUri.searchParams.append("workspace_id", workspaceId);
  }

  return {
    redirect: {
      destination: redirectUri.toString(),
      permanent: false,
    },
  };
}

export default GetPermissions;
