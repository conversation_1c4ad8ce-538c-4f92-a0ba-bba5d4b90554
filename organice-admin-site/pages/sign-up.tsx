import { GetServerSidePropsContext, GetServerSidePropsResult } from "next";
import Head from "next/head";
import Image from "next/image";
import { useState } from "react";

import Button from "../components/Button";
import DotBackground from "../components/DotBackground";
import Input from "../components/Input";
import { NextPageWithLayout } from "../components/layouts/Layout";
import { useCollectEmailMutation } from "../graphql/client.generated";
import { getAuthenticator } from "../helpers/getAuthenticator";

const SignUp: NextPageWithLayout = () => {
  const [view, setView] = useState<
    | "Create an Organice account"
    | "Sign up via Email"
    | "Thanks for your interest!"
  >("Create an Organice account");
  const [collectEmail] = useCollectEmailMutation();
  const [pending, setPending] = useState(false);
  const [fullName, setFullName] = useState("");
  const [email, setEmail] = useState("");
  const [companyName, setCompanyName] = useState("");

  const handleFormSubmit = (e: React.FormEvent<HTMLFormElement>): void => {
    e.preventDefault();
    setPending(true);
    void (async () => {
      await collectEmail({
        variables: {
          fullName,
          email,
          companyName,
        },
      });
      setView("Thanks for your interest!");
    })().finally(() => setPending(false));
  };

  return (
    <>
      <Head>
        <title>Create an Organice account</title>
      </Head>
      <main className="relative flex h-screen w-screen items-start overflow-y-auto bg-white">
        <div className="flex min-h-screen w-1/3 min-w-[580px] flex-col bg-slate-50">
          <div className="m-6">
            <Image
              className="cursor-pointer"
              alt="logo"
              src="/svg/logo-labeled.svg"
              width="140"
              height="30"
            />
          </div>

          <div className="pl-12 pr-20 pt-[160px]">
            {view === "Create an Organice account" ? (
              <>
                <h1 className="text-4xl font-bold">
                  Create an Organice account
                </h1>
                <p className="mt-8 text-slate-500">
                  Get started with your email or sign up via Slack.
                </p>
                <div className="mt-5 flex gap-3">
                  <Button
                    color="gray"
                    variant="outline"
                    size="m"
                    onClick={() => setView("Sign up via Email")}
                  >
                    <Image
                      alt="Slack"
                      src="/svg/mail.svg"
                      width="20"
                      height="20"
                      className="mr-2"
                    />
                    Sign up via Email
                  </Button>
                  <a href="/api/slack/kudos/oauth">
                    <Button color="gray" variant="outline" size="m">
                      <Image
                        alt="Slack"
                        src="/svg/slack.svg"
                        width="20"
                        height="20"
                        className="mr-2"
                      />
                      Add to Slack
                    </Button>
                  </a>
                </div>
              </>
            ) : view === "Sign up via Email" ? (
              <form onSubmit={handleFormSubmit}>
                <h1 className="text-4xl font-bold">Sign up via Email</h1>
                <div className="mt-8 mb-8 flex flex-col gap-6">
                  <Input
                    label="Full Name"
                    placeholder="Enter your full name"
                    required
                    type="text"
                    value={fullName}
                    onChange={(value) => setFullName(value)}
                  />
                  <Input
                    label="Email"
                    placeholder="Enter your email"
                    required
                    type="email"
                    value={email}
                    onChange={(value) => setEmail(value)}
                  />
                  <Input
                    type="text"
                    label="Company Name"
                    placeholder="Enter your company name"
                    required
                    value={companyName}
                    onChange={(value) => setCompanyName(value)}
                  />
                </div>
                <Button color="primary" size="m" submit disabled={pending}>
                  Sign Up
                </Button>
              </form>
            ) : (
              <>
                <h1 className="text-4xl font-bold">
                  Thanks for your interest!
                </h1>
                <p className="mt-8 text-slate-500">
                  Email sign-up isn’t available yet, but you’re on our early
                  access list. We’ll notify you as soon as it’s ready.
                </p>
                <div className="mt-5 flex gap-3">
                  <a href="/api/slack/kudos/oauth">
                    <Button color="gray" variant="outline" size="m">
                      <Image
                        alt="Slack"
                        src="/svg/slack.svg"
                        width="20"
                        height="20"
                        className="mr-2"
                      />
                      Add to Slack
                    </Button>
                  </a>
                </div>
              </>
            )}
          </div>
        </div>
        <div className="relative h-screen w-full overflow-auto bg-violet-600">
          <div className="absolute inset-0 z-10 flex justify-center p-12 [@media(min-height:900px)]:items-center">
            <div className="grid h-fit w-fit grid-cols-2 flex-wrap gap-5 [@media(min-width:1920px)]:grid-cols-3">
              <img
                alt="Al-Powered Org Chart Builder"
                src="/png/sign-up/1.png"
                width={372}
                height={281}
                className="w-[372px]"
              />
              <img
                alt="Track Time Offs"
                src="/png/sign-up/2.png"
                width={372}
                height={281}
                className="w-[372px]"
              />
              <img
                alt="Automate B-days & Anniversaries"
                src="/png/sign-up/3.png"
                width={372}
                height={281}
                className="w-[372px]"
              />
              <img
                alt="Create Kudos"
                src="/png/sign-up/4.png"
                width={372}
                height={281}
                className="w-[372px]"
              />
              <img
                alt="Create Rewards"
                src="/png/sign-up/5.png"
                width={372}
                height={281}
                className="w-[372px]"
              />
              <img
                alt="Surveys"
                src="/png/sign-up/6.png"
                width={372}
                height={281}
                className="w-[372px]"
              />
            </div>
          </div>
          <DotBackground fill="#A182E7" className="absolute top-0 left-0" />
        </div>
      </main>
    </>
  );
};

SignUp.getHeader = () => null;
SignUp.getNavigation = () => null;

export default SignUp;

export async function getServerSideProps(
  ctx: GetServerSidePropsContext
): Promise<GetServerSidePropsResult<unknown>> {
  const authenticator = getAuthenticator(ctx.req, ctx.res);
  const session = await authenticator.getSession();

  if (session) {
    return {
      redirect: {
        destination: "/",
        permanent: false,
      },
    };
  }

  return { props: {} };
}
