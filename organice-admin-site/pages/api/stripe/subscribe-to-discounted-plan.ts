import assert from "assert";

import { getBillableMembersCount } from "@organice/core/domain/billing";
import { prismaClient } from "@organice/core/prisma/PrismaClient";
import PrismaWorkspaceRepository from "@organice/core/prisma/PrismaWorkspaceRepository";
import StripeAdapterImpl from "@organice/core/stripe/StripeAdapterImpl";
import StripeClient from "@organice/core/stripe/StripeClient";
import { NextApiRequest, NextApiResponse } from "next";

import { getSessionWorkspace } from "../../../domain";
import { getAuthenticator } from "../../../helpers/getAuthenticator";
import { getBaseUrl } from "../../../helpers/getBaseUrl";

export default async function subscribeToDiscountedPlan(
  req: NextApiRequest,
  res: NextApiResponse
): Promise<void> {
  assert(
    process.env.STRIPE_PRODUCT_ID,
    "STRIPE_PRODUCT_ID env variable should be defined"
  );
  assert(
    process.env.STRIPE_SECRET_KEY,
    "STRIPE_SECRET_KEY env variable should be defined"
  );
  assert(
    process.env.STRIPE_WEBHOOK_SIGNING_SECRET,
    "STRIPE_WEBHOOK_SIGNING_SECRET env variable should be defined"
  );

  const authenticator = getAuthenticator(req, res);
  const session = await authenticator.getSession();

  if (!session) {
    const workspaceId = req.query.workspace;

    assert(typeof workspaceId === "string", "Expected 'workspace' query param");

    const callbackUri = new URL("/api/slack/organice/openid", getBaseUrl(req));

    callbackUri.searchParams.set("redirect_to", req.url!);

    res.statusCode = 307;
    res.setHeader("Location", callbackUri.toString());
    res.end();

    return;
  }

  const repository = new PrismaWorkspaceRepository(prismaClient);
  const workspace = await getSessionWorkspace(repository, session);
  const member = workspace.members.find((m) => m.id === session.memberId);

  assert(member, "Expected member to exist");

  const stripeClient = new StripeClient(process.env.STRIPE_SECRET_KEY);
  const stripeAdapter = new StripeAdapterImpl(
    stripeClient,
    process.env.STRIPE_PRODUCT_ID
  );
  const checkoutUrl = await stripeAdapter.generateCheckoutUrl({
    workspace: {
      id: workspace.id,
      name: workspace.name,
    },
    member: {
      id: member.id,
      email: member.email ?? "",
    },
    quantity: getBillableMembersCount(workspace),
    price: {
      discounted: true,
      interval: "year",
    },
    successUrl: new URL("/?success=true", getBaseUrl(req)).toString(),
  });

  res.statusCode = 307;
  res.setHeader("Location", checkoutUrl);
  res.end();
}
