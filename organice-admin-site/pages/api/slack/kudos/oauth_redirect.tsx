import assert from "assert";

import {
  AnotherOrgaNiceBotAlreadyInstalledError,
  LoggerContext,
  NoOpProductOwnerNotifier,
  SlackBotApplication,
} from "@organice/core/domain";
import { prismaClient } from "@organice/core/prisma/PrismaClient";
import PrismaInstallationStore from "@organice/core/prisma/PrismaInstallationStore";
import PrismaWorkspaceRepository from "@organice/core/prisma/PrismaWorkspaceRepository";
import SlackWebhookProductOwnerNotifier from "@organice/core/slack/SlackWebhookProductOwnerNotifier";
import { InstallProvider } from "@slack/oauth";
import { NextApiRequest, NextApiResponse } from "next";

import { Session } from "../../../../domain";
import { getAuthenticator } from "../../../../helpers/getAuthenticator";
import { getBaseUrl } from "../../../../helpers/getBaseUrl";
import { getLogger } from "../../../../helpers/getLogger";
import { getSlackAdapter } from "../../../../helpers/getSlackAdapter";

export default async (
  req: NextApiRequest,
  res: NextApiResponse
): Promise<void> => {
  assert(
    process.env.KUDOS_SLACK_APP_ID,
    "KUDOS_SLACK_APP_ID env variable should be defined"
  );
  assert(
    process.env.KUDOS_SLACK_CLIENT_ID,
    "KUDOS_SLACK_CLIENT_ID env variable should be defined"
  );
  assert(
    process.env.KUDOS_SLACK_CLIENT_SECRET,
    "KUDOS_SLACK_CLIENT_SECRET env variable should be defined"
  );
  assert(
    process.env.KUDOS_SLACK_SIGNING_SECRET,
    "KUDOS_SLACK_SIGNING_SECRET env variable should be defined"
  );
  assert(
    process.env.SLACK_STATE_SECRET,
    "SLACK_STATE_SECRET env variable should be defined"
  );

  const loggerContext: LoggerContext = {};
  const logger = getLogger(loggerContext);

  const installationStore = new PrismaInstallationStore({
    app: SlackBotApplication.KUDOS,
    appId: process.env.KUDOS_SLACK_APP_ID,
    clientId: process.env.KUDOS_SLACK_CLIENT_ID,
    clientSecret: process.env.KUDOS_SLACK_CLIENT_SECRET,
    logger,
    prismaClient,
    getRepository: () => new PrismaWorkspaceRepository(prismaClient),
    getSlackAdapter,
    getProductOwnerNotifier() {
      return process.env.SLACK_INSTALLATION_HANDLER_WEBHOOK_URL
        ? new SlackWebhookProductOwnerNotifier(
            process.env.SLACK_INSTALLATION_HANDLER_WEBHOOK_URL
          )
        : new NoOpProductOwnerNotifier();
    },
  });

  const installProvider = new InstallProvider({
    directInstall: true,
    clientId: process.env.KUDOS_SLACK_CLIENT_ID,
    clientSecret: process.env.KUDOS_SLACK_CLIENT_SECRET,
    stateSecret: process.env.SLACK_STATE_SECRET,
    authorizationUrl: process.env.SLACK_AUTHORIZATION_URL
      ? process.env.SLACK_AUTHORIZATION_URL
      : undefined,
    legacyStateVerification: process.env.NODE_ENV !== "production",
    clientOptions: {
      slackApiUrl: process.env.SLACK_API_URL
        ? process.env.SLACK_API_URL
        : undefined,
    },
    logger,
    installationStore,
  });

  let workspaceId: string | null = null;

  await installProvider.handleCallback(req, res, {
    // eslint-disable-next-line @typescript-eslint/require-await
    afterInstallation: async (installation) => {
      loggerContext.member = { id: installation.user.id };
      loggerContext.workspace = {
        id: (installation.team?.id ?? installation.enterprise?.id)!,
        name: installation.team?.name,
      };
      loggerContext.botToken = installation.bot?.token;

      workspaceId = (installation.team?.id ?? installation.enterprise?.id)!;

      return true;
    },
    success: (installation) => {
      assert(installation.user.token, "Missing user token in installation");
      assert(installation.team?.id, "Missing team id in installation");

      const session: Session = {
        userToken: installation.user.token,
        workspaceId: installation.team.id,
        memberId: installation.user.id,
      };
      const authenticator = getAuthenticator(req, res);

      authenticator.setSession(session);

      res.redirect((req.query.redirect_to as string | undefined) ?? "/");
    },
    failure: (error) => {
      res.writeHead(500, { "Content-Type": "text/html; charset=utf-8" });

      if (error instanceof AnotherOrgaNiceBotAlreadyInstalledError) {
        const redirectUri = new URL(
          {
            [SlackBotApplication.ORGANICE]: "/api/slack/organice/oauth",
            [SlackBotApplication.KUDOS]: "/api/slack/kudos/oauth",
          }[error.alreadyInstalledApp],
          getBaseUrl(req)
        );

        redirectUri.searchParams.set(
          "redirect_uri",
          (req.query.redirect_uri as string | undefined) ?? "/"
        );

        if (workspaceId) {
          redirectUri.searchParams.set("workspace_id", workspaceId);
        }

        res.end(
          `<html>
            <head>
              <meta http-equiv="refresh" content="5;url=${redirectUri.toString()}" />
            </head>
            <body>
              <h1>You've already got another OrgaNice bot installed</h1>
              <p>You'll be redirected there. If it didn't happened automatically, <a href="${redirectUri.toString()}">click here</a>.</p>
            </body>
          </html>`
        );
      } else {
        res.end(
          `<html>
            <body>
              <h1>Oops, Something Went Wrong!</h1>
              <p>Please try again or <a href="https://calendly.com/andrew-fan/what-is-organice">contact us</a>.</p>
              <pre>${error.stack ?? error.message}</pre>
            </body>
          </html>`
        );
      }
    },
  });
};
