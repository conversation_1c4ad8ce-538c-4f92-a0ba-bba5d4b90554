import assert from "assert";

import { prismaClient } from "@organice/core/prisma/PrismaClient";
import PrismaWorkspaceRepository from "@organice/core/prisma/PrismaWorkspaceRepository";
import SlackOpenIdAuthenticator from "@organice/slack-bot/slack/SlackOpenIdAuthenticator";
import { NextApiRequest, NextApiResponse } from "next";

import { getBaseUrl } from "../../../../helpers/getBaseUrl";
import { getLogger } from "../../../../helpers/getLogger";

export default async (
  req: NextApiRequest,
  res: NextApiResponse
): Promise<void> => {
  assert(
    process.env.ORGANICE_SLACK_CLIENT_ID,
    "ORGANICE_SLACK_CLIENT_ID env variable should be defined"
  );
  assert(
    process.env.ORGANICE_SLACK_CLIENT_SECRET,
    "ORGANICE_SLACK_CLIENT_SECRET env variable should be defined"
  );
  assert(
    process.env.ORGANICE_SLACK_SIGNING_SECRET,
    "ORGANICE_SLACK_SIGNING_SECRET env variable should be defined"
  );
  assert(
    process.env.SLACK_STATE_SECRET,
    "SLACK_STATE_SECRET env variable should be defined"
  );
  assert(
    process.env.SLACK_ADMIN_SITE_URL,
    "SLACK_ADMIN_SITE_URL env variable should be defined"
  );
  assert(req.query.redirect_to, "'redirect_to' param is mandatory");

  const logger = getLogger({});
  const authenticator = new SlackOpenIdAuthenticator({
    clientId: process.env.ORGANICE_SLACK_CLIENT_ID,
    clientSecret: process.env.ORGANICE_SLACK_CLIENT_SECRET,
    signingSecret: process.env.ORGANICE_SLACK_SIGNING_SECRET,
    stateSecret: process.env.SLACK_STATE_SECRET,
    adminSiteUrl: process.env.SLACK_ADMIN_SITE_URL,
    slackApiUrl: process.env.SLACK_API_URL
      ? process.env.SLACK_API_URL
      : undefined,

    getRepository: () => new PrismaWorkspaceRepository(prismaClient),
    getLogger,
  });
  const workspaceId = new URL(
    req.query.redirect_to as string,
    getBaseUrl(req)
  ).searchParams.get("workspace");

  assert(
    workspaceId,
    "'workspace' param is mandatory within the 'redirect_to'"
  );

  const callbackUri = new URL(
    "/api/slack/organice/openid_redirect",
    getBaseUrl(req)
  );

  callbackUri.searchParams.set("redirect_to", req.query.redirect_to as string);

  assert(
    callbackUri.searchParams.get("redirect_to")?.startsWith("/"),
    "Open redirect is not allowed"
  );

  await authenticator.handleInstallPath(req, res, undefined, {
    redirectUri: callbackUri.toString(),
    teamId: workspaceId,
  });

  logger.info(`Redirected to the Slack OpenId`);
};
