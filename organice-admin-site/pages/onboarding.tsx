import assert from "assert";

import { prismaClient } from "@organice/core/prisma/PrismaClient";
import PrismaWorkspaceRepository from "@organice/core/prisma/PrismaWorkspaceRepository";
import cn from "classnames";
import { motion, AnimatePresence } from "framer-motion";
import { GetServerSidePropsContext, GetServerSidePropsResult } from "next";
import Head from "next/head";
import Image from "next/image";
import { useRouter } from "next/router";
import { useState } from "react";
import { useIntercom } from "react-use-intercom";

import DotBackground from "../components/DotBackground";
import FeaturePreview, {
  PreviewSize,
} from "../components/Onboarding/FeaturePreview";
import OnboardingForm from "../components/Onboarding/OnboardingForm";
import { NextPageWithLayout } from "../components/layouts/Layout";
import { useMixpanelContext } from "../context/MixpanelContext";
import { useSessionContext } from "../context/SessionContext";
import { getSessionWorkspace } from "../domain";
import {
  SupportedFeature,
  useRequestOrgTreePredictionMutation,
  useUpdateBookmarkedFeaturesMutation,
  useCompleteOnboardingMutation,
} from "../graphql/client.generated";
import { getAuthenticator } from "../helpers/getAuthenticator";
import { getLoggerFromSession } from "../helpers/getLogger";
import { redirectIfUnauthenticated } from "../helpers/redirectIfUnauthenticated";

const Previews = {
  [SupportedFeature.OrgChart]: [
    {
      title:
        "Generate your org chart from Slack workspace in just 2 clicks using AI",
      color: "#FACC15",
      size: PreviewSize.md,
      image: "/png/onboarding/org-chart/Org Chart 1.png",
    },
    {
      title: "Create a Welcoming Environment for Newcomers",
      color: "#D946EF",
      size: PreviewSize.sm,
      image: "/png/onboarding/org-chart/Org Chart 2.png",
    },
    {
      title: "Hire Faster with an Employee Referral Program",
      color: "#22C55E",
      size: PreviewSize.sm,
      image: "/png/onboarding/org-chart/Org Chart 3.png",
    },
  ],
  [SupportedFeature.TimeOffs]: [
    {
      title: "Customize and Automate Slack Status Updates",
      color: "#EF4444",
      size: PreviewSize.sm,
      image: "/png/onboarding/time-offs/Time Offs 1.png",
    },
    {
      title: "Turn Any Slack Channel into a Time Off Notifier",
      color: "#FACC15",
      size: PreviewSize.sm,
      image: "/png/onboarding/time-offs/Time Offs 2.png",
    },
    {
      title: "Stay on Top of Your Team's Absence Calendar",
      color: "#06B6D4",
      size: PreviewSize.md,
      image: "/png/onboarding/time-offs/Time Offs 3.png",
    },
  ],
  [SupportedFeature.Celebration]: [
    {
      title: "Send birthday and work anniversary celebrations messages",
      color: "#F59E0B",
      size: PreviewSize.md,
      image: "/png/onboarding/celebrations/Celebrations 1.png",
    },
    {
      title: "Receive notifications of upcoming anniversaries and birthdays",
      color: "#22C55E",
      size: PreviewSize.sm,
      image: "/png/onboarding/celebrations/Celebrations 2.png",
    },
    {
      title: "Use existing message templates or add your own",
      color: "#D946EF",
      size: PreviewSize.sm,
      image: "/png/onboarding/celebrations/Celebrations 3.png",
    },
  ],
  [SupportedFeature.Kudos]: [
    {
      title: "Feedback Friday in Slack",
      color: "#EF4444",
      size: PreviewSize.sm,
      image: "/png/onboarding/kudos/Kudos 1.png",
    },
    {
      title: "Get Valuable Insights for Performance Reviews",
      color: "#FACC15",
      size: PreviewSize.sm,
      image: "/png/onboarding/kudos/Kudos 2.png",
    },
    {
      title: "Give Kudos Easily, on Time",
      color: "#38CB6E",
      size: PreviewSize.md,
      image: "/png/onboarding/kudos/Kudos 3.png",
    },
  ],
  [SupportedFeature.Surveys]: [
    {
      title: "Build a Listening-Based Culture",
      color: "#FF3E80",
      size: PreviewSize.md,
      image: "/png/onboarding/surveys/Surveys 1.png",
    },
    {
      title: "Get 3x More Responses: Run Surveys where Work Happens",
      color: "#22C55E",
      size: PreviewSize.sm,
      image: "/png/onboarding/surveys/Surveys 2.png",
    },
    {
      title: "Get Your Stats Anytime with Our Handy Dashboard",
      color: "#D946EF",
      size: PreviewSize.sm,
      image: "/png/onboarding/surveys/Surveys 3.png",
    },
  ],
};

const TWO_DAYS_IN_SECONDS = 2 * 24 * 60 * 60;
const ONBOARDING_FOLLOW_UP_DELAY_IN_MINUTES =
  process.env.NEXT_PUBLIC_INTERCOM_ONBOARDING_FOLLOW_UP_DELAY_IN_MINUTES;
const STARTED_ONBOARDING_AGO = ONBOARDING_FOLLOW_UP_DELAY_IN_MINUTES
  ? TWO_DAYS_IN_SECONDS - Number(ONBOARDING_FOLLOW_UP_DELAY_IN_MINUTES) * 60
  : 0;

const PageOnboarding: NextPageWithLayout = () => {
  const { track } = useMixpanelContext();

  const [activePreivew, setActivePreview] = useState(SupportedFeature.OrgChart);
  const [
    updateBookmarkedFeatures,
    { loading: updateBookmarkedFeaturesLoading },
  ] = useUpdateBookmarkedFeaturesMutation();
  const [completeOnboarding, { loading: completeOnboardingLoading }] =
    useCompleteOnboardingMutation();
  const [
    requestOrgTreePrediction,
    { loading: requestOrgTreePredictionLoading },
  ] = useRequestOrgTreePredictionMutation();

  const router = useRouter();
  const { session } = useSessionContext();
  const { update } = useIntercom();

  return (
    <>
      <Head>
        <title>OnBoarding</title>
      </Head>
      <main className="relative flex h-screen w-screen items-start overflow-y-auto bg-white">
        <div className="flex min-h-screen w-1/3 min-w-[460px] flex-col bg-slate-50">
          <div className="m-6">
            <Image
              className="cursor-pointer"
              alt="logo"
              src="/svg/logo-labeled.svg"
              width="140"
              height="30"
            />
          </div>
          <OnboardingForm
            key={session ? "loading" : "ready"}
            initialFeatures={session?.onboarding.bookmarkedFeatures ?? []}
            setActivePreview={setActivePreview}
            loading={
              !session ||
              updateBookmarkedFeaturesLoading ||
              completeOnboardingLoading ||
              requestOrgTreePredictionLoading
            }
            onSubmit={async ({ features, useAI }) => {
              await updateBookmarkedFeatures({
                variables: {
                  features,
                },
                refetchQueries: ["Session"],
              });
              await completeOnboarding();

              if (useAI) {
                await requestOrgTreePrediction();
              }

              if (session?.me) {
                update({
                  userId: session.me.id,
                  userHash: session.me.intercomHash,
                  customAttributes: {
                    startedOnboardingAt:
                      Math.round(Date.now() / 1000) - STARTED_ONBOARDING_AGO,
                  },
                });
              }

              track("Onboarding - 'Thanks for signing up!' step");

              if (
                session?.billing.subscription.__typename ===
                "AppSumoSubscription"
              ) {
                router.push("/settings/pricing").catch(() => {});
              } else {
                router.push("/settings").catch(() => {});
              }
            }}
          />
        </div>
        <div className="relative h-full w-full overflow-auto bg-violet-600">
          <div className="flex min-h-full w-full items-center justify-center p-6">
            <AnimatePresence mode="wait">
              <motion.div
                key={activePreivew}
                className="relative z-10 grid w-full max-w-[760px] grid-cols-2 gap-6"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.25 }}
              >
                {Previews[activePreivew].map((preview, index) => (
                  <FeaturePreview
                    // eslint-disable-next-line react/no-array-index-key
                    key={index}
                    title={preview.title}
                    color={preview.color}
                    image={preview.image}
                    size={preview.size}
                    className={cn("h-[430px]", {
                      "col-span-2": preview.size === PreviewSize.md,
                    })}
                  />
                ))}
              </motion.div>
            </AnimatePresence>
          </div>
          <DotBackground fill="#A182E7" className="absolute top-0 left-0" />
        </div>
      </main>
    </>
  );
};

PageOnboarding.getHeader = () => null;
PageOnboarding.getNavigation = () => null;

export default PageOnboarding;

// eslint-disable-next-line @typescript-eslint/require-await
export async function getServerSideProps(
  ctx: GetServerSidePropsContext
): Promise<GetServerSidePropsResult<unknown>> {
  const redirect = await redirectIfUnauthenticated(ctx);

  if (redirect) {
    return redirect;
  }

  const authenticator = getAuthenticator(ctx.req, ctx.res);
  const session = await authenticator.getSession();

  assert(session, "Expected user to be authenticated");

  const repository = new PrismaWorkspaceRepository(prismaClient);
  let logger = getLoggerFromSession(session);
  const workspace = await getSessionWorkspace(repository, session);

  logger = logger.withContext({ workspace, url: ctx.req.url });

  if (workspace.onboarding.completed) {
    logger.info("Redirected to / as onboarding is completed");

    return {
      redirect: {
        destination: "/",
        permanent: false,
      },
    };
  }

  return { props: {} };
}
