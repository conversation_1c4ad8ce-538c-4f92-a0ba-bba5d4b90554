import React, { useState } from "react";

import { useSessionContext } from "../../context/SessionContext";
import { SupportedFeature } from "../../graphql/client.generated";
import getGreetings from "../../helpers/getGreetings";
import { useToast } from "../../hooks/useToast";
import Button from "../Button";
import Checkbox from "../Checkbox";
import Notification from "../Notification";

const Features = [
  {
    id: SupportedFeature.OrgChart,
    label: "Org Chart",
  },
  {
    id: SupportedFeature.TimeOffs,
    label: "Time Offs",
  },
  {
    id: SupportedFeature.Celebration,
    label: "Celebrations",
  },
  {
    id: SupportedFeature.Kudos,
    label: "Kudos",
  },
  {
    id: SupportedFeature.Surveys,
    label: "Surveys",
  },
];

interface Props {
  initialFeatures: SupportedFeature[];
  onSubmit: (data: {
    features: SupportedFeature[];
    useAI: boolean;
  }) => Promise<void>;
  setActivePreview: (feature: SupportedFeature) => void;
  loading?: boolean;
}

const OnboardingForm: React.FC<Props> = ({
  initialFeatures,
  onSubmit,
  setActivePreview,
  loading,
}) => {
  const toast = useToast();
  const { session } = useSessionContext();

  const [bookmarkedFeatures, setBookmarkedFeatures] =
    useState<SupportedFeature[]>(initialFeatures);

  const [useAI, setUseAI] = useState(false);

  return (
    <div className="relative mx-auto flex h-full w-full max-w-[454px] flex-1  items-center px-6 pb-20">
      <div>
        <h2 className="mb-8 text-3xl font-bold tracking-wider text-slate-700">
          {getGreetings(session?.me.realName)} Thanks for signing up! 🎉
        </h2>
        <p className="mb-5 tracking-wide text-slate-500">
          Pin features you want to start with:
        </p>
        <div className="mb-6">
          {Features.map((feature) => (
            <div className="mb-4" key={feature.id}>
              <div
                className="inline-block"
                onMouseEnter={() => setActivePreview(feature.id)}
              >
                <Checkbox
                  disabled={loading}
                  name={feature.id}
                  checked={bookmarkedFeatures.includes(feature.id)}
                  onChange={(e) => {
                    setBookmarkedFeatures((prev) => {
                      if (e.target.checked) {
                        return [...prev, feature.id];
                      }

                      return prev.filter((id) => id !== feature.id);
                    });
                  }}
                >
                  {feature.label}
                </Checkbox>

                {feature.id === SupportedFeature.OrgChart ? (
                  <Notification
                    type="info"
                    visible={bookmarkedFeatures.includes(feature.id)}
                    hideIcon
                    className="mt-4"
                  >
                    <Checkbox
                      name="org-chart-ai"
                      disabled={loading}
                      checked={useAI}
                      wrapperClassName="!items-start"
                      onChange={() => setUseAI(!useAI)}
                    >
                      <>
                        <div className="mb-2 text-base font-bold">
                          Would you like us to pre-build your Org Chart from
                          your Slack data using OrgaNice AI?
                        </div>
                        <div className="text-main-600">
                          We&apos;ll extract department information and
                          automatically organize your colleagues into their
                          respective departments using data from Slack. You can
                          further customize and edit your org chart afterward.
                        </div>
                      </>
                    </Checkbox>
                  </Notification>
                ) : null}
              </div>
            </div>
          ))}
        </div>

        <Button
          loading={loading}
          disabled={!bookmarkedFeatures.length}
          onClick={() => {
            if (bookmarkedFeatures.length) {
              onSubmit({
                features: bookmarkedFeatures,
                useAI:
                  bookmarkedFeatures.includes(SupportedFeature.OrgChart) &&
                  useAI,
              }).catch(() => {
                toast("error", "Oops! Something went wrong. Please try again.");
                setBookmarkedFeatures([]);
                setUseAI(false);
              });
            }
          }}
        >
          Setup OrgaNice
        </Button>
        <div className="mt-8 text-sm text-slate-900">
          <span>Need help?</span>
          <a
            className="ml-2 text-violet-600 hover:text-violet-400 hover:underline"
            href="https://calendly.com/andrew-fan/what-is-organice"
            target="_blank"
            rel="noreferrer"
          >
            Book a call with our team
          </a>
        </div>
      </div>
    </div>
  );
};

export default OnboardingForm;
