import { SlackBotApplication } from "@organice/core/domain";
import { prismaClient } from "@organice/core/prisma/PrismaClient";
import PrismaWorkspaceRepository from "@organice/core/prisma/PrismaWorkspaceRepository";
import { GetServerSidePropsContext, GetServerSidePropsResult } from "next";

import { getSessionWorkspace, InvalidTokenError } from "../domain";

import { getAuthenticator } from "./getAuthenticator";
import { getBaseUrl } from "./getBaseUrl";
import { getLoggerFromSession, getLogger } from "./getLogger";
import { getSlackAdapter } from "./getSlackAdapter";

export async function redirectIfUnauthenticated(
  ctx: GetServerSidePropsContext
): Promise<GetServerSidePropsResult<never> | null> {
  const authenticator = getAuthenticator(ctx.req, ctx.res);
  const session = await authenticator.getSession();

  if (!session) {
    const logger = getLogger({});

    logger.info(`Redirected to the Slack OAuth`, {
      headers: ctx.req.headers,
      url: ctx.req.url,
    });

    return redirectToAuthentication(ctx);
  }

  const repository = new PrismaWorkspaceRepository(prismaClient);
  const logger = getLoggerFromSession(session);

  try {
    await getSessionWorkspace(repository, session);
  } catch (error) {
    if (error instanceof InvalidTokenError) {
      logger.info("Invalid token found when visiting a page");
      authenticator.endSession();

      return redirectToAuthentication(ctx);
    }

    throw error;
  }

  return null;
}

async function redirectToAuthentication(
  ctx: GetServerSidePropsContext
): Promise<GetServerSidePropsResult<never>> {
  const targetUrl = new URL(ctx.req.url ?? "/", getBaseUrl(ctx.req));

  return {
    redirect: {
      destination: targetUrl.searchParams.get("workspace")
        ? (await generateOpenIdUrl(ctx, targetUrl)).toString()
        : (await generateOAuthUrl(ctx, targetUrl)).toString(),
      permanent: false,
    },
  };
}

export async function generateOAuthUrl(
  ctx: GetServerSidePropsContext,
  redirectUrl: URL
): Promise<URL> {
  let oauthUrl = new URL("/api/slack/organice/oauth", getBaseUrl(ctx.req));
  const workspaceId = new URL(
    ctx.req.url ?? "/",
    getBaseUrl(ctx.req)
  ).searchParams.get("workspace");

  if (workspaceId) {
    const repository = new PrismaWorkspaceRepository(prismaClient);
    const workspace = await repository.getWorkspace(workspaceId);

    if (workspace?.slackBotToken) {
      const slackAdapter = getSlackAdapter(workspace.slackBotToken);
      const app = slackAdapter.getSlackBotApplicationByAppId(
        workspace.slackBotToken.appId
      );

      oauthUrl = {
        [SlackBotApplication.ORGANICE]: new URL(
          "/api/slack/organice/oauth",
          getBaseUrl(ctx.req)
        ),
        [SlackBotApplication.KUDOS]: new URL(
          "/api/slack/kudos/oauth",
          getBaseUrl(ctx.req)
        ),
      }[app];
    }
  }

  oauthUrl.searchParams.set(
    "redirect_to",
    redirectUrl.pathname + redirectUrl.search
  );

  return oauthUrl;
}

async function generateOpenIdUrl(
  ctx: GetServerSidePropsContext,
  redirectUrl: URL
): Promise<URL> {
  let openIdUrl = new URL("/api/slack/organice/openid", getBaseUrl(ctx.req));
  const workspaceId = new URL(
    ctx.req.url ?? "/",
    getBaseUrl(ctx.req)
  ).searchParams.get("workspace");

  if (workspaceId) {
    const repository = new PrismaWorkspaceRepository(prismaClient);
    const workspace = await repository.getWorkspace(workspaceId);

    if (workspace?.slackBotToken) {
      const slackAdapter = getSlackAdapter(workspace.slackBotToken);
      const app = slackAdapter.getSlackBotApplicationByAppId(
        workspace.slackBotToken.appId
      );

      openIdUrl = {
        [SlackBotApplication.ORGANICE]: new URL(
          "/api/slack/organice/openid",
          getBaseUrl(ctx.req)
        ),
        [SlackBotApplication.KUDOS]: new URL(
          "/api/slack/kudos/openid",
          getBaseUrl(ctx.req)
        ),
      }[app];
    }
  }

  openIdUrl.searchParams.set(
    "redirect_to",
    redirectUrl.pathname + redirectUrl.search
  );

  return openIdUrl;
}
