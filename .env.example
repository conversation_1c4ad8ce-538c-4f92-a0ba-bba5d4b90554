# 1. Put your username into brackets
ORGANICE_SLACK_BOT_NAME=OrgaNice (MY NAME)
KUDOS_SLACK_BOT_NAME=<PERSON><PERSON> (MY NAME)

# 2. Issue an app configuration token here: https://api.slack.com/authentication/config-tokens
# Put the _refresh_ token below:
SLACK_CONFIG_REFRESH_TOKEN=

# 3. Run `npm start`, go to https://api.slack.com/apps, select your app there,
# and click "Generate Token and Scopes" within "App-Level Tokens" section.
# Give the token and arbitrary name, and grant access to `connections:write` scope.
# Copy the newly generated token here:
ORGANICE_SLACK_APP_TOKEN=
KUDOS_SLACK_APP_TOKEN=

# 4. Ask nikityy or andrewfan for this key.
CHATGPT_API_KEY=

# Everything that is below this line doesn't require setup or sets up automatically
# -----------------------------

# Clean next 6 variables if you're switching to a new bot
SLACK_CONFIG_ACCESS_TOKEN=
SLACK_CONFIG_ACCESS_TOKEN_EXPIRES_AT=
ORGANICE_SLACK_APP_ID=
ORGANICE_SLACK_CLIENT_ID=
ORGANICE_SLACK_CLIENT_SECRET=
ORGANICE_SLACK_SIGNING_SECRET=
KUDOS_SLACK_APP_ID=
KUDOS_SLACK_CLIENT_ID=
KUDOS_SLACK_CLIENT_SECRET=
KUDOS_SLACK_SIGNING_SECRET=

SLACK_STATE_SECRET=random-string
SLACK_ADMIN_SITE_URL=http://localhost:3000
SLACK_API_URL=
SLACK_AUTHORIZATION_URL=

# Set to X if you want to receive notifications every X minutes
# Leave empty if you want production behavior
NOTIFICATION_PERIOD_IN_MINUTES=1

# Set to X if you want trial mode to expire in X minutes
# Leave empty if you want production behavior
TRIAL_DURATION_IN_MINUTES=10

# Set to X if you want to receive a follow-up message about non-finished onboarding in Intercom in X minutes
# Leave empty if you want production behavior
NEXT_PUBLIC_INTERCOM_ONBOARDING_FOLLOW_UP_DELAY_IN_MINUTES=5

# Set to 1 if you want to run a local test version of Slack. It's convienient for performance testing.
# Leave empty to use real Slack.
SLACK_TEST_SERVER=

# Set to 1 if you want worker to re-run cycles immediately, without any time gap.
# Leave empty if you want production behavior
BACKGROUND_WORKER_NO_DELAY=1

# When OrgaNice gets installed into a new workspace, send a message to the specified channel.
# Webhook URL may be generated here: https://slack.com/apps/A0F7XDUAZ-incoming-webhooks?tab=more_info
SLACK_INSTALLATION_HANDLER_WEBHOOK_URL=

# Username, password, and database name should match to docker-compose.yml
POSTGRES_CONNECTION_URL=postgresql://organice:organice@localhost:5432/organice

# Email credentials for outgoing emails
SMTP_CONNECTION_URL=

# Crypto key (must be 32 characters long)
COOKIE_ENCRYPTION_KEY=01234567890123456789012345678901

# Sentry credentials
SENTRY_URL=https://sentry.io/
SENTRY_ORG=organice-f0
SENTRY_PROJECT=organice-admin
SENTRY_AUTH_TOKEN=
NEXT_PUBLIC_SENTRY_ENVIRONMENT=test
NEXT_PUBLIC_SENTRY_DSN_ADMIN_SITE=
SENTRY_DSN_BACKGROUND_WORKER=

# Mixpanel credentials
NEXT_PUBLIC_MIXPANEL_AUTH_TOKEN=bda160c4d41437819d9570b8588ba5f6
MIXPANEL_AUTH_TOKEN=bda160c4d41437819d9570b8588ba5f6

# Intercom credentials
NEXT_PUBLIC_INTERCOM_APP_ID=
INTERCOM_SECRET_KEY=

# Stripe credentials
# Without it you won't be able to receive Stripe Webhook events.
# See README.md "How to enable Stripe" section to obtain this secret.
STRIPE_WEBHOOK_SIGNING_SECRET=
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_51LZtC7LwBpuRF41K9ZWZeDx2U33zmrmQ2H2rz4nG8V89VEYgixnn9tVvl3RKVgfDx9q1KfVmo6FuLF1euDrJojkp00BMj93lNR
STRIPE_SECRET_KEY=sk_test_51LZtC7LwBpuRF41KKIEn505H1NJCqnWbzVqb9TYvwVR3uT3P0LfAmeFELQmkvKbTmps8H7N9FIpuzccfnXeYj2tW00bdCANxWi
STRIPE_PRODUCT_ID=prod_MLXanYSqOoKHg7

# LogRocket credentials
NEXT_PUBLIC_LOGROCKET_APP_ID=

# Token for our landing page API
# https://www.organice.app/birthday-message-generator
BIRTHDAY_GENERATOR_API_TOKEN=

# Ask nikityy for these ones if you wish to access production logs
DIGITALOCEAN_ACCESS_TOKEN=
GITHUB_TOKEN=
PULUMI_ACCESS_TOKEN=
PULUMI_CONFIG_PASSPHRASE=
