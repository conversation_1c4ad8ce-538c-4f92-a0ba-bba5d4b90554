import uvicorn
from fastapi import Fast<PERSON><PERSON>
from starlette.middleware.cors import CORSMiddleware

from api import root_router

from config import settings

app = FastAPI(
    title=settings.PROJECT_NAME,
    docs_url="/docs",
    redoc_url="/redoc",
    openapi_url="/openapi.json",
)

app.include_router(root_router)

# CORS origins - use FRONTEND_URL from settings
frontend_url = settings.get("FRONTEND_URL", "http://localhost:3000")
origins = [
    frontend_url,
    "http://localhost:8080",
]

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


if __name__ == "__main__":
    uvicorn.run(
        app, host="0.0.0.0", port=8000, log_level="info", reload=False, log_config=None
    )
