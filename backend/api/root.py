from fastapi import APIRouter, status
from pydantic import BaseModel
from sqlalchemy.ext.asyncio import AsyncSession
from starlette.responses import JSONResponse, RedirectResponse

from config import log
from db import get_session_dep
import models as m
import sqlalchemy as sa

root_router = APIRouter()


class HealthCheckResponse(BaseModel):
    status: str


@root_router.get("/", include_in_schema=False)
def index():
    return RedirectResponse("/docs")


@root_router.get(
    "/healthcheck", tags=["healthcheck"], response_model=HealthCheckResponse
)
async def healthcheck():
    return JSONResponse({"status": "ok"}, status.HTTP_200_OK)
