import assert from "node:assert";
import { spawnSync, SpawnSyncReturns } from "node:child_process";
import * as fs from "node:fs";
import * as path from "node:path";

import SlackAppManifest from "@organice/slack-bot/slack/SlackAppManifest";

void (async () => {
  const standSlug = process.env.STAND_SLUG;
  const slackConfigAccessToken = process.env.SLACK_CONFIG_ACCESS_TOKEN;
  const passphrase = process.env.PULUMI_CONFIG_PASSPHRASE;

  assert(standSlug, "Expected STAND_SLUG to exist");
  assert(
    /^[a-z0-9-]*$/.exec(standSlug),
    "Expected STAND_SLUG to contain only a-z, 0-9 and -"
  );
  assert(passphrase, "Expected PULUMI_CONFIG_PASSPHRASE to exist");
  assert(standSlug !== "production", "You shall not break production!");
  assert(slackConfigAccessToken, "Expected SLACK_CONFIG_ACCESS_TOKEN to exist");

  const config = getPulumiStackConfig({
    stackName: standSlug,
    encryptionSalt:
      "v1:wMvtx4TDEOA=:v1:mv8ZPWMfHMgrTqSM:FgvNj/Fq5K0CVR9y95XQPuYahg52tA==",
  });
  const adminSiteUrl = `https://${standSlug}.stand-do.organice.app`;
  const slackBotUrl = `https://bot-${standSlug}.stand-do.organice.app`;

  const organiceSlackBotName = `${`OrgaNice (${standSlug}`.slice(0, 34)})`;
  const organiceSlackBotManifest = new SlackAppManifest({
    name: organiceSlackBotName,
    oauthConfig: {
      redirectUrls: [new URL(adminSiteUrl)],
    },
    eventSubscriptions: {
      requestUrl: new URL("/slack/events", new URL(adminSiteUrl)),
    },
    interactivity: {
      requestUrl: new URL("/slack/events", new URL(adminSiteUrl)),
    },
  });

  const kudosSlackBotName = `${`Kudos (${standSlug}`.slice(0, 34)})`;
  const kudosSlackBotManifest = new SlackAppManifest({
    name: kudosSlackBotName,
    oauthConfig: {
      redirectUrls: [new URL(adminSiteUrl)],
    },
    eventSubscriptions: {
      requestUrl: new URL("/slack/kudos/events", new URL(adminSiteUrl)),
    },
    interactivity: {
      requestUrl: new URL("/slack/kudos/events", new URL(adminSiteUrl)),
    },
  });

  if (!config) {
    const organiceSlackBotCredentials = await organiceSlackBotManifest.create({
      token: slackConfigAccessToken,
    });
    const kudosSlackBotCredentials = await kudosSlackBotManifest.create({
      token: slackConfigAccessToken,
    });

    generatePulumiStackConfig({
      domains: {
        adminSite: adminSiteUrl,
        slackBot: slackBotUrl,
      },
      pulumi: {
        stackName: standSlug,
        passphrase,
      },
      organiceSlackBot: {
        appId: organiceSlackBotCredentials.appId,
        botName: organiceSlackBotName,
        clientId: organiceSlackBotCredentials.clientId,
        clientSecret: organiceSlackBotCredentials.clientSecret,
        signingSecret: organiceSlackBotCredentials.signingSecret,
      },
      kudosSlackBot: {
        appId: kudosSlackBotCredentials.appId,
        botName: kudosSlackBotName,
        clientId: kudosSlackBotCredentials.clientId,
        clientSecret: kudosSlackBotCredentials.clientSecret,
        signingSecret: kudosSlackBotCredentials.signingSecret,
      },
    });
  } else {
    await organiceSlackBotManifest.update({
      appId: config.organiceSlackBot.appId,
      token: slackConfigAccessToken,
    });
    await kudosSlackBotManifest.update({
      appId: config.kudosSlackBot.appId,
      token: slackConfigAccessToken,
    });

    generatePulumiStackConfig({
      domains: {
        adminSite: adminSiteUrl,
        slackBot: slackBotUrl,
      },
      pulumi: {
        stackName: standSlug,
        passphrase,
      },
      organiceSlackBot: {
        appId: config.organiceSlackBot.appId,
        botName: organiceSlackBotName,
        clientId: config.organiceSlackBot.clientId,
        clientSecret: config.organiceSlackBot.clientSecret,
        signingSecret: config.organiceSlackBot.signingSecret,
      },
      kudosSlackBot: {
        appId: config.kudosSlackBot.appId,
        botName: kudosSlackBotName,
        clientId: config.kudosSlackBot.clientId,
        clientSecret: config.kudosSlackBot.clientSecret,
        signingSecret: config.kudosSlackBot.signingSecret,
      },
    });
  }
})();

function getPulumiStackConfig({
  stackName,
  encryptionSalt,
}: {
  stackName: string;
  encryptionSalt: string;
}): {
  organiceSlackBot: {
    appId: string;
    clientId: string;
    clientSecret: string;
    signingSecret: string;
  };
  kudosSlackBot: {
    appId: string;
    clientId: string;
    clientSecret: string;
    signingSecret: string;
  };
} | null {
  const stacks = JSON.parse(
    (
      spawnSync("pulumi", ["stack", "ls", "--json"], {
        encoding: "utf-8",
      }) as unknown as SpawnSyncReturns<string>
    ).stdout
  ) as {
    name: string;
    current: boolean;
    lastUpdate: string;
    updateInProgress: boolean;
    resourceCount: number;
    url: string;
  }[];
  const stackExists = stacks.some((stack) => stack.name === stackName);

  if (stackExists) {
    const configFileExists = fs.existsSync(
      path.resolve(__dirname, "..", `Pulumi.${stackName}.yaml`)
    );

    if (!configFileExists) {
      fs.writeFileSync(
        path.resolve(__dirname, "..", `Pulumi.${stackName}.yaml`),
        `encryptionsalt: ${encryptionSalt}`,
        "utf-8"
      );
      spawnSync("pulumi", ["config", "refresh", "-s", stackName]);
    }

    const config = JSON.parse(
      (
        spawnSync(
          "pulumi",
          ["config", "-s", stackName, "--json", "--show-secrets"],
          {
            encoding: "utf-8",
          }
        ) as unknown as SpawnSyncReturns<string>
      ).stdout
    ) as Record<string, { value: string; secret: boolean } | undefined>;

    if (
      !config["organice:organice_slack_app_id"] ||
      !config["organice:organice_slack_client_id"] ||
      !config["organice:organice_slack_client_secret"] ||
      !config["organice:organice_slack_signing_secret"] ||
      !config["organice:kudos_slack_app_id"] ||
      !config["organice:kudos_slack_client_id"] ||
      !config["organice:kudos_slack_client_secret"] ||
      !config["organice:kudos_slack_signing_secret"]
    ) {
      return null;
    }

    return {
      organiceSlackBot: {
        appId: config["organice:organice_slack_app_id"].value,
        clientId: config["organice:organice_slack_client_id"].value,
        clientSecret: config["organice:organice_slack_client_secret"].value,
        signingSecret: config["organice:organice_slack_signing_secret"].value,
      },
      kudosSlackBot: {
        appId: config["organice:kudos_slack_app_id"].value,
        clientId: config["organice:kudos_slack_client_id"].value,
        clientSecret: config["organice:kudos_slack_client_secret"].value,
        signingSecret: config["organice:kudos_slack_signing_secret"].value,
      },
    };
  }

  return null;
}

function generatePulumiStackConfig({
  domains,
  pulumi: { stackName, passphrase },
  organiceSlackBot,
  kudosSlackBot,
}: {
  domains: {
    adminSite: string;
    slackBot: string;
  };
  pulumi: {
    stackName: string;
    passphrase: string;
  };
  organiceSlackBot: {
    appId: string;
    botName: string;
    clientId: string;
    clientSecret: string;
    signingSecret: string;
  };
  kudosSlackBot: {
    appId: string;
    botName: string;
    clientId: string;
    clientSecret: string;
    signingSecret: string;
  };
}): void {
  const stacks = JSON.parse(
    (
      spawnSync("pulumi", ["stack", "ls", "--json"], {
        encoding: "utf-8",
      }) as unknown as SpawnSyncReturns<string>
    ).stdout
  ) as {
    name: string;
    current: boolean;
    lastUpdate: string;
    updateInProgress: boolean;
    resourceCount: number;
    url: string;
  }[];
  const stackExists = stacks.some((stack) => stack.name === stackName);

  if (!stackExists) {
    spawnSync("pulumi", ["stack", "init", stackName]);
    spawnSync(
      "pulumi",
      ["stack", "change-secrets-provider", "passphrase", "-s", stackName],
      {
        input: passphrase,
      }
    );
  }

  const template = fs.readFileSync(
    path.resolve(__dirname, "..", "Pulumi.[stand].yaml"),
    "utf-8"
  );

  fs.writeFileSync(
    path.resolve(__dirname, "..", `Pulumi.${stackName}.yaml`),
    template,
    "utf-8"
  );

  const plains: Record<string, string> = {
    stack_name: stackName,
    "bot-domain": domains.slackBot,
    domain: domains.adminSite,
    sentry_environment: stackName,
    organice_slack_app_id: organiceSlackBot.appId,
    kudos_slack_app_id: kudosSlackBot.appId,
  };
  const secrets: Record<string, string> = {
    organice_slack_client_id: organiceSlackBot.clientId,
    organice_slack_client_secret: organiceSlackBot.clientSecret,
    organice_slack_signing_secret: organiceSlackBot.signingSecret,
    kudos_slack_client_id: kudosSlackBot.clientId,
    kudos_slack_client_secret: kudosSlackBot.clientSecret,
    kudos_slack_signing_secret: kudosSlackBot.signingSecret,
  };

  for (const [key, value] of Object.entries(plains)) {
    spawnSync("pulumi", ["config", "set", key, value, `--stack=${stackName}`]);
  }

  for (const [key, value] of Object.entries(secrets)) {
    spawnSync("pulumi", [
      "config",
      "set",
      key,
      value,
      "--secret",
      `--stack=${stackName}`,
    ]);
  }
}
