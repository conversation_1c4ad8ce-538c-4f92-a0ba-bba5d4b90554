/* eslint-disable no-console */
import assert from "assert";
import fs from "fs";
import path from "path";

import SlackAppManifest from "@organice/slack-bot/slack/SlackAppManifest";

import { getConfigAccessToken } from "./slack/SlackConfigToken";

assert(
  process.env.ORGANICE_SLACK_BOT_NAME,
  "ORGANICE_SLACK_BOT_NAME env variable should be defined"
);
assert(
  process.env.KUDOS_SLACK_BOT_NAME,
  "KUDOS_SLACK_BOT_NAME env variable should be defined"
);
assert(
  process.env.SLACK_ADMIN_SITE_URL,
  "SLACK_ADMIN_SITE_URL env variable should be defined"
);

const organiceBotName = process.env.ORGANICE_SLACK_BOT_NAME;
const kudosBotName = process.env.KUDOS_SLACK_BOT_NAME;
const redirectUrls = [new URL(process.env.SLACK_ADMIN_SITE_URL)];

const organiceBotManifest = new SlackAppManifest({
  name: organiceBotName,
  oauthConfig: {
    redirectUrls,
  },
  eventSubscriptions: {
    requestUrl: new URL(
      "/slack/events",
      new URL(process.env.SLACK_ADMIN_SITE_URL)
    ),
  },
  interactivity: {
    requestUrl: new URL(
      "/slack/events",
      new URL(process.env.SLACK_ADMIN_SITE_URL)
    ),
  },
  socketModeEnabled: true,
});
const kudosBotManifest = new SlackAppManifest({
  name: kudosBotName,
  oauthConfig: {
    redirectUrls,
  },
  eventSubscriptions: {
    requestUrl: new URL(
      "/slack/kudos/events",
      new URL(process.env.SLACK_ADMIN_SITE_URL)
    ),
  },
  interactivity: {
    requestUrl: new URL(
      "/slack/kudos/events",
      new URL(process.env.SLACK_ADMIN_SITE_URL)
    ),
  },
  socketModeEnabled: true,
});

let accessToken;

if (process.env.SLACK_CONFIG_REFRESH_TOKEN) {
  accessToken = getConfigAccessToken({
    token: process.env.SLACK_CONFIG_ACCESS_TOKEN,
    expiresAt: process.env.SLACK_CONFIG_ACCESS_TOKEN_EXPIRES_AT
      ? new Date(process.env.SLACK_CONFIG_ACCESS_TOKEN_EXPIRES_AT)
      : undefined,
    refreshToken: process.env.SLACK_CONFIG_REFRESH_TOKEN,
  }).then(async ({ token, expiresAt, newRefreshToken }) => {
    if (newRefreshToken) {
      await setToDotEnv("SLACK_CONFIG_REFRESH_TOKEN", newRefreshToken);
    }

    await setToDotEnv("SLACK_CONFIG_ACCESS_TOKEN", token);
    await setToDotEnv(
      "SLACK_CONFIG_ACCESS_TOKEN_EXPIRES_AT",
      expiresAt.toISOString()
    );

    return token;
  });
} else {
  assert(
    process.env.SLACK_CONFIG_ACCESS_TOKEN,
    "SLACK_CONFIG_ACCESS_TOKEN or SLACK_CONFIG_REFRESH_TOKEN env variable should be defined"
  );

  accessToken = Promise.resolve(process.env.SLACK_CONFIG_ACCESS_TOKEN);
}

if (!process.env.ORGANICE_SLACK_APP_ID) {
  accessToken
    .then((token) => organiceBotManifest.create({ token }))
    .then(async (credentials) => {
      await setToDotEnv("ORGANICE_SLACK_APP_ID", credentials.appId);
      await setToDotEnv("ORGANICE_SLACK_CLIENT_ID", credentials.clientId);
      await setToDotEnv(
        "ORGANICE_SLACK_CLIENT_SECRET",
        credentials.clientSecret
      );
      await setToDotEnv(
        "ORGANICE_SLACK_SIGNING_SECRET",
        credentials.signingSecret
      );
      console.log(`Created app manifest for "${organiceBotName}"`);
    })
    .catch(console.error);
} else {
  const appId = process.env.ORGANICE_SLACK_APP_ID;

  accessToken
    .then((token) => organiceBotManifest.update({ appId, token }))
    .then(() => console.log(`Updated app manifest for "${organiceBotName}"`))
    .catch(console.error);
}

if (!process.env.KUDOS_SLACK_APP_ID) {
  accessToken
    .then((token) => kudosBotManifest.create({ token }))
    .then(async (credentials) => {
      await setToDotEnv("KUDOS_SLACK_APP_ID", credentials.appId);
      await setToDotEnv("KUDOS_SLACK_CLIENT_ID", credentials.clientId);
      await setToDotEnv("KUDOS_SLACK_CLIENT_SECRET", credentials.clientSecret);
      await setToDotEnv(
        "KUDOS_SLACK_SIGNING_SECRET",
        credentials.signingSecret
      );
      console.log(`Created app manifest for "${kudosBotName}"`);
    })
    .catch(console.error);
} else {
  const appId = process.env.KUDOS_SLACK_APP_ID;

  accessToken
    .then((token) => kudosBotManifest.update({ appId, token }))
    .then(() => console.log(`Updated app manifest for "${kudosBotName}"`))
    .catch(console.error);
}

async function setToDotEnv(key: string, value: string): Promise<void> {
  const dotEnvPath = path.resolve(__dirname, "..", ".env");
  const dotEnv = await fs.promises.readFile(dotEnvPath, "utf-8");
  const lines = dotEnv.split("\n");
  const lineIndex = lines.findIndex((line) => line.startsWith(`${key}=`));

  if (lineIndex >= 0) {
    lines[lineIndex] = `${key}=${value}`;
  } else {
    lines.push(`${key}=${value}`);
  }

  const newDotEnv = lines.join("\n");

  await fs.promises.writeFile(dotEnvPath, newDotEnv);
}
