{"name": "scopezilla", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"build": "next build", "start": "concurrently --raw 'next dev' 'docker compose up'", "test": "echo \"Error: no test specified\" && exit 1"}, "author": "", "license": "ISC", "engines": {"node": "24.2.0", "npm": "11.3.0"}, "devDependencies": {"concurrently": "^9.2.1", "supabase": "^2.54.11"}, "dependencies": {"next": "^16.0.1"}}