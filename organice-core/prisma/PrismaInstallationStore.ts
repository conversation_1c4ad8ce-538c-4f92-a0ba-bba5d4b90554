import { PrismaClient } from "@prisma/client";
import * as Sen<PERSON> from "@sentry/core";
import {
  Installation,
  InstallationQuery,
  InstallationStore,
} from "@slack/oauth";
import { WebClient } from "@slack/web-api";

import {
  Logger,
  WorkspaceRepository,
  onSlackOAuthInstall,
  ProductOwnerNotifier,
  ReadonlyDeep,
  SlackAdapter,
  SlackBotToken,
  AnotherOrgaNiceBotAlreadyInstalledError,
  SlackBotApplication,
} from "../domain";

interface ConstructorOptions {
  app: SlackBotApplication;
  appId: string;
  clientId: string;
  clientSecret: string;
  logger: Logger;
  prismaClient: PrismaClient;

  getSlackAdapter: (slackBotToken: ReadonlyDeep<SlackBotToken>) => SlackAdapter;
  getRepository(): WorkspaceRepository;
  getProductOwnerNotifier(): ProductOwnerNotifier;
}

export default class PrismaInstallationStore implements InstallationStore {
  constructor(private options: ConstructorOptions) {}

  async fetchInstallation(
    query: InstallationQuery<boolean>
  ): Promise<Installation> {
    const workspaceId = query.teamId ?? query.enterpriseId!;

    const tokens = await Sentry.startSpan(
      {
        op: "db.query",
        name: "fetchInstallation",
        attributes: {
          workspaceId,
        },
      },
      async () =>
        this.options.prismaClient.workspace.findFirst({
          select: {
            slackBotToken: true,
            slackMemberTokens: {
              where: {
                memberId: query.userId,
              },
            },
          },
          where: {
            id: workspaceId,
          },
        })
    );

    // We've lost several OAuth tokens on production and we have to live with it forever...
    if (
      !tokens?.slackBotToken ||
      tokens.slackBotToken.appId !== this.options.appId
    ) {
      return {
        team: query.teamId ? { id: query.teamId } : undefined,
        enterprise: query.enterpriseId ? { id: query.enterpriseId } : undefined,
        user: {
          id: query.userId!,
          token: undefined,
          scopes: [],
        },
      };
    }

    return {
      team: {
        id: query.teamId!,
      },
      enterprise: undefined,
      bot: {
        id: tokens.slackBotToken.botId,
        scopes: tokens.slackBotToken.scopes,
        token: tokens.slackBotToken.token,
        userId: tokens.slackBotToken.botMemberId,
      },
      user: {
        id: query.userId ?? tokens.slackMemberTokens[0]?.memberId,
        scopes: tokens.slackMemberTokens[0]?.scopes,
        token: tokens.slackMemberTokens[0]?.token,
      },
      tokenType: "bot",
      authVersion: "v2",
    };
  }

  async storeInstallation<AuthVersion extends "v1" | "v2">(
    installation: Installation<AuthVersion>
  ): Promise<void> {
    const repository = this.options.getRepository();
    const logger = this.options.logger.withContext({
      member: {
        id: installation.user.id,
      },
      workspace: {
        id: (installation.team?.id ?? installation.enterprise?.id)!,
      },
    });

    try {
      await onSlackOAuthInstall(
        repository,
        this.options.getSlackAdapter,
        this.options.getProductOwnerNotifier(),
        installation as Installation<"v2">,
        logger,
        this.options.app
      );
    } catch (error) {
      if (error instanceof AnotherOrgaNiceBotAlreadyInstalledError) {
        const client = new WebClient(installation.bot!.token);

        await client.apps.uninstall({
          client_id: this.options.clientId,
          client_secret: this.options.clientSecret,
        });
      }

      throw error;
    }
  }
}
