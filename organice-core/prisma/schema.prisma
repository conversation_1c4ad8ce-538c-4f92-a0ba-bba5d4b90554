generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("POSTGRES_CONNECTION_URL")
}

model Workspace {
  id                      String                   @id
  name                    String
  url                     String
  iconUrl                 String?
  customFields            String
  onboarding              String
  members                 Member[]
  teams                   Team[]
  policy                  WorkspacePolicy?
  policyFields            PolicyField[]
  memberPolicyFields      MemberPolicyField[]
  pricing                 Pricing?
  orgTreeNodes            OrgTreeNode[]
  slackPlan               String
  wasDeleted              Boolean
  channels                Channel[]
  reference               String
  reports                 Reports?
  installedAt             DateTime?
  installedBy             String?
  updatedAt               DateTime?
  // TODO: rename to celebration settings
  announcements           String?
  timeOffs                TimeOffs?
  timeOffRequestApprovers TimeOffRequestApprover[]
  timeOffTypes            TimeOffType[]
  timeOffRequests         TimeOffRequest[]
  slackIntegration        Boolean
  chartLayout             String                   @default("{\"clusterHorizontal\": true, \"dataField\": null}")
  predictedOrgTree        String                   @default("{\"requested\": false}")
  // new field that now stores feature announcements and will store existing BD and Annuversary announcements once we make a data migration
  Announcements           Announcement[]
  kudos                   <PERSON>[]
  kudosSettings           KudosSetting?
  surveys                 Survey[]
  surveyTemplates         SurveyTemplate[]
  surveyAnswers           SurveyAnswer[]
  links                   Link[]
  activityLog             ActivityEvent[]
  holidays                Holidays[]
  holidaysSettings        HolidaysSettings?
  slackStatusChanges      SlackStatusChange[]
  calendarFeeds           CalendarFeed[]
  notifications           Notification[]
  publishedNotifications  PublishedNotification[]
  appSumoCodes            AppSumoCode[]
  slackBotToken           SlackBotToken?
  slackMemberTokens       SlackMemberToken[]
  timeOffTypeMemberStates TimeOffTypeMemberState[]
}

model SlackBotToken {
  workspaceId String    @id
  workspace   Workspace @relation(fields: [workspaceId], references: [id], onDelete: Cascade)
  token       String
  scopes      String[]
  botId       String // it's NOT a member id
  botMemberId String // this fields doesn't have a relation to Member because it stores bot user id, which we typically don't store in Member table
  appId       String
}

model SlackMemberToken {
  workspaceId String
  workspace   Workspace @relation(fields: [workspaceId], references: [id], onDelete: Cascade)
  memberId    String
  member      Member    @relation(fields: [memberId, workspaceId], references: [id, workspaceId], onDelete: Cascade)
  token       String
  scopes      String[]

  @@id([workspaceId, memberId])
}

model Link {
  id            String    @id
  title         String
  workspaceId   String
  workspace     Workspace @relation(fields: [workspaceId], references: [id], onDelete: Cascade)
  chartLayout   String    @default("{\"clusterHorizontal\": true, \"dataField\": null}")
  expandedNodes String[]
  visibleNodes  String[]
  visibleFields String[]
  visibleViews  String[]
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @default(now()) @updatedAt
}

model CalendarFeed {
  id          String    @id
  title       String
  workspaceId String
  workspace   Workspace @relation(fields: [workspaceId], references: [id], onDelete: Cascade)
  memberId    String?
  member      Member?   @relation(fields: [memberId, workspaceId], references: [id, workspaceId], onDelete: Cascade)
  filters     String
}

model Announcement {
  id          String    @id
  message     String
  sentAt      DateTime?
  type        String
  // a member can be null if the announcement is a system announcement
  memberId    String?
  workspaceId String
  // can be a public channel or a DM with a user
  channelId   String

  member    Member?   @relation(fields: [memberId, workspaceId], references: [id, workspaceId], onDelete: Cascade)
  workspace Workspace @relation(fields: [workspaceId], references: [id], onDelete: Cascade)
  channel   Channel?  @relation(fields: [channelId, workspaceId], references: [id, workspaceId], onDelete: Cascade)

  createAt DateTime @default(now())
  updateAt DateTime @default(now()) @updatedAt
}

model TimeOffs {
  workspaceId                                  String          @id
  channelId                                    String?
  isEnable                                     Boolean
  createDiscussionChannelWhenMultipleApprovers Boolean
  changeSlackStatus                            Boolean
  workspace                                    Workspace       @relation(fields: [workspaceId], references: [id], onDelete: Cascade)
  policies                                     TimeOffPolicy[]
}

model TimeOffType {
  workspace          Workspace                @relation(fields: [workspaceId], references: [id], onDelete: Cascade)
  workspaceId        String
  id                 String
  label              String
  color              String
  emoji              String
  slackEmoji         String
  hint               String?
  requests           TimeOffRequest[]
  timeOffPolicyTypes TimeOffPolicyType[]
  order              Int
  memberStates       TimeOffTypeMemberState[]

  @@id([workspaceId, id])
}

model TimeOffRequest {
  workspace     Workspace                @relation(fields: [workspaceId], references: [id], onDelete: Cascade)
  workspaceId   String
  id            String                   @id
  member        Member                   @relation("timeOffRequestMember", fields: [workspaceId, memberId], references: [workspaceId, id], onDelete: Cascade)
  memberId      String
  type          TimeOffType              @relation(fields: [workspaceId, typeId], references: [workspaceId, id], onDelete: Cascade)
  typeId        String
  startDate     DateTime
  endDate       DateTime
  comment       String?
  approvers     TimeOffRequestApprover[]
  status        String
  rejectReason  String?
  notifications String
  handledBy     String?

  createdAt DateTime
  updatedAt DateTime
  deletedAt DateTime?
}

model TimeOffRequestApprover {
  workspace   Workspace      @relation(fields: [workspaceId], references: [id], onDelete: Cascade)
  workspaceId String
  request     TimeOffRequest @relation(fields: [requestId], references: [id], onDelete: Cascade)
  requestId   String
  approver    Member         @relation(fields: [workspaceId, approverId], references: [workspaceId, id], onDelete: Cascade)
  approverId  String

  @@id([requestId, approverId])
}

model PolicyField {
  id                 String              @id
  label              String
  type               String
  required           Boolean
  publiclyAvailable  Boolean             @default(true)
  order              Int
  slackFieldId       String?
  workspaceId        String
  workspace          Workspace           @relation(fields: [workspaceId], references: [id], onDelete: Cascade)
  memberPolicyFields MemberPolicyField[]
}

model WorkspacePolicy {
  phoneRequired               Boolean   @default(false)
  birthdayRequired            Boolean   @default(false)
  anniversaryRequired         Boolean   @default(false)
  countryRequired             Boolean   @default(false)
  managerSlackFieldId         String?
  jobTitleSlackFieldId        String?
  phoneSlackFieldId           String?
  birthdaySlackFieldId        String?
  anniversarySlackFieldId     String?
  departmentSlackFieldId      String?
  teamsSlackFieldId           String?
  countrySlackFieldId         String?
  photoUrlRequired            Boolean   @default(true)
  workspaceId                 String    @id
  notifications               String
  workspace                   Workspace @relation(fields: [workspaceId], references: [id], onDelete: Cascade)
  wasInitiallySyncedWithSlack Boolean   @default(false)

  @@unique([workspaceId])
}

model Reports {
  enabled     Boolean
  workspaceId String
  workspace   Workspace @relation(fields: [workspaceId], references: [id], onDelete: Cascade)

  @@unique([workspaceId])
}

model OrgTreeNode {
  workspaceId  String
  workspace    Workspace     @relation(fields: [workspaceId], references: [id], onDelete: Cascade)
  id           String
  type         String
  parentId     String?
  parent       OrgTreeNode?  @relation("subordinates", fields: [workspaceId, parentId], references: [workspaceId, id])
  subordinates OrgTreeNode[] @relation("subordinates")
  timestamp    Float?
  order        Int?

  department_title     String?
  department_color     String?
  department_managerId String?
  department_manager   OrgTreeNode? @relation("departmentManager")

  position_withoutManagerManual Boolean @default(false)

  position_title                String?
  position_memberId             String?
  position_member               Member?      @relation(fields: [position_memberId, workspaceId], references: [id, workspaceId])
  position_reference            String?
  position_teamIds              String[]
  position_managed_departmentId String?
  position_managed_department   OrgTreeNode? @relation("departmentManager", fields: [position_managed_departmentId, workspaceId], references: [id, workspaceId])
  position_managed_teamIds      String[]
  position_manager_teams        Team[]       @relation("teamManager")

  @@id([workspaceId, id])
  @@unique([position_memberId, workspaceId])
  @@unique([position_managed_departmentId, workspaceId])
}

model MemberPolicyField {
  value         String?
  workspace     Workspace   @relation(fields: [workspaceId], references: [id], onDelete: Cascade)
  workspaceId   String
  policyField   PolicyField @relation(fields: [policyFieldId], references: [id], onDelete: Cascade)
  policyFieldId String
  member        Member      @relation(fields: [memberId, workspaceId], references: [id, workspaceId], onDelete: Cascade)
  memberId      String

  @@id([policyFieldId, memberId])
  @@unique([policyFieldId, memberId])
}

model Member {
  id                           String
  isAdmin                      Boolean
  isSlackWorkspaceAdmin        Boolean
  isSlackWorkspaceOwner        Boolean
  isSlackWorkspacePrimaryOwner Boolean
  name                         String?
  photoUrl                     String?
  photo512Url                  String?
  photo72Url                   String?
  realName                     String?
  displayName                  String?
  email                        String?
  organicePhone                String?
  birthday                     DateTime?
  hideBirthday                 Boolean                  @default(false)
  notes                        String?
  workspaceId                  String
  deleted                      Boolean? // DEPRECATED, don't use it
  updated                      DateTime?
  isSlackBillable              Boolean                  @default(true)
  botState                     String
  joinedAt                     DateTime?
  country                      String?
  status                       String?
  workspace                    Workspace                @relation(fields: [workspaceId], references: [id], onDelete: Cascade)
  firstClickedProPlanAt        DateTime?
  orgTreeNode                  OrgTreeNode?
  organiceCustomFileds         MemberPolicyField[]
  howManyTimesHomeTabVisited   Int                      @default(0)
  announcements                Announcement[]
  timeOffOutcomingRequests     TimeOffRequest[]         @relation("timeOffRequestMember")
  timeOffIncomingRequests      TimeOffRequestApprover[]
  kudosSent                    Kudos[]                  @relation("sentKudos")
  kudosReceived                Kudos[]                  @relation("receivedKudos")

  surveys         Survey[]
  surveyTemplates SurveyTemplate[]
  surveyAnswers   SurveyAnswer[]

  timeOffBalance      String? // DEPRECATED, don't use it
  timeOffTypePolicyId String?
  timeOffTypePolicy   TimeOffPolicy?           @relation(fields: [timeOffTypePolicyId], references: [id])
  timeOffNextReset    DateTime? // DEPRECATED, don't use it
  timezone            String?
  calendarFeeds       CalendarFeed[]
  notifications       Notification[]
  slackTokens         SlackMemberToken[]
  timeOffTypeStates   TimeOffTypeMemberState[]

  @@id([id, workspaceId])
}

model Channel {
  id            String
  name          String
  workspaceId   String
  workspace     Workspace      @relation(fields: [workspaceId], references: [id], onDelete: Cascade)
  Announcements Announcement[]
  membersCount  Int?

  @@unique([id, workspaceId])
}

model Team {
  id          String       @id
  createAt    DateTime     @default(now())
  updateAt    DateTime     @default(now()) @updatedAt
  label       String
  color       String
  workspaceId String
  workspace   Workspace    @relation(fields: [workspaceId], references: [id], onDelete: Cascade)
  managerId   String?
  manager     OrgTreeNode? @relation("teamManager", fields: [workspaceId, managerId], references: [workspaceId, id])
}

model Pricing {
  customerId     String?
  subscriptionId String?
  plan           String
  limits         String // deprecated
  status         String
  trialEndDate   Float?
  appSumoCode    String?
  appSumoEndDate DateTime?
  workspaceId    String    @id
  workspace      Workspace @relation(fields: [workspaceId], references: [id], onDelete: Cascade)

  @@unique([workspaceId])
}

model AppSumoCode {
  code                  String     @id
  redeemedAt            DateTime?
  redeemedByWorkspace   Workspace? @relation(fields: [redeemedByWorkspaceId], references: [id], onDelete: SetNull)
  redeemedByWorkspaceId String?
}

model Kudos {
  id          String    @id
  workspaceId String
  workspace   Workspace @relation(fields: [workspaceId], references: [id], onDelete: Cascade)
  quantity    Int
  message     String
  channel     String
  messageTS   String
  permalink   String?
  gifUrl      String?
  createdAt   DateTime
  sender      Member    @relation("sentKudos", fields: [senderId, workspaceId], references: [id, workspaceId], onDelete: Cascade)
  senderId    String
  receiver    Member    @relation("receivedKudos", fields: [receiverId, workspaceId], references: [id, workspaceId], onDelete: Cascade)
  receiverId  String
  valuesIds   String?
}

enum KudosResetFrequency {
  Week
  Month
}

model KudosSetting {
  enable      Boolean
  workspaceId String    @id
  workspace   Workspace @relation(fields: [workspaceId], references: [id], onDelete: Cascade)
  channelId   String?
  kudosLimit  Int

  resetFrequency KudosResetFrequency @default(Week)
  // NOTE: if frequency is Week, then day should be in an interval from 1 to 7
  // if frequency is Month, then day should be in an interval from 1 to 31
  resetDay       Int                 @default(7)
  resetTime      String              @default("11 pm")
  resetTimezone  String              @default("GMT +00:00")
  values         KudosValue[]

  @@unique([workspaceId])
}

model KudosValue {
  id          String       @id
  workspaceId String
  emoji       String
  title       String
  description String?
  setting     KudosSetting @relation(fields: [workspaceId], references: [workspaceId], onDelete: Cascade)
}

model Survey {
  id                    String       @id
  title                 String
  status                SurveyStatus
  isAnonymous           Boolean
  startMessagePermalink String
  startMessageTS        String

  workspaceId String
  workspace   Workspace @relation(fields: [workspaceId], references: [id], onDelete: Cascade)

  channelId         String?
  participantsCount Int     @default(0)

  defaultTemplateId String?
  templateId        String?
  template          SurveyTemplate? @relation(fields: [templateId], references: [id])

  createdById String?
  createdBy   Member? @relation(fields: [createdById, workspaceId], references: [id, workspaceId])

  createdAt DateTime @default(now())
  updatedAt DateTime @default(now()) @updatedAt

  answers   SurveyAnswer[]
  questions SurveyQuestion[]
}

enum SurveyStatus {
  InProgress
  Closed
}

model SurveyTemplate {
  id    String @id
  title String

  workspaceId String
  workspace   Workspace @relation(fields: [workspaceId], references: [id], onDelete: Cascade)

  surveys   Survey[]
  questions SurveyTemplateQuestion[]

  createdById String?
  createdBy   Member? @relation(fields: [createdById, workspaceId], references: [id, workspaceId])

  createdAt DateTime @default(now())
  updatedAt DateTime @default(now()) @updatedAt
}

model SurveyTemplateQuestion {
  id       String  @id
  title    String
  required Boolean
  order    Int?

  type            SurveyQuestionType
  singleOptions   String?
  multipleOptions String?
  scaleOptions    String?

  templateId String
  template   SurveyTemplate @relation(fields: [templateId], references: [id], onDelete: Cascade)
}

model SurveyQuestion {
  id       String  @id
  title    String
  required Boolean

  type            SurveyQuestionType
  singleOptions   String?
  multipleOptions String?
  scaleOptions    String?

  answers  SurveyAnswer[]
  Survey   Survey?        @relation(fields: [surveyId], references: [id], onDelete: Cascade)
  surveyId String?
}

model SurveyAnswer {
  id String @id

  workspaceId String
  workspace   Workspace @relation(fields: [workspaceId], references: [id], onDelete: Cascade)

  responderId String
  responder   Member @relation(fields: [responderId, workspaceId], references: [id, workspaceId], onDelete: Cascade)

  questionId String
  question   SurveyQuestion @relation(fields: [questionId], references: [id], onDelete: Cascade)

  surveyId String
  survey   Survey @relation(fields: [surveyId], references: [id], onDelete: Cascade)

  value String
}

enum SurveyQuestionType {
  Text
  Scale
  SingleOption
  MultipleOptions
}

enum YearStart {
  Calendar
  StartDate
}

model TimeOffPolicy {
  id                  String              @id
  title               String
  workspaceId         String
  timeOffs            TimeOffs            @relation(fields: [workspaceId], references: [workspaceId])
  typePolicies        TimeOffPolicyType[]
  members             Member[]
  isDefault           Boolean             @default(false)
  yearStart           YearStart           @default(Calendar) // DEPRECATED, don't use it
  workDays            Int[]               @default([1, 2, 3, 4, 5])
  includedWeekendDays Int[]               @default([])
  notifyAccruals      Boolean             @default(true)
}

enum AccrualsFrequency {
  Week
  Month
}

model TimeOffPolicyType {
  policyId    String
  policy      TimeOffPolicy @relation(fields: [policyId], references: [id], onDelete: Cascade)
  workspaceId String
  typeId      String
  type        TimeOffType   @relation(fields: [workspaceId, typeId], references: [workspaceId, id], onDelete: Cascade)

  onStartQuota       Float     @default(0)
  rollOverToNextYear Boolean   @default(false)
  yearStart          YearStart @default(Calendar)

  accrualsQuota     Float              @default(0)
  accuralsFrequency AccrualsFrequency?
  nextAccruals      DateTime?
  maxCapacity       Float              @default(0)

  @@id([workspaceId, typeId, policyId])
}

model TimeOffTypeMemberState {
  workspaceId String
  workspace   Workspace   @relation(fields: [workspaceId], references: [id], onDelete: Cascade)
  memberId    String
  member      Member      @relation(fields: [memberId, workspaceId], references: [id, workspaceId], onDelete: Cascade)
  typeId      String
  type        TimeOffType @relation(fields: [workspaceId, typeId], references: [workspaceId, id], onDelete: Cascade)

  balance     Float
  nextResetAt DateTime?

  @@id([workspaceId, memberId, typeId])
}

model ActivityEvent {
  id          String    @id
  type        String
  data        String
  workspaceId String
  workspace   Workspace @relation(fields: [workspaceId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())
}

model Holidays {
  id          String    @id
  workspaceId String
  workspace   Workspace @relation(fields: [workspaceId], references: [id], onDelete: Cascade)
  startDate   DateTime
  endDate     DateTime
  name        String
  description String
  isOfficial  Boolean
  country     String

  createdAt DateTime @default(now())
  updatedAt DateTime @default(now()) @updatedAt
}

enum SlackStatusType {
  TimeOff
  Holiday
  Birthday
  Anniversary
}

model SlackStatusChange {
  type          SlackStatusType
  stashedStatus String
  refId         String
  memberId      String
  workspaceId   String
  workspace     Workspace       @relation(fields: [workspaceId], references: [id], onDelete: Cascade)

  @@id([workspaceId, memberId, type, refId])
}

model HolidaysSettings {
  workspaceId String    @id
  workspace   Workspace @relation(fields: [workspaceId], references: [id], onDelete: Cascade)
  countries   String[]
  channelId   String?

  createdAt DateTime @default(now())
  updatedAt DateTime @default(now()) @updatedAt
}

model NotificationBlock {
  id             String       @id
  type           String
  data           String
  notificationId String
  notification   Notification @relation(fields: [notificationId], references: [id], onDelete: Cascade)
}

model PublishedNotification {
  id          String    @id
  sentAt      DateTime?
  messageTS   String
  workspaceId String
  // can be a public channel or a DM with a user
  channelId   String

  state String

  workspace      Workspace @relation(fields: [workspaceId], references: [id], onDelete: Cascade)
  notificationId String

  createAt DateTime @default(now())
}

model Notification {
  id          String    @id
  title       String
  isActive    Boolean   @default(false)
  workspaceId String
  workspace   Workspace @relation(fields: [workspaceId], references: [id], onDelete: Cascade)

  createdById String
  createdBy   Member @relation(fields: [createdById, workspaceId], references: [id, workspaceId])

  settings  NotificationSettings?
  blocks    NotificationBlock[]
  createdAt DateTime              @default(now())
  updatedAt DateTime              @default(now()) @updatedAt
}

enum NotificationsFrequency {
  Day
  Week
  Month
}

model NotificationSettings {
  notificationId String
  notification   Notification           @relation(fields: [notificationId], references: [id], onDelete: Cascade)
  frequency      NotificationsFrequency
  // NOTE: if frequency is Week, then day should be in an interval from 1 to 7
  // if frequency is Month, then day should be in an interval from 1 to 31
  day            Int?
  time           String
  timezone       String
  channelId      String?

  @@unique([notificationId])
}
