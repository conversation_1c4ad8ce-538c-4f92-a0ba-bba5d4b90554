import got from "got";

import {
  SlackBotApplication,
  EmailCollectedHandlerProps,
  ProductOwnerNotifier,
  SlackInstallHandlerProps,
  SlackReinstallHandlerProps,
  SlackUninstallHandlerProps,
  StripePaymentFailureProps,
  SwitchToAppSumoSubscriptionProps,
  SwitchToStripeSubscriptionProps,
} from "../domain";

class SlackWebhookProductOwnerNotifier implements ProductOwnerNotifier {
  private url: string;

  constructor(url: string) {
    this.url = url;
  }

  async handleInstallation(props: SlackInstallHandlerProps): Promise<void> {
    await got.post(this.url, {
      body: JSON.stringify({
        blocks: [
          {
            type: "section",
            text: {
              type: "mrkdwn",
              text: [
                `*New ${
                  {
                    [SlackBotApplication.ORGANICE]: "OrgaNice",
                    [SlackBotApplication.KUDOS]: "Kudos",
                  }[props.application]
                } bot installation*`,
                `Workspace: *${props.workspace.name}*`,
                `Workspace url: ${props.workspace.url}`,
                `Installed by: *${props.installedBy.name} (${props.installedBy.jobTitle}, ${props.installedBy.position}), ${props.installedBy.email}*`,
                `Members: *${props.workspace.membersCount}*`,
                `Slack Plan: *${
                  {
                    free: "Free",
                    std: "Pro",
                    plus: "Business+",
                    enterprise: "Enterprise Grid",
                    compliance: "Compliance",
                  }[props.workspace.plan]
                }*`,
              ].join("\n"),
            },
          },
        ],
      }),
    });
  }

  async handleUninstallation(props: SlackUninstallHandlerProps): Promise<void> {
    await got.post(this.url, {
      body: JSON.stringify({
        blocks: [
          {
            type: "section",
            text: {
              type: "mrkdwn",
              text: [
                `*${props.workspace.name} (${
                  props.workspace.url
                }) just uninstalled ${
                  {
                    [SlackBotApplication.ORGANICE]: "OrgaNice",
                    [SlackBotApplication.KUDOS]: "Kudos",
                  }[props.application]
                }*`,
              ].join("\n"),
            },
          },
        ],
      }),
    });
  }

  async handleReinstallation(props: SlackReinstallHandlerProps): Promise<void> {
    await got.post(this.url, {
      body: JSON.stringify({
        blocks: [
          {
            type: "section",
            text: {
              type: "mrkdwn",
              text: [
                `*${props.workspace.name} (${
                  props.workspace.url
                }) just reinstalled ${
                  {
                    [SlackBotApplication.ORGANICE]: "OrgaNice",
                    [SlackBotApplication.KUDOS]: "Kudos",
                  }[props.application]
                }*`,
              ].join("\n"),
            },
          },
        ],
      }),
    });
  }

  async handleSwitchToAppSumoSubscription(
    props: SwitchToAppSumoSubscriptionProps
  ): Promise<void> {
    await got.post(this.url, {
      body: JSON.stringify({
        blocks: [
          {
            type: "section",
            text: {
              type: "mrkdwn",
              text: [
                `:fire: *New AppSumo subscription!*`,
                `Workspace: *${props.workspace.name}*`,
                `Code: *${props.billing.code}*`,
              ].join("\n"),
            },
          },
        ],
      }),
    });
  }

  async handleSwitchToStripeSubscription(
    props: SwitchToStripeSubscriptionProps
  ): Promise<void> {
    await got.post(this.url, {
      body: JSON.stringify({
        blocks: [
          {
            type: "section",
            text: {
              type: "mrkdwn",
              text: [
                `:fire: *New Stripe paid subscription!*`,
                `Workspace: *${props.workspace.name}*`,
                `Stripe customer id: *${props.billing.customerId}*`,
                `Stripe subscription id: *${props.billing.subscriptionId}*`,
              ].join("\n"),
            },
          },
        ],
      }),
    });
  }

  async handleStripePaymentFailure(
    props: StripePaymentFailureProps
  ): Promise<void> {
    await got.post(this.url, {
      body: JSON.stringify({
        blocks: [
          {
            type: "section",
            text: {
              type: "mrkdwn",
              text: [
                `:warning: *Stripe invoice payment failed*`,
                `Workspace: *${props.workspace.name}*`,
                `Stripe customer id: *${props.billing.customerId}*`,
                `Stripe subscription id: *${props.billing.subscriptionId}*`,
              ].join("\n"),
            },
          },
        ],
      }),
    });
  }

  async handleEmailCollection(
    props: EmailCollectedHandlerProps
  ): Promise<void> {
    await got.post(this.url, {
      body: JSON.stringify({
        blocks: [
          {
            type: "section",
            text: {
              type: "mrkdwn",
              text: [
                `*New customer is interested in Slackless sign up*`,
                `Company: *${props.companyName}*`,
                `Full name: *${props.fullName}*`,
                `Email: ${props.email}`,
              ].join("\n"),
            },
          },
        ],
      }),
    });
  }
}

export default SlackWebhookProductOwnerNotifier;
