import assert from "assert";
import crypto from "crypto";

import {
  <PERSON><PERSON>,
  Member,
  ReadonlyDeep,
  SlackAdapter,
  Survey,
  SurveyQuestionInput,
  SurveyStatus,
  UISurvey,
  Workspace,
} from ".";

export function getSurveyResponders(
  workspace: <PERSON>only<PERSON>eep<Workspace>,
  survey: ReadonlyDeep<Survey>
): Readonly<PERSON>eep<Member[]> {
  const responders = workspace.members.filter((m) =>
    survey.answers.some((a) => a.responderId === m.id)
  );

  return responders;
}

export function getSlackUISurvey(
  workspace: ReadonlyDeep<Workspace>,
  survey: ReadonlyDeep<Survey>
): ReadonlyDeep<UISurvey> {
  const channel =
    workspace.channels.find((c) => c.id === survey.channelId) ?? null;
  const responders = getSurveyResponders(workspace, survey);
  const completionRate = Math.round(
    survey.participantsCount
      ? (responders.length / survey.participantsCount) * 100
      : 0
  );
  const createdBy =
    workspace.members.find((m) => m.id === survey.createdById) ?? null;

  return {
    id: survey.id,
    title: survey.title,
    channel,
    createdAt: survey.createdAt,
    status: survey.status,
    createdBy,
    startMessagePermalink: survey.startMessagePermalink,
    startMessageTS: survey.startMessageTS,
    completionRate,
    completedByCount: responders.length,
  };
}

type RunSurveyProps =
  | {
      origin: "admin-site";
      surveyTitle: string;
      templateId?: string;
      channelId: string;
      isAnonymous: boolean;
      questions: ReadonlyDeep<SurveyQuestionInput[]>;
    }
  | {
      origin: "slack";
      templateId: string;
      channelId: string;
      isAnonymous: boolean;
    };

export async function runSurvey(
  workspace: ReadonlyDeep<Workspace>,
  member: ReadonlyDeep<Member>,
  props: RunSurveyProps,
  logger: Logger,
  slackAdapter: SlackAdapter
): Promise<[ReadonlyDeep<Workspace>, Survey]> {
  const { templateId, channelId, isAnonymous } = props;

  const template = workspace.surveyTemplates.find((t) => t.id === templateId);
  const shouldAddTemplate = template != null && !template.isDefault;
  const templateTitle = template?.title ?? "Untitled";

  const title: string =
    props.origin === "admin-site"
      ? props.surveyTitle || templateTitle
      : templateTitle;

  const questions =
    props.origin === "admin-site" ? props.questions : template?.questions ?? [];

  const newSurvey: Survey = {
    id: crypto.randomUUID(),
    title,
    createdAt: new Date(),
    updatedAt: new Date(),
    createdById: member.id,
    status: SurveyStatus.InProgress,
    templateId: shouldAddTemplate ? template.id : null,
    template: shouldAddTemplate ? template : null,
    defaultTemplateId: template?.isDefault ? template.id : null,
    channelId,
    participantsCount: 1,
    isAnonymous,
    startMessagePermalink: "",
    startMessageTS: "",
    answers: [],
    questions: questions.map((q) => {
      return {
        ...q,
        id: crypto.randomUUID(),
        singleOptions: q.singleOptions?.map((s) => ({
          id: crypto.randomUUID(),
          value: typeof s === "string" ? s : s.value,
        })),
        multipleOptions: q.multipleOptions?.map((s) => ({
          id: crypto.randomUUID(),
          value: typeof s === "string" ? s : s.value,
        })),
        scaleOptions: q.scaleOptions?.map((s) => ({
          ...s,
          id: crypto.randomUUID(),
        })),
      };
    }),
  };

  const response = await slackAdapter.runNewSurvey(newSurvey);

  assert(
    response.channel,
    "Expected 'channel' to exist at chat.postMessage response"
  );
  assert(response.ts, "Expected 'ts' to exist at chat.postMessage response");

  newSurvey.startMessageTS = response.ts;
  const permalink = await slackAdapter.getMessagePermalink(
    channelId,
    response.ts
  );

  newSurvey.startMessagePermalink = permalink;

  let updatedWorkspace = { ...workspace };
  const updatedSurveys = updatedWorkspace.surveys.concat(newSurvey);

  updatedWorkspace = { ...workspace, surveys: updatedSurveys };

  logger
    .withContext({ member, workspace, survey: newSurvey })
    .info("Member started new survey");

  return [updatedWorkspace, newSurvey];
}
