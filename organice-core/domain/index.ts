/* eslint-disable import/no-cycle */
/* eslint-disable max-classes-per-file */

import assert from "assert";
import crypto from "crypto";

import type { Installation as SlackInstallation } from "@slack/bolt";
import { Logger as SlackLogger } from "@slack/logger";
import {
  AuthTestResponse,
  ChatPostMessageResponse,
  ChatUpdateResponse,
} from "@slack/web-api";
import { Profile } from "@slack/web-api/dist/response/UsersProfileGetResponse";
import { isValid } from "date-fns";
import { isValidPhoneNumber } from "libphonenumber-js";
import { isEqual, uniqBy } from "lodash";
import moment from "moment-timezone";

import { formatPhoneNumber } from "../utils/phone";

import { removeAnnouncementReminder } from "./announcements";
import { TRIAL_DURATION_IN_MILLISECONDS } from "./billing";
import {
  collectMemberData,
  countries,
  parseCustomFieldFromSlackField,
  formatFieldValueForSlack,
  formatPolicyFields,
  FormattedPolicy,
} from "./data-completion";
import { updateMemberNextTimeOffResetDates } from "./time-offs";

export type ReadonlyDeep<T> = {
  readonly [P in keyof T]: T[P] extends (...args: unknown[]) => unknown
    ? T[P]
    : ReadonlyDeep<T[P]>;
};

export interface SlackBotToken {
  appId: string;
  botId: string;
  botMemberId: string;
  scopes: string[];
  token: string;
}

export interface SlackMemberToken {
  memberId: string;
  scopes: string[];
  token: string;
}

export const HOW_MANY_TIMES_SHOW_FEATURE_BANNER = 3;

export enum SupportedFeature {
  Celebration = "CELEBRATION",
  Kudos = "KUDOS",
  OrgChart = "ORG_CHART",
  Surveys = "SURVEYS",
  TimeOffs = "TIME_OFFS",
}

export interface ProgressBar {
  amountOfAllFields: number;
  amountOfFilledFields: number;
}

interface ChartLayout {
  clusterHorizontal: boolean;
  dataField?: string;
}

export type Status =
  | {
      type: "ok";
    }
  | { error: string; type: "error" };

export interface ReferralDetailsButton {
  name: string;
  position: string;
  referrerId: string;
  email: string;
  positionId: string;
}

export interface PositionCandidate {
  name: string;
  email: string;
  notes: string;
  status: string;
  referrer: PositionReferrer;
}
export interface PositionReference {
  bonusDescription: string | null;
  jobDescriptionLink: string | null;
  isIncludeToReferrals: boolean | null;
  hiringManagerId: string | null;
  candidates: PositionCandidate[];
}

export interface TeamProfile {
  fields: CustomField[];
  sections: CustomFieldSection[];
  defaultManagerFieldId: string | null;
  defaultDepartmentFieldId: string | null;
  defaultTeamFieldId: string | null;
  defaultPhoneFieldId: string | null;
  defaultTitleFieldId: string | null;
  defaultCountryFieldId: string | null;
}

export enum Color {
  Blue = "BLUE",
  Cyan = "CYAN",
  Sky = "SKY",
  Emerald = "EMERALD",
  Lime = "LIME",
  Teal = "TEAL",
  Green = "GREEN",
  Yellow = "YELLOW",
  Amber = "AMBER",
  Orange = "ORANGE",
  Red = "RED",
  Rose = "ROSE",
  Violet = "VIOLET",
  Fuchsia = "FUCHSIA",
  Pink = "PINK",
}

export interface Reference {
  isEnable: boolean;
  channel: string;
}

export interface SlackPlan {
  plan: "free" | "std" | "plus" | "enterprise" | "compliance";
  updatedAt: Date;
}

export interface MembersCompletionCounts {
  onboarded: number;
  progress: number;
  all: number;
}

export interface DigestMessage {
  ts: string;
  channel: string;
  positions: string;
  updatedAt: number;
  createdAt: number | null;
}

export interface Announcement {
  id: string;
  message: string;
  sentAt?: Date | null;
  type:
    | "INTRODUCE_CELEBRATIONS"
    | "INTRODUCE_HIRING"
    | "INTRODUCE_KUDOS"
    | "INTRODUCE_ORG_CHART"
    | "INTRODUCE_TIME_OFFS"
    // TODO: delete
    | "KUDOS_WEELY_REMINDER";
  memberId?: Member["id"];
  channelId: Channel["id"];
}

export interface PublishedNotificationState {
  start: Date;
  end: Date;
  blocks: Record<
    string,
    {
      paginatorPage: number;
    }
  >;
}

export interface PublishedNotification {
  id: string;
  messageTS: string;
  sentAt?: Date | null;
  state: PublishedNotificationState;
  channelId: string;
  notificationId: string;
}

export interface Link {
  id: string;
  title: string;
  expandedNodes: string[];
  visibleNodes: string[];
  visibleFields: string[];
  visibleViews: string[];
  chartLayout: ChartLayout;
  createdAt?: Date;
  updatedAt?: Date;
}

export enum AccrualsFrequency {
  Week = "Week",
  Month = "Month",
}

export enum YearStart {
  Calendar = "Calendar",
  StartDate = "StartDate",
}

export interface TimeOffPolicyType {
  typeId: TimeOffType["id"];
  onStartQuota: number;
  rollOverToNextYear: boolean;
  yearStart: YearStart;
  accrualsQuota: number;
  accuralsFrequency: AccrualsFrequency | null;
  nextAccruals: Date | null;
  maxCapacity: number;
}

export enum DayOfWeek {
  MONDAY = 1,
  TUESDAY = 2,
  WEDNESDAY = 3,
  THURSDAY = 4,
  FRIDAY = 5,
  SATURDAY = 6,
  SUNDAY = 7,
}

export interface TimeOffPolicy {
  id: string;
  title: string;
  isDefault: boolean;
  typePolicies: TimeOffPolicyType[];
  notifyAccruals: boolean;
  workDays: DayOfWeek[];
  includedWeekendDays: DayOfWeek[];
}

export interface Holiday {
  id: string;
  startDate: Date;
  endDate: Date;
  name: string;
  description: string;
  isOfficial: boolean;
  country: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface HolidaysSettings {
  countries: string[];
  channel: Channel | null;
  createdAt: Date;
  updatedAt: Date;
}

interface SoftDeleted {
  requests: TimeOffRequest[];
}

export interface CalendarFeed {
  id: string;
  title: string;
  memberId: Member["id"] | null;
  filters: {
    managers: string[];
    departments: string[];
    teams: string[];
    countries: string[];
    types: string[];
  };
}

// eslint-disable-next-line @typescript-eslint/consistent-type-definitions
export type TextBlockData = {
  text: string;
};

export enum ButtonBlockType {
  timeOffs = "TIME_OFFS",
  kudos = "KUDOS",
  link = "LINK",
}

// eslint-disable-next-line @typescript-eslint/consistent-type-definitions
export type ButtonBlockData = {
  type: ButtonBlockType;
  title: string;
  url?: string;
};

export enum NotificationBlockType {
  text = "TEXT",
  button = "BUTTON",
  timeOffs = "TIME_OFFS",
  celebrations = "CELEBRATIONS",
  holidays = "HOLIDAYS",
}

export enum TimeOffRequestStatus {
  Pending = "PENDING",
  Approved = "APPROVED",
  Rejected = "REJECTED",
}

// eslint-disable-next-line @typescript-eslint/consistent-type-definitions
export type TimeOffsBlockData = {
  status: TimeOffRequestStatus;
  managers: string[];
  departments: string[];
  teams: string[];
  countries: string[];
  types: string[];
};

export enum CelebrationType {
  Birthday = "BIRTHDAY",
  Anniversary = "ANNIVERSARY",
}

// eslint-disable-next-line @typescript-eslint/consistent-type-definitions
export type CelebrationsBlockData = {
  managers: string[];
  departments: string[];
  teams: string[];
  countries: string[];
  type: CelebrationType | null;
};

export enum HolidayType {
  Official = "OFFICIAL",
  Unofficial = "UNOFFICIAL",
}

// eslint-disable-next-line @typescript-eslint/consistent-type-definitions
export type HolidaysBlockData = {
  countries: string[];
  type: HolidayType | null;
  includeDescription: boolean;
};

export interface INotificationBlock<TData extends Record<string, unknown>> {
  id: string;
  type: NotificationBlockType;
  data: TData;
}

export type NotificationBlock = INotificationBlock<
  | TextBlockData
  | ButtonBlockData
  | TimeOffsBlockData
  | CelebrationsBlockData
  | HolidaysBlockData
>;

export type NotificationBlockData =
  | {
      id: string;
      type: NotificationBlockType.text;
      data: TextBlockData;
    }
  | {
      id: string;
      type: NotificationBlockType.button;
      data: ButtonBlockData;
    }
  | {
      id: string;
      type: NotificationBlockType.timeOffs;
      data: ReadonlyDeep<TimeOffRequest>[];
    }
  | {
      id: string;
      type: NotificationBlockType.celebrations;
      data: ReadonlyDeep<CelebrationEvent>[];
    }
  | {
      id: string;
      type: NotificationBlockType.holidays;
      data: {
        includeDescription: boolean;
        holidays: ReadonlyDeep<Holiday>[];
      };
    };

export function isTextBlock(
  block: ReadonlyDeep<NotificationBlock>
): block is INotificationBlock<TextBlockData> {
  return block.type === NotificationBlockType.text;
}

export function isButtonBlock(
  block: ReadonlyDeep<NotificationBlock>
): block is INotificationBlock<ButtonBlockData> {
  return block.type === NotificationBlockType.button;
}

export function isTimeOffsBlock(
  block: ReadonlyDeep<NotificationBlock>
): block is INotificationBlock<TimeOffsBlockData> {
  return block.type === NotificationBlockType.timeOffs;
}

export function isCelebrationsBlock(
  block: ReadonlyDeep<NotificationBlock>
): block is INotificationBlock<CelebrationsBlockData> {
  return block.type === NotificationBlockType.celebrations;
}

export function isHolidaysBlock(
  block: ReadonlyDeep<NotificationBlock>
): block is INotificationBlock<HolidaysBlockData> {
  return block.type === NotificationBlockType.holidays;
}

export enum NotificationsFrequency {
  Day = "DAY",
  Week = "WEEK",
  Month = "MONTH",
}

export interface Notification {
  id: string;
  title: string;
  isActive: boolean;
  createdById: string;
  createdAt: Date;
  updatedAt: Date;
  blocks: NotificationBlock[];
  settings: NotificationSettings;
}

export interface NotificationTemplate {
  id: string;
  title: string;
  description: string;
  blocks: NotificationBlock[];
  settings: Pick<NotificationSettings, "frequency" | "day" | "time">;
}

export interface NotificationSettings {
  frequency: NotificationsFrequency;
  day?: number;
  time: string;
  timezone: string;
  channelId: string | null;
}

export interface Workspace {
  id: string;
  name: string;
  url: string;
  iconUrl?: string;
  members: Member[];
  policy: Policy;
  billing: Billing;
  customFields: CustomField[];
  teams: Team[];
  onboarding: Onboarding;
  slackBotToken?: SlackBotToken;
  slackMemberTokens: Record<Member["id"], SlackMemberToken | undefined>;
  orgTree: OrgChart;
  predictedOrgTree: PredictedOrgChart;
  slackPlan: SlackPlan;
  installedAt?: Date;
  installedBy?: string;
  reference: {
    isEnable: boolean;
    channel: string;
    message?: DigestMessage;
  };
  wasDeleted: boolean;
  reports: {
    enabled: boolean;
  };
  channels: Channel[];
  celebrationSettings: CelebrationSettings;
  timeOffs: TimeOffs;
  slackIntegration: boolean;
  announcements: Announcement[];
  kudosSettings: KudosSettings;
  surveyTemplates: SurveyTemplate[];
  surveys: Survey[];
  links: Link[];
  holidays: Holiday[];
  holidaysSettings: HolidaysSettings;
  slackStatusChanges: SlackStatusChange[];
  softDeleted: SoftDeleted;
  calendarFeeds: CalendarFeed[];
  chartLayout: ChartLayout;
  updatedAt?: Date;
  notifications: Notification[];
  notificationTemplates: NotificationTemplate[];
  publishedNotifications: PublishedNotification[];
}

export interface TimeOffType {
  id: string;
  label: string;
  color: Color;
  emoji: string;
  slackEmoji: string;
  hint?: string;
}

export enum TimeOffNotificationType {
  TimeOffRequest = "TIME_OFF_REQUEST",
  TimeOffStatusToMemberManager = "TIME_OFF_STATUS_TO_MEMBER_MANAGER",
  TimeOffStatusToRequester = "TIME_OFF_STATUS_TO_REQUESTER",
  TimeOffChangeToRequester = "TIME_OFF_CHANGE_TO_REQUESTER",
  TimeOffReminder = "TIME_OFF_REMINDER",

  UNUSED__TimeOffStatusToManager = "TIME_OFF_STATUS_TO_MANAGER", // not used anymore, but left here as this type is still in the database
  UNUSED__TimeOffRequestSent = "TIME_OFF_REQUEST_SENT", // not used anymore, but left here as this type is still in the database
}

export enum DeletedTimeOffNotificationsType {
  RequesterMessage = "REQUESTER_MESSAGE",
  ApprovedTimeOffManagerMessage = "APPROVED_TIME_OFF_MANAGER_MESSAGE",
}

export interface TimeOffs {
  channelId?: Channel["id"] | null;
  requests: TimeOffRequest[];
  types: TimeOffType[];
  isEnable: boolean;
  createDiscussionChannelWhenMultipleApprovers: boolean;
  changeSlackStatus: boolean;
  policies: TimeOffPolicy[];
}

export type CalendarEvent = TimeOffRequest | Holiday | CelebrationEvent;

export interface TimeOffRequest {
  id: string;
  memberId: Member["id"];
  approversIds: Member["id"][];
  createdAt: Date;
  updatedAt: Date;
  startDate: Date;
  endDate: Date;
  type: string;
  comment?: string;
  status: TimeOffRequestStatus;
  rejectReason?: string;
  notifications: TimeOffNotification[];
  handledBy?: string;
}

export interface CelebrationEvent {
  memberId: Member["id"];
  startDate: Date;
  endDate: Date;
  type: CelebrationType;
}

export interface UIGroup {
  groupTitle: "Today" | "Pending" | "Upcoming" | "Past" | null;
}

export type UICalendarEvent = CalendarEvent & UIGroup;

export enum SlackStatusType {
  TimeOff = "TimeOff",
  Holiday = "Holiday",
  Birthday = "Birthday",
  Anniversary = "Anniversary",
}

export interface SlackStatusChange {
  type: SlackStatusType;
  stashedStatus: MemberStatusStash | null;
  memberId: Member["id"];
  refId: Holiday["id"] | null;
}

export interface MemberStatusStash {
  emoji: string;
  text: string;
  expiresAt?: Date;
}

export interface TimeOffNotification {
  recipientId: Member["id"];
  ts: string;
  type: TimeOffNotificationType;
  scheduleTime?: Date;
  deleted?: boolean;
}

export interface Template {
  id: string;
  text: string;
}

export interface AnnouncementReminder {
  hideBirthday?: boolean;
  cancelled: boolean;
  celebrantId: string;
  message: string;
  gifUrl?: string | null;
  type: "birthday" | "anniversary";
  messages: { ts: string; channel: string }[];
  createdAt: Date;
  eventDate: Date;
  announcementsDisabled?: boolean;
  accessSuspended?: boolean;
  editedBy?: string;
}

export type SentAnnouncement =
  | {
      channel: string;
      message: string;
      celebrantId: string;
      scheduledMessageId: string;
      gifUrl?: string | null;
      timeToPost: Date;
      type: "birthday" | "anniversary";
    }
  | {
      channel: string;
      timeToPost: Date;
      type: "weeklyDigest";
    };

export interface FeaturesAnnouncement {
  channel: string;
  message: string;
}

export interface CelebrationSettings {
  common: {
    channel?: string;
    timeOfPosting: string;
    timezone: string | null;
    whenToPostIfHappensOnWeekend: "FRIDAY" | "WEEKEND" | "MONDAY";
  };
  birthday: {
    changeSlackStatus: boolean;
    enabled: boolean;
    includeGif: boolean;
    templates: ReadonlyDeep<Template[]>;
  };
  anniversary: {
    changeSlackStatus: boolean;
    enabled: boolean;
    includeGif: boolean;
    templates: ReadonlyDeep<Template[]>;
  };
  reminders: AnnouncementReminder[];
  sentAnnouncements: SentAnnouncement[];
}

export interface WorkspaceRepository {
  getWorkspaces(): Promise<Workspace[]>;
  getWorkspace(workspaceId: string): Promise<ReadonlyDeep<Workspace | null>>;
  getWorkspaceByLink(linkId: string): Promise<ReadonlyDeep<Workspace | null>>;
  getWorkspaceByFeed(feedId: string): Promise<ReadonlyDeep<Workspace | null>>;
  setWorkspace(
    workspace: ReadonlyDeep<Workspace>
  ): Promise<ReadonlyDeep<Workspace>>;
}

export interface Department {
  id: string;
  timestamp: number;
  type: "department";
  parentId: string;
  managerId: string | null;
  title: string;
  color: Color;
  subordinates: (Department | Position)[];
}

export interface PositionReferer {
  id: string;
  name: string;
}

export interface Candidate {
  name: string;
  email: string;
  notes: string;
  status: {
    title: CandidateStatus;
    userId: string | null;
  };
  referrer: PositionReferer;
  position?: string;
  messages?: CandidateMessageToReferrer[];
}

export enum ReferralMessageType {
  ASK_HIRED_BY_REFERRAL = "ask-hired-by-referral",
  ASK_CLOSE_THE_POSITION = "ask-close-the-position",
}

export enum EmployeeReferenceStatus {
  HIRED_BY_REFERRAL = "Hired by referral",
  HIRED_NOT_BY_REFERRAL = "Hired not by referral",
}

export interface ReferralMessage {
  type: ReferralMessageType;
  messages: ReferralApiMessage[] | null;
  resolvedBy: string | null;
}

interface ReferralApiMessage {
  channel: string;
  ts: string;
  userId: string;
}

export interface Position {
  id: string;
  timestamp: number;
  type: "position";
  parentId: string;
  memberId: string | null;
  managedDepartmentId: string | null;
  managerTeamIds: string[];
  title: string;
  teamIds: string[];
  subordinates: (Department | Position)[];
  reference: {
    isIncludeToReferrals: boolean | null;
    bonusDescription: string | null;
    jobDescriptionLink: string | null;
    hiringManagerId?: string | null;
    candidates: Candidate[];
    messages?: ReferralMessage[] | null;
  } | null;
  withoutManagerManual: boolean;
}

export interface RootNode {
  id: string;
  type: "root";
  subordinates: (Department | Position)[];
}

export interface OrgChart {
  rootNode: RootNode;
}

export enum KudosResetFrequency {
  Week = "WEEK",
  Month = "MONTH",
}

export interface KudosSettings {
  enable: boolean;
  kudosLimit: number;
  channel?: Channel;
  values: KudosValue[];
  resetFrequency: KudosResetFrequency;
  resetDay: number;
  resetTime: string;
  resetTimezone: string;
}

export interface KudosValue {
  id: string;
  emoji: string;
  title: string;
  description?: string;
}

export interface Member {
  id: string;
  isAdmin: boolean;
  isSlackWorkspaceAdmin: boolean;
  isSlackWorkspaceOwner: boolean;
  isSlackWorkspacePrimaryOwner: boolean;
  name?: string;
  photoUrl?: string;
  photo512Url?: string;
  photo72Url?: string;
  realName?: string;
  displayName?: string;
  email?: string;
  organicePhone?: string;
  birthday?: Date;
  hideBirthday?: boolean;
  notes?: string;
  updated?: Date;
  botState: MemberBotState;
  organiceCustomFileds: Record<PresetPolicyId | string, string | undefined>;
  joinedAt?: Date;
  country?: string;
  status?: {
    text: string;
    emoji: string;
    expiresAt?: Date;
  } | null;
  firstClickedProPlanAt?: Date;
  howManyTimesHomeTabVisited: number;
  timeOffTypePolicyId?: string;
  isSlackBillable: boolean;
  timeOffs: Record<
    TimeOffType["id"],
    | {
        balance: number;
        nextResetAt: Date | null;
      }
    | undefined
  >;
  timezone: string | null;
}

export interface Channel {
  id: string;
  name: string;
  is_general?: boolean;
  membersCount?: number;
}

export interface CustomFieldSection {
  id: string;
  label: string;
  order: number;
  sectionType?: string;
  isHidden?: boolean;
}

export interface Team {
  id: string;
  color: Color;
  label: string;
  managerId: string | null;
}
export interface MemberBotState {
  wasAskedAboutManager?: boolean;
  wasAskedAboutDepartment?: boolean;
  shouldAskAboutVacancy?: boolean;
  wasInitialOrgTreePositionSet?: boolean;
  sentNotifications?: MemberSentNotification[];
  customFieldId?: string;
  type:
    | "not-notified"
    | "required-photo-url"
    | "required-manager"
    | "required-vacancy"
    | "required-custom-field"
    | "ok";
  toBeConfirmed?: string;
  welcomeMessageTs?: string;
  updateProfileMessageTs?: string;
  updateProfileRequestedAt?: number;
  channel?: string;
  updatesCount?: Record<string, number | undefined>;
  haveBeenAdminsNotificationOrgTreeSent?: boolean;
  wasInitiallySyncedWithSlack?: boolean;
}

export interface MemberSentNotification {
  tag:
    | "field-request"
    | "admin-need-help"
    | "primary-owner-install-request"
    | "follow-up-message"
    | "free-plan-ends-soon"
    | "appsumo-subscription-ends-soon"
    | "discounted-subscription-offer"
    | "free-plan-ended"
    | "trial-expired"
    | "owner-approve"
    | "time-offs-channel-was-archived"
    | "notifications-channel-was-archived";
  medium: "slack" | "email";
  timestamp: number;
  ts?: string;
}

export enum PresetPolicyId {
  MANAGER = "manager",
  JOB_TITLE = "jobTitle",
  PHOTO_URL = "photo",
  PHONE = "organicePhone",
  BIRTHDAY = "birthday",
  ANNIVERSARY = "anniversary",
  DEPARTMENT = "department",
  COUNTRY = "country",
  TEAMS = "teams",
}

export type PolicyFieldType = "user" | "date" | "text" | "link";

export interface PolicyCustomField {
  id: string;
  label: string;
  type: PolicyFieldType;
  required: boolean;
  publiclyAvailable: boolean;
  order: number;
  slackFieldId: string | null;
}

export interface Policy {
  [PresetPolicyId.MANAGER]: {
    slackFieldId: string | null;
  };
  [PresetPolicyId.JOB_TITLE]: {
    slackFieldId: string | null;
  };
  [PresetPolicyId.PHONE]: {
    slackFieldId: string | null;
    required: boolean;
  };
  [PresetPolicyId.BIRTHDAY]: {
    slackFieldId: string | null;
    required: boolean;
  };
  [PresetPolicyId.ANNIVERSARY]: {
    slackFieldId: string | null;
    required: boolean;
  };
  [PresetPolicyId.PHOTO_URL]: {
    required: boolean;
  };
  [PresetPolicyId.DEPARTMENT]: {
    slackFieldId: string | null;
  };
  [PresetPolicyId.TEAMS]: {
    slackFieldId: string | null;
  };
  [PresetPolicyId.COUNTRY]: {
    required: boolean;
    slackFieldId: string | null;
  };
  customPolicyFields: PolicyCustomField[];
  notifications: {
    schedule: Schedule;
    welcomeMessage: string;
    followUpMessage: string;
    enable: boolean;
  };
  wasInitiallySyncedWithSlack: boolean;
}

export interface CustomFieldPolicy {
  id: string;
  required: boolean;
}

export type StripePlan = "pro" | "enterprise";

export interface Billing {
  subscription:
    | {
        ok: boolean;
        endsAt: Date;
        type: "trial";
      }
    | {
        ok: boolean;
        customerId: string;
        subscriptionId: string;
        plan: StripePlan;
        type: "stripe";
      }
    | {
        ok: boolean;
        endsAt: Date;
        code: string;
        type: "appsumo";
      };
}

export interface CustomField {
  id: string;
  label: string;
  type: "text" | "options_list" | "date" | "link" | "user";
  visible: boolean;
  hint?: string;
  variants?: string[];
  isEditableOnSlackOnly: boolean;
  order: number;
  isSlackDefaultField: boolean;
  sectionId: string | null;
}

export enum Schedule {
  DAY = "Every day",
  TWO_DAYS = "Every other day",
  WEEKLY = "Every week",
  HALF_HOUR = "Half hour",
}

export type MemberData = Record<PresetPolicyId | string, string | undefined> & {
  managerPositionId?: string;
};

export type MemberPolicyViolation =
  | {
      field: "manager";
      type: "user";
      label: string;
      id: PresetPolicyId.MANAGER;
    }
  | {
      field: "department";
      type: "options_list";
      label: string;
      id: PresetPolicyId.DEPARTMENT;
      options: { id: string; label: string }[];
    }
  | {
      field: "teams";
      type: "options_list";
      label: string;
      id: PresetPolicyId.TEAMS;
      options: { id: string; label: string }[];
    }
  | {
      field: "photoUrl";
      type: "text";
      label: string;
      id: PresetPolicyId.PHOTO_URL;
    }
  | {
      field: "custom";
      id: string;
      label: string;
      type: CustomField["type"];
    };

export interface SlackMessage {
  memberId: string;
  text: string;
}

export interface UpdateMember {
  memberId: string;
  realName?: string;
  email?: string;
  customFields?: UpdateMemberCustomFields;
  status?: {
    statusText: string;
    statusEmoji: string;
    statusExpiration?: Date;
  };
}

export type UpdateMemberCustomFields = Record<
  string,
  { value: string | null; alt?: string } | undefined
>;

export class CustomFieldHasntBeenUpdatedError extends Error {
  constructor(
    expectedCustomFields: UpdateMemberCustomFields,
    actualCustomFields?: Record<string, string>
  ) {
    super(
      actualCustomFields
        ? `Slack member profile hasn't been updated (expected: ${JSON.stringify(
            expectedCustomFields
          )}, instead got: ${JSON.stringify(actualCustomFields)})`
        : `Slack member profile hasn't been updated (expected: ${JSON.stringify(
            expectedCustomFields
          )}, instead got error)`
    );
  }
}

export class StatusHasntBeenUpdatedError extends Error {
  constructor(status: {
    statusText: string;
    statusEmoji: string;
    statusExpiration?: Date;
  }) {
    super(
      `Slack member status hasn't been updated (expected: ${JSON.stringify(
        status
      )}, instead got error)`
    );
  }
}

export interface UIAdapterMember {
  id: string;
  isAdmin: boolean;
  name?: string;
  photoUrl?: string;
  photo512Url?: string;
  realName?: string;
  title?: string;
  email?: string;
  updated?: Date;
  hideBirthday?: boolean;
}

export interface UIMember extends UIAdapterMember {
  department?: UIDepartment;
  manager?: UIAdapterMember & { position?: Position };
  teams: UITeam[];
  position?: Position;
  howManyTimesHomeTabVisited: number;
}

export interface UIDepartment {
  id: string;
  label: string;
}

export interface UITeam {
  id: string;
  label: string;
}

export enum HomePages {
  OrgChart = "ORG_CHART",
  Calendar = "CALENDAR",
  Kudos = "KUDOS",
  Surveys = "SURVEYS",
  Dashboard = "DASHBOARD",
}

export type HomePageState =
  | OrgChartHomePageState
  | CalendarHomePageState
  | KudosHomePageState
  | SurveysHomePageState
  | DashboardHomePageState
  | {
      // Old calendar state version
      version: "v1";
      currentPage: HomePages.Calendar;
      currentCalendarPage:
        | "my_time_offs"
        | "incoming_requests"
        | "organization_requests";
      selectedStatus: "PENDING" | "APPROVED" | "REJECTED" | "ALL";
      selectedUser?: string;
      paginatorPage: number;
      hideAllBlockWithPeopleAreMissingOnOrgChart: boolean;
      showPeopleAreMissingOnOrgChartBlock: boolean;
      hiringBlockState: boolean;
    }
  | {
      // Old calendar state version
      version: "v2";
      currentPage: HomePages.Calendar;
      currentCalendarPage:
        | "my_time_offs"
        | "incoming_requests"
        | "organization_requests";
      selectedStatus: "UPCOMING" | "ALL";
      selectedUser?: string;
      paginatorPage: number;

      // Common home page state
      hideAllBlockWithPeopleAreMissingOnOrgChart: boolean;
      showPeopleAreMissingOnOrgChartBlock: boolean;
      hiringBlockState: boolean;
    }
  | {
      // Old calendar state version
      version: "v3";
      currentPage: HomePages.Calendar;
      currentCalendarPage:
        | "my_time_offs"
        | "incoming_requests"
        | "organization_requests";
      selectedUser?: string;
      paginatorPage: number;

      // Common home page state
      hideAllBlockWithPeopleAreMissingOnOrgChart: boolean;
      showPeopleAreMissingOnOrgChartBlock: boolean;
      hiringBlockState: boolean;
    };

export interface OrgChartHomePageProps {
  teams: ReadonlyDeep<UITeam[]>;
  departments: ReadonlyDeep<Department[]>;
  filteredMembersOnOrganizationTab: ReadonlyDeep<UIMember[]>;
  currentMember: ReadonlyDeep<UIMember> | ReadonlyDeep<Member>;
  trialExpired: boolean;
  assignedPositionNumber: number;
  membersWithoutManager: ReadonlyDeep<Member>[];
  installedBy?: string;
  joinedPeople: ReadonlyDeep<UIMember[]>;
  companyName: string;
  openPositions: DigestPosition[];
  enableHiring: boolean;
  isCompletedOnboarding: boolean;
  workspaceId: string;
  enabledFeatures: ReadonlyDeep<SupportedFeature[]>;
}

export interface OrgChartHomePageState {
  /**
   * If you change this interface, do as follows:
   * 1. Copy the current shape of this interface to HomePageState, append it like `| { version: "v1"; ... }`
   * 2. Increment the version below
   */
  version: "v1";
  currentPage: HomePages.OrgChart;
  organizationSelectedDepartment?: {
    id: string;
    name: string;
  };
  organizationSelectedTeam?: {
    id: string;
    name: string;
  };

  // Common home page state
  hideAllBlockWithPeopleAreMissingOnOrgChart: boolean;
  showPeopleAreMissingOnOrgChartBlock: boolean;
  hiringBlockState: boolean;
}

export interface CalendarHomePageProps {
  currentMember: ReadonlyDeep<Member>;
  trialExpired: boolean;
  events: ReadonlyDeep<UICalendarEvent[]>;
  workspaceMembers: ReadonlyDeep<UIAdapterMember[]>;
  memberSubordinates: ReadonlyDeep<(Department | Position)[]>;
  memberData?: MemberData;
  selectedUser?: ReadonlyDeep<Member>;
  allTimeOffsRequests: ReadonlyDeep<TimeOffRequest[]>;
  countries: string[];
  timeOffTypes: ReadonlyDeep<TimeOffType[]>;
  workspaceId: string;
  enabledFeatures: ReadonlyDeep<SupportedFeature[]>;
}

export interface CalendarHomePageState {
  /**
   * If you change this interface, do as follows:
   * 1. Copy the current shape of this interface to HomePageState, append it like `| { version: "v1"; ... }`
   * 2. Increment the version below
   */
  version: "v4";
  currentPage: HomePages.Calendar;
  currentCalendarPage:
    | "my_calendar"
    | "incoming_requests"
    | "organization_calendar";
  selectedUser?: string;
  selectedEventType?: "time_off" | "holiday" | "celebration";
  paginatorPage: number;

  // Common home page state
  hideAllBlockWithPeopleAreMissingOnOrgChart: boolean;
  showPeopleAreMissingOnOrgChartBlock: boolean;
  hiringBlockState: boolean;
}

export interface DashboardHomePageState {
  version: "v1";
  currentPage: HomePages.Dashboard;

  // Common home page state
  hideAllBlockWithPeopleAreMissingOnOrgChart: boolean;
  showPeopleAreMissingOnOrgChartBlock: boolean;
  hiringBlockState: boolean;
}

export interface KudosHomePageState {
  /**
   * If you change this interface, do as follows:
   * 1. Copy the current shape of this interface to HomePageState, append it like `| { version: "v1"; ... }`
   * 2. Increment the version below
   */
  version: "v1";
  currentPage: HomePages.Kudos;
  currentKudosPage: "given_kudos" | "received_kudos";
  paginatorPage: number;

  // Common home page state
  hideAllBlockWithPeopleAreMissingOnOrgChart: boolean;
  showPeopleAreMissingOnOrgChartBlock: boolean;
  hiringBlockState: boolean;
}

export interface SurveysHomePageProps {
  currentMember: ReadonlyDeep<UIMember> | ReadonlyDeep<Member>;
  trialExpired: boolean;
  workspaceId: string;
  surveys: ReadonlyDeep<UISurvey[]>;
  enabledFeatures: ReadonlyDeep<SupportedFeature[]>;
}

export interface SurveysHomePageState {
  /**
   * If you change this interface, do as follows:
   * 1. Copy the current shape of this interface to HomePageState, append it like `| { version: "v1"; ... }`
   * 2. Increment the version below
   */
  version: "v1";
  currentPage: HomePages.Surveys;
  currentSurveyPage: "all_surveys" | "closed_surveys" | "in_progress_surveys";
  paginatorPage: number;

  // Common home page state
  hideAllBlockWithPeopleAreMissingOnOrgChart: boolean;
  showPeopleAreMissingOnOrgChartBlock: boolean;
  hiringBlockState: boolean;
}

export interface RenderTrialExpiredModalOptions {
  triggerId: string;
  members: ReadonlyDeep<Member[]>;
  me: ReadonlyDeep<Member>;
  trialExpired: boolean;
}

export interface RunSurveyModalProps {
  templates: ReadonlyDeep<SurveyTemplate[]>;
}

export interface PassSurveyModalProps {
  survey: ReadonlyDeep<Survey>;
}
export interface PassSurveyModalState {
  surveyId: string;
}

export interface RunSurveyModalState {
  currentTemplate: ReadonlyDeep<SurveyTemplate> | null;
}

export enum CandidateStatus {
  InReview = "In Review",
  Approved = "Approved",
  Rejected = "Rejected",
}

export interface CandidateMessageToReferrer {
  channel: string;
  ts: string;
  userId: string;
}

export interface ReferralDetailsOptions {
  userId: string;
  positionId: string;
  candidate: ReadonlyDeep<Candidate>;
  isShowHandledByInStatus: boolean;
}

export interface PositionReferrer {
  referrerId: string;
  count: number;
}

export interface DigestPosition extends Position {
  manager?: ReadonlyDeep<Member>;
  countOfApproved?: number;
  referrers?: Record<string, PositionReferrer>;
}

export interface ResultOfReference {
  memberId: string;
  name: string;
  position: string;
  status: string;
  positionId: string;
}

export interface ReferralModalOptions {
  triggerId: string;
  positionId: string;
  title: string;
  triggeredBy?: string;
  showPeopleAreMissingOnOrgChartBlock?: boolean;
}

export interface SlackWorkspace {
  id: string;
  name: string;
  iconUrl?: string;
  url: string;
  profile: TeamProfile;
}

export interface SlackMember {
  id: string;
  isSlackWorkspaceAdmin: boolean;
  isSlackWorkspaceOwner: boolean;
  isSlackWorkspacePrimaryOwner: boolean;
  name?: string;
  photoUrl?: string;
  photo512Url?: string;
  photo72Url?: string;
  realName?: string;
  displayName?: string;
  title?: string;
  phone?: string;
  email?: string;
  isSlackBillable?: boolean;
  tz?: string;
  tzOffset?: number;
  status?: {
    text: string;
    emoji: string;
    expiresAt?: Date;
  } | null;
}

export interface GreetUserOptions {
  sender: Member;
  recipient: Member;
  message: string;
  installedBy?: string;
}

export interface VacancyOption {
  text: string;
  value: string;
}

export interface UpdateMemberOptions {
  throwIfHaventChangedFields?: boolean;
}

export interface StripeSubscription {
  id: string;
  quantity: number;
  interval: "month" | "year";
}

export interface StripePrice {
  discounted: boolean;
  interval: "month" | "year";
  perUnit: {
    amount: number;
    formatted: string;
  };
  total: {
    amount: number;
    formatted: string;
  };
}

export interface GenerateCheckoutUrlInput {
  workspace: {
    id: string;
    name: string;
  };
  member: {
    id: string;
    email: string;
  };
  quantity: number;
  price:
    | {
        discounted: false;
        interval: "month";
      }
    | {
        discounted: false;
        interval: "year";
      }
    | {
        discounted: true;
        interval: "year";
      };
  successUrl: string;
  cancelUrl?: string;
}

export interface StripeAdapter {
  generateCustomerPortalUrl(
    customerId: string,
    returnUrl: string
  ): Promise<string>;
  generateCheckoutUrl(input: GenerateCheckoutUrlInput): Promise<string>;
  getSubscription(subscriptionId: string): Promise<StripeSubscription>;
  setQuantityForNextInvoice(
    subscriptionId: string,
    quantity: number
  ): Promise<void>;
  setQuantityAndChargeImmediately(
    subscriptionId: string,
    quantity: number
  ): Promise<void>;
  getPrices(quantity: number): Promise<StripePrice[]>;
}

/* eslint-disable class-methods-use-this */
export class NoOpStripeAdapter implements StripeAdapter {
  generateCheckoutUrl(): Promise<string> {
    throw new TypeError(
      "Can't invoke StripeAdapter methods as one or more Stripe env variables hasn't been set"
    );
  }

  generateCustomerPortalUrl(): Promise<string> {
    throw new TypeError(
      "Can't invoke StripeAdapter methods as one or more Stripe env variables hasn't been set"
    );
  }

  getSubscription(): Promise<StripeSubscription> {
    throw new TypeError(
      "Can't invoke StripeAdapter methods as one or more Stripe env variables hasn't been set"
    );
  }

  setQuantityForNextInvoice(): Promise<void> {
    throw new TypeError(
      "Can't invoke StripeAdapter methods as one or more Stripe env variables hasn't been set"
    );
  }

  setQuantityAndChargeImmediately(): Promise<void> {
    throw new TypeError(
      "Can't invoke StripeAdapter methods as one or more Stripe env variables hasn't been set"
    );
  }

  getPrices(): Promise<StripePrice[]> {
    throw new TypeError(
      "Can't invoke StripeAdapter methods as one or more Stripe env variables hasn't been set"
    );
  }
}
/* eslint-enable class-methods-use-this */

export interface NotifyAdminAboutAnnouncementProps {
  celebrantId: string;
  channelId: string;
  workspaceId: string;
  messageText: AnnouncementResponse;
  eventType: "birthday" | "anniversary";
  showCallToActionButton: "go_to_settings" | "go_to_pricing" | null;
  showEditButton: boolean;
  showDontSendButton: boolean;
  gifUrl?: string | null;
}

export interface EditReminderMessageProps
  extends NotifyAdminAboutAnnouncementProps {
  ts: string;
  editedBy?: string;
  cancelled?: boolean;
}

export interface GlobalContext {
  workspace: ReadonlyDeep<Workspace>;
}

export interface SlackAdapter {
  readonly token: string;
  readonly userToken?: string;

  getWorkspace(workspaceId: string): Promise<SlackWorkspace | null>;
  getWorkspaceMembers(workspaceId: string): Promise<SlackMember[]>;
  getMembersPartially(workspaceId: string): Promise<SlackMember[]>;
  getWorkspaceMembersCount(workspaceId: string): Promise<number>;
  getMember(memberId: string): Promise<SlackMember | null>;
  getSlackBotApplicationByAppId(appId: string): SlackBotApplication;
  greetUser(
    options: ReadonlyDeep<GreetUserOptions>
  ): Promise<{ channel: string; ts: string }>;
  sugestUserToInstall(workspaceId: string, memberId: string): Promise<void>;
  getChannelMessages(channel: string): Promise<{ ts: string }[]>;
  getChannels(workspaceId: string): Promise<Channel[]>;
  deleteMessage(
    workspaceId: string,
    ts: string,
    channel: string
  ): Promise<void>;
  askToPopulateProfileFields(
    workspaceId: string,
    memberId: string,
    progress: ProgressBar,
    channel: string | undefined,
    ts: string | undefined
  ): Promise<{ ts: string | undefined; channel: string | undefined }>;
  sendPopulatedProfileMessage(
    workspace: ReadonlyDeep<Workspace>,
    memberId: string,
    channel: string | undefined,
    ts: string | undefined
  ): Promise<{ ts: string | undefined; channel: string | undefined }>;
  sendFollowUpMessage(
    memberId: string,
    threadTs: string,
    input: {
      violations: ReadonlyDeep<MemberPolicyViolation>[];
      isPolicyRecentlyChanged: boolean;
      installedBy: string;
    }
  ): Promise<{ ts: string | undefined; channel: string | undefined }>;
  sendFieldAskMessage(
    workspaceId: string,
    memberId: string,
    violation: ReadonlyDeep<MemberPolicyViolation>,
    progress: ProgressBar
  ): Promise<{ ts: string | undefined; channel: string | undefined }>;
  approveFieldAskMessage(
    workspaceId: string,
    memberId: string,
    violation: MemberPolicyViolation,
    channel: string,
    ts: string,
    progress: ProgressBar,
    value?: string,
    updatesCount?: number
  ): Promise<void>;
  rejectFieldAskMessage(
    workspaceId: string,
    memberId: string,
    violation: MemberPolicyViolation,
    channel: string,
    ts: string,
    progress: ProgressBar,
    error: string
  ): Promise<void>;
  sendVacancyAskMessage(
    memberId: string,
    vacancies: ReadonlyDeep<VacancyOption>[]
  ): Promise<{ ts: string | undefined; channel: string | undefined }>;
  approveVacancyAskMessage(
    memberId: string,
    channel: string,
    ts: string,
    vacancies?: ReadonlyDeep<VacancyOption>[]
  ): Promise<void>;
  rejectVacancyAskMessage(
    memberId: string,
    channel: string,
    ts: string,
    error: string,
    vacancies?: ReadonlyDeep<VacancyOption>[]
  ): Promise<void>;
  thankUserForImageUpdate(workspaceId: string, memberId: string): Promise<void>;
  thankUser(
    workspaceId: string,
    memberId: string,
    position: ReadonlyDeep<Position>,
    progress: ProgressBar
  ): Promise<void>;
  notifyAdminAboutAdminsRights(
    adminId: string,
    userId: string,
    workspaceId: string
  ): Promise<void>;
  notifyAdminAboutOrgTree(
    adminId: string,
    memberGroups: MembersCompletionCounts,
    workspaceId: string
  ): Promise<void>;
  notifyAdminAboutAnnouncement(
    options: NotifyAdminAboutAnnouncementProps
  ): Promise<{ ts: string; channel: string }>;
  postAnnouncement(
    channelId: string,
    messageText: string,
    postAt: Date,
    gifUrl?: string | null
  ): Promise<{ scheduledMessageId: string }>;
  postMessage(
    channelId: string,
    messageText: string,
    threadTS?: string
  ): Promise<ChatPostMessageResponse>;
  sendSurveyReminder(survey: ReadonlyDeep<Survey>): Promise<void>;
  updateSurveyMessageOnDelete(survey: ReadonlyDeep<Survey>): Promise<void>;
  sendDiscountedSubscriptionOffer(
    workspaceId: string,
    memberId: string,
    regularPrice: string,
    discountedPrice: string
  ): Promise<{ ts: string | undefined; channel: string | undefined }>;

  askAdminToHelpFillFields(
    adminId: string,
    memberGroups: MembersCompletionCounts,
    needHelpMemberIds: string[],
    workspaceId: string
  ): Promise<void>;

  runNewSurvey(survey: Survey): Promise<ChatPostMessageResponse>;

  updateMember(
    workspaceId: string,
    { memberId, realName, customFields }: UpdateMember,
    policy: ReadonlyDeep<Policy>,
    // userToken might be retrieved using findTokenForSlackAdapterMemberUpdate
    userToken: string,
    options?: UpdateMemberOptions
  ): Promise<Profile | null>;
  renderOrgChartHomeTab(
    workspace: ReadonlyDeep<Workspace>,
    member: ReadonlyDeep<Member> | ReadonlyDeep<UIMember>,
    state: OrgChartHomePageState
  ): Promise<void>;
  renderCalendarHomeTab(
    workspace: ReadonlyDeep<Workspace>,
    memberId: string,
    state?: CalendarHomePageState
  ): Promise<void>;
  renderKudosHomeTab(
    workspace: ReadonlyDeep<Workspace>,
    member: ReadonlyDeep<Member>,
    state: KudosHomePageState | "given_kudos" | "received_kudos"
  ): Promise<void>;
  openTrialExpiredModal({
    triggerId,
    members,
    me,
    trialExpired,
  }: RenderTrialExpiredModalOptions): Promise<void>;
  renderSurveysHomeTab(
    workspace: ReadonlyDeep<Workspace>,
    member: ReadonlyDeep<Member> | ReadonlyDeep<UIMember>,
    state: SurveysHomePageState
  ): Promise<void>;
  renderDashboardHomeTab(
    workspace: ReadonlyDeep<Workspace>,
    member: ReadonlyDeep<Member> | ReadonlyDeep<UIMember>,
    state: DashboardHomePageState
  ): Promise<void>;
  renderActiveTab(
    workspace: ReadonlyDeep<Workspace>,
    member: ReadonlyDeep<Member>,
    state: HomePageState | null,
    getDefaultState?: (pages: {
      [HomePages.Surveys]: SurveysHomePageState;
      [HomePages.Kudos]: (
        kudosPreset: KudosHomePageState["currentKudosPage"]
      ) => KudosHomePageState;
    }) => HomePageState
  ): Promise<void>;

  getSlackBillingInfo(workspaceId: string): Promise<Pick<SlackPlan, "plan">>;
  getProfileConfiguration(
    workspace: ReadonlyDeep<Workspace>,
    memberId: string | null,
    userToken: string | null
  ): Promise<TeamProfile>;
  getMemberCustomFields(
    workspaceId: string,
    memberId: string,
    policy: ReadonlyDeep<Policy>
  ): Promise<Record<string, string | undefined>>;
  sendCandidateStatusToReferrer(options: ResultOfReference): Promise<void>;
  sendReferralCandidateDetails(
    options: ReferralDetailsOptions
  ): Promise<ChatUpdateResponse>;
  openReferralModal(options: ReferralModalOptions): Promise<void>;
  sendOpenPositionsNotification(
    channel: string,
    digestPositions: DigestPosition[]
  ): Promise<ChatUpdateResponse>;
  updateOpenPositionsNotification(
    channel: string,
    ts: string,
    digestPositions: DigestPosition[]
  ): Promise<ChatUpdateResponse>;
  updateReferralCandidateDetails(
    positionId: string,
    channel: string,
    ts: string,
    candidate: ReadonlyDeep<Candidate>,
    userId: string,
    isShowHandledByInStatus: boolean
  ): Promise<ChatUpdateResponse>;
  askManagersEmployeeHiredByReferral(
    node: ReadonlyDeep<Position>,
    userId: string,
    employeeId: string,
    candidates: ReadonlyDeep<Candidate>[]
  ): Promise<ChatUpdateResponse>;
  updateAskHiredByReferralMessage(
    channel: string,
    ts: string,
    node: ReadonlyDeep<Position>,
    isShowResolvedBy: boolean | null,
    status: EmployeeReferenceStatus,
    employeeId: string,
    userId: string,
    resolvedBy?: string
  ): Promise<ChatUpdateResponse>;
  sendReferralRecognitionMessage(
    memberId: string,
    referrerId: string,
    employeeId: string
  ): Promise<void>;
  askPrimaryOwnerToApprove(
    workspaceId: string,
    adminId: string,
    senderId: string,
    app: SlackBotApplication
  ): Promise<ChatPostMessageResponse>;
  notifyAdminsAboutTrialExpiration(memberId: string): Promise<void>;
  notifyAdminsAboutAppSumoSubscriptionSoonExpiration(
    memberId: string
  ): Promise<void>;
  getMessagePermalink(channel: string, messageTS: string): Promise<string>;
  sendKudosAsDirectMessage(
    senderId: string,
    recipientsIds: string[],
    data: {
      message: string;
      quantity: number;
      gifUrl?: string;
      kudosValues: KudosValue[];
    }
  ): Promise<{ ts: string; channel: string }[]>;
  postKudosInChannel(
    senderId: string,
    channel: string,
    recipientsIds: string[],
    data: {
      message: string;
      quantity: number;
      gifUrl?: string;
      kudosValues: KudosValue[];
    }
  ): Promise<{ ts: string; channel: string }>;

  postNotification(
    channel: string,
    blocks: NotificationBlockData[],
    title: string,
    context: GlobalContext,
    notificationId?: string
  ): Promise<{ ts: string }>;

  updateNotification(
    notificationId: string,
    channel: string,
    blocks: NotificationBlockData[],
    title: string,
    state: ReadonlyDeep<PublishedNotificationState>,
    ts: string,
    context: GlobalContext
  ): Promise<{ ts: string }>;

  createPrivateChannel(
    name: string,
    participants: string[]
  ): Promise<{ channel: string }>;
  archiveChannel(channel: string): Promise<void>;
  isDirectMessageChannel(channelId: string): boolean;
  sendTimeOffRequest(
    timeOff: ReadonlyDeep<TimeOffRequest>,
    channel: string,
    context: GlobalContext
  ): Promise<{ ts: string; channel: string }>;
  notifyMemberMangerAboutTimeOffStatus(
    workspaceId: string,
    managerId: string,
    timeOff: ReadonlyDeep<TimeOffRequest>,
    context: GlobalContext
  ): Promise<{ ts: string; channel: string }>;
  notifyRequesterAboutTimeOff(
    timeOff: ReadonlyDeep<TimeOffRequest>,
    context: GlobalContext
  ): Promise<{ ts: string; channel: string }>;
  notifyRequesterAboutTimeOffChange(
    prevTimeOff: ReadonlyDeep<TimeOffRequest>,
    timeOff: ReadonlyDeep<TimeOffRequest>,
    editor: ReadonlyDeep<Member>,
    context: GlobalContext
  ): Promise<{ ts: string; channel: string }>;
  notifyAboutSendingKudos(
    senderId: string,
    recipientsIds: string[],
    kudosLeft: number
  ): Promise<{ ts: string; channel: string }>;
  updateTimeOffRequest(
    context: GlobalContext,
    timeOff: ReadonlyDeep<TimeOffRequest>,
    channel: string,
    ts: string,
    deletedByMemberId?: string
  ): Promise<void>;
  updateReminderMessage(options: EditReminderMessageProps): Promise<void>;
  sendReminderAboutTimeOffRequest(
    channel: string,
    postAt: Date,
    threadTs?: string
  ): Promise<{
    ts: string;
    channel: string;
  }>;
  deleteReminder(channel: string, ts: string): Promise<void>;
  postTimeOffInChannel(
    timeOff: ReadonlyDeep<TimeOffRequest>,
    channel: string,
    context: GlobalContext
  ): Promise<void>;
  postTimeOffChangeInChannel(
    prevTimeOff: ReadonlyDeep<TimeOffRequest>,
    timeOff: ReadonlyDeep<TimeOffRequest>,
    editor: ReadonlyDeep<Member>,
    channel: string,
    context: GlobalContext
  ): Promise<void>;
  notifyThatApprovedTimeOffWasDeleted(
    channel: string,
    deletedBy: string,
    type: DeletedTimeOffNotificationsType,
    threadTs: string
  ): Promise<void>;
  checkToken(token: string): Promise<AuthTestResponse>;
  sendTimeOffsChannelWasArchivedReminder(
    workspaceId: string,
    adminId: string
  ): Promise<{ ts: string; channel: string }>;
  sendNotificationsChannelWasArchivedReminder(
    workspaceId: string,
    adminId: string,
    notificationTitles: string[]
  ): Promise<{ ts: string; channel: string }>;
  notifyAboutReceivingAccruals(
    memberId: string,
    timeOffPolicyTypes: ReadonlyDeep<TimeOffPolicyType>[],
    context: GlobalContext
  ): Promise<void>;

  setUserPhoto(fileUrl: string, userToken: string): Promise<boolean>;
}

export interface SlackInstallHandlerProps {
  application: SlackBotApplication;
  workspace: {
    name: string;
    plan: SlackPlan["plan"];
    membersCount: number;
    url: string;
  };
  installedBy: {
    name: string;
    jobTitle: string;
    position: string;
    email: string;
  };
}

export interface SlackUninstallHandlerProps {
  application: SlackBotApplication;
  workspace: {
    name: string;
    url: string;
  };
}

export interface SlackReinstallHandlerProps {
  application: SlackBotApplication;
  workspace: {
    name: string;
    url: string;
  };
}

export interface SwitchToStripeSubscriptionProps {
  workspace: {
    name: string;
  };
  billing: {
    customerId: string;
    subscriptionId: string;
  };
}

export interface SwitchToAppSumoSubscriptionProps {
  workspace: {
    name: string;
  };
  billing: {
    code: string;
  };
}

export interface StripePaymentFailureProps {
  workspace: {
    name: string;
  };
  billing: {
    customerId: string;
    subscriptionId: string;
  };
}

export interface EmailCollectedHandlerProps {
  fullName: string;
  email: string;
  companyName: string;
}

export interface ProductOwnerNotifier {
  handleInstallation(props: SlackInstallHandlerProps): Promise<void>;
  handleUninstallation(props: SlackUninstallHandlerProps): Promise<void>;
  handleReinstallation(props: SlackReinstallHandlerProps): Promise<void>;
  handleSwitchToAppSumoSubscription(
    props: SwitchToAppSumoSubscriptionProps
  ): Promise<void>;
  handleSwitchToStripeSubscription(
    props: SwitchToStripeSubscriptionProps
  ): Promise<void>;
  handleStripePaymentFailure(props: StripePaymentFailureProps): Promise<void>;
  handleEmailCollection(props: EmailCollectedHandlerProps): Promise<void>;
}

export class NoOpProductOwnerNotifier implements ProductOwnerNotifier {
  // eslint-disable-next-line class-methods-use-this, @typescript-eslint/no-empty-function
  async handleInstallation(): Promise<void> {}
  // eslint-disable-next-line class-methods-use-this, @typescript-eslint/no-empty-function
  async handleUninstallation(): Promise<void> {}
  // eslint-disable-next-line class-methods-use-this, @typescript-eslint/no-empty-function
  async handleReinstallation(): Promise<void> {}
  // eslint-disable-next-line class-methods-use-this, @typescript-eslint/no-empty-function
  async handleSwitchToAppSumoSubscription(): Promise<void> {}
  // eslint-disable-next-line class-methods-use-this, @typescript-eslint/no-empty-function
  async handleSwitchToStripeSubscription(): Promise<void> {}
  // eslint-disable-next-line class-methods-use-this, @typescript-eslint/no-empty-function
  async handleStripePaymentFailure(): Promise<void> {}
  // eslint-disable-next-line class-methods-use-this, @typescript-eslint/no-empty-function
  async handleEmailCollection(): Promise<void> {}
}

export interface LoggerContext {
  [key: string]: unknown;
  workspace?: {
    id: string;
    name?: string;
  };
  member?: {
    id: string;
    realName?: string;
    name?: string;
    email?: string;
  };
  survey?: {
    id: string;
    title: string;
    status: SurveyStatus;
  };
  surveyTemplate?: {
    id: string;
    title: string;
    isDefault: boolean;
  };
}

export interface Logger extends SlackLogger {
  info(...args: unknown[]): void;
  error(...args: unknown[]): void;

  withContext(context: LoggerContext): Logger;
  withPrefix(label: string): Logger;
}

export interface Onboarding {
  completed: boolean;
  finishedAt?: number;
  finishedBy?: Member["id"];
  bookmarkedFeatures: SupportedFeature[];
}

export interface OrgChartPredictor {
  predictOrgChart(input: PredictOrgChartInput): Promise<PredictedRootNode>;
}

export interface PredictOrgChartInput {
  members: PredictOrgChartMemberInput[];
}

export interface PredictOrgChartMemberInput {
  id: string;
  name: string;
  title: string;
}

export interface PredictedOrgChart {
  requested: boolean;
  rootNode?: PredictedRootNode;
}

export interface PredictedRootNode {
  subordinates: PredictedPosition[];
  type: "root";
}

export interface PredictedPosition {
  member: PredictOrgChartMemberInput | null;
  memberId: string | null;
  subordinates: (PredictedPosition | PredictedDepartment)[];
  title: string;
  type: "position";
}

export interface PredictedDepartment {
  name: string;
  subordinates: (PredictedPosition | PredictedDepartment)[];
  type: "department";
}

export interface AnnouncementResponse {
  reminderMsg: string;
  congratulationText?: string;
}

export class MessageNotFoundError extends Error {
  constructor(message: string) {
    super(message);
    this.name = "MessageNotFoundError";
  }
}

/**
 * Pass it to SlackAdapter.updateMember
 */
export function findTokenForSlackAdapterMemberUpdate(
  workspace: ReadonlyDeep<Workspace>,
  targetMember: ReadonlyDeep<Member>
): string | null {
  const canUpdateTargetMember: (m: ReadonlyDeep<Member>) => boolean =
    targetMember.isSlackWorkspacePrimaryOwner ||
    targetMember.isSlackWorkspaceOwner
      ? (m) => m.isSlackWorkspacePrimaryOwner
      : targetMember.isSlackWorkspaceAdmin
      ? (m) => m.isSlackWorkspaceOwner
      : (m) => m.isSlackWorkspaceAdmin;

  const authorizedMembers = workspace.members.filter(canUpdateTargetMember);
  const token = Object.values(workspace.slackMemberTokens).find(
    (t) =>
      t?.scopes.includes("users.profile:write") &&
      authorizedMembers.some((m) => m.id === t.memberId)
  )?.token;

  return token ?? null;
}

export const defaultWelcomeMessage = `Hey, <RECIPIENT> 👋 Welcome here
My name is OrgaNice - my goal is to help keep your team information filled and available for all users.
<INSTALLER_NAME> asked you to update the following information:`;

export const defaultFollowUpMessage = `Hey, <RECIPIENT> 👋
My goal is to help keep your team information filled and available for all users.
Could you please update the following information in your profile:`;

export class AnotherOrgaNiceBotAlreadyInstalledError extends Error {
  constructor(public alreadyInstalledApp: SlackBotApplication) {
    super();
  }
}

export enum SlackBotApplication {
  ORGANICE = "ORGANICE",
  KUDOS = "KUDOS",
}

export async function onSlackOAuthInstall(
  repository: WorkspaceRepository,
  getSlackAdapter: (slackBotToken: ReadonlyDeep<SlackBotToken>) => SlackAdapter,
  productOwnerNotifier: ProductOwnerNotifier,
  slackInstallation: SlackInstallation<"v2">,
  logger: Logger,
  application: SlackBotApplication
): Promise<void> {
  const workspaceId = (slackInstallation.team?.id ??
    slackInstallation.enterprise?.id)!;
  const memberId = slackInstallation.user.id;
  const existingWorkspace = await repository.getWorkspace(workspaceId);

  assert(
    slackInstallation.appId,
    "Expected installation to come with an app id"
  );
  assert(
    slackInstallation.bot,
    "Expected installation to come with a bot token"
  );
  assert(
    slackInstallation.user.scopes,
    "Expected installation to come with a user scopes"
  );
  assert(
    slackInstallation.user.token,
    "Expected installation to come with a user token"
  );

  const slackBotToken: SlackBotToken = {
    appId: slackInstallation.appId,
    botId: slackInstallation.bot.id,
    botMemberId: slackInstallation.bot.userId,
    scopes: slackInstallation.bot.scopes,
    token: slackInstallation.bot.token,
  };
  const slackOAuthMemberToken: SlackMemberToken = {
    memberId: slackInstallation.user.id,
    scopes: slackInstallation.user.scopes,
    token: slackInstallation.user.token,
  };

  if (existingWorkspace) {
    let workspace = { ...existingWorkspace };

    if (workspace.wasDeleted) {
      logger.info("OrgaNice Reinstalled", {
        method: "openid",
      });
      await productOwnerNotifier.handleReinstallation({
        application,
        workspace: {
          name: workspace.name,
          url: workspace.url,
        },
      });

      if (
        application === SlackBotApplication.KUDOS &&
        !workspace.onboarding.bookmarkedFeatures.includes(
          SupportedFeature.Kudos
        )
      ) {
        workspace = {
          ...workspace,
          onboarding: {
            ...workspace.onboarding,
            bookmarkedFeatures: [
              ...workspace.onboarding.bookmarkedFeatures,
              SupportedFeature.Kudos,
            ],
          },
        };
      }
    } else if (
      workspace.slackBotToken &&
      workspace.slackBotToken.appId !== slackInstallation.appId
    ) {
      const slackAdapter = getSlackAdapter(workspace.slackBotToken);
      const existingApp = slackAdapter.getSlackBotApplicationByAppId(
        workspace.slackBotToken.appId
      );

      throw new AnotherOrgaNiceBotAlreadyInstalledError(existingApp);
    }

    logger.info("Sign In", {
      method: "oauth",
    });

    workspace = {
      ...workspace,
      slackBotToken,
      slackMemberTokens: {
        ...workspace.slackMemberTokens,
        [memberId]: slackOAuthMemberToken,
      },
      wasDeleted: false,
    };

    await repository.setWorkspace(workspace);
  } else {
    const slackAdapter = getSlackAdapter(slackBotToken);
    const slackWorkspace = await slackAdapter.getWorkspace(workspaceId);

    assert(slackWorkspace, `Workspace does not exist`);

    const { plan } = await slackAdapter.getSlackBillingInfo(workspaceId);

    const installedBy = await slackAdapter.getMember(slackInstallation.user.id);

    assert(
      installedBy,
      `Expected member with id "${slackInstallation.user.id}" to exist`
    );
    const membersCount = await slackAdapter.getWorkspaceMembersCount(
      slackWorkspace.id
    );

    await productOwnerNotifier.handleInstallation({
      application,
      workspace: {
        name: slackWorkspace.name,
        plan,
        membersCount,
        url: slackWorkspace.url,
      },
      installedBy: {
        name: installedBy.realName ?? "",
        jobTitle: installedBy.title ?? "",
        position: installedBy.isSlackWorkspacePrimaryOwner
          ? "Primary owner"
          : installedBy.isSlackWorkspaceOwner
          ? "Owner"
          : installedBy.isSlackWorkspaceAdmin
          ? "Admin"
          : "Regular user",
        email: installedBy.email ?? "",
      },
    });

    const rawMembers = [
      installedBy,
      ...(await slackAdapter.getMembersPartially(slackWorkspace.id)),
    ];

    let members = rawMembers.map((member): ReadonlyDeep<Member> => {
      const tz = member.tz ?? "";
      const timezone = `GMT ${moment.tz([], tz).format("Z")}`;

      return {
        ...member,
        organicePhone: member.phone,
        isAdmin: member.id === installedBy.id,
        isSlackWorkspaceAdmin: member.isSlackWorkspaceAdmin,
        botState: {
          type: "not-notified",
        },
        organiceCustomFileds: {},
        howManyTimesHomeTabVisited: 0,
        isSlackBillable: true,
        timeOffs: {},
        timezone,
      };
    });

    members = uniqBy(members, "id");

    const newWorkspace: ReadonlyDeep<Workspace> = {
      ...slackWorkspace,
      customFields: slackWorkspace.profile.fields,
      teams: [],
      holidays: [],
      holidaysSettings: {
        countries: [],
        channel: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      members,
      policy: {
        [PresetPolicyId.MANAGER]: {
          slackFieldId: slackWorkspace.profile.defaultManagerFieldId,
        },
        [PresetPolicyId.JOB_TITLE]: {
          slackFieldId: slackWorkspace.profile.defaultTitleFieldId,
        },
        [PresetPolicyId.PHOTO_URL]: {
          required: true,
        },
        [PresetPolicyId.PHONE]: {
          slackFieldId: slackWorkspace.profile.defaultPhoneFieldId,
          required: true,
        },
        [PresetPolicyId.BIRTHDAY]: {
          slackFieldId: null,
          required: true,
        },
        [PresetPolicyId.ANNIVERSARY]: {
          slackFieldId: null,
          required: true,
        },
        [PresetPolicyId.DEPARTMENT]: {
          slackFieldId: slackWorkspace.profile.defaultDepartmentFieldId,
        },
        [PresetPolicyId.TEAMS]: {
          slackFieldId: slackWorkspace.profile.defaultTeamFieldId,
        },
        [PresetPolicyId.COUNTRY]: {
          required: true,
          slackFieldId: slackWorkspace.profile.defaultCountryFieldId,
        },
        customPolicyFields: [],
        notifications: {
          schedule: Schedule.DAY,
          welcomeMessage: defaultWelcomeMessage,
          followUpMessage: defaultFollowUpMessage,
          enable: false,
        },
        wasInitiallySyncedWithSlack: false,
      },
      billing: {
        subscription: {
          ok: true,
          endsAt: new Date(
            new Date().valueOf() +
              (process.env.TRIAL_DURATION_IN_MINUTES
                ? Number(process.env.TRIAL_DURATION_IN_MINUTES) * 60 * 1000
                : TRIAL_DURATION_IN_MILLISECONDS)
          ),
          type: "trial",
        },
      },
      onboarding: {
        completed: false,
        bookmarkedFeatures:
          application === SlackBotApplication.KUDOS
            ? [SupportedFeature.Kudos]
            : [],
      },
      slackBotToken,
      slackMemberTokens: {
        [memberId]: slackOAuthMemberToken,
      },
      installedAt: new Date(),
      installedBy: slackInstallation.user.id,
      orgTree: {
        rootNode: {
          id: slackWorkspace.id,
          type: "root",
          subordinates: [],
        },
      },
      predictedOrgTree: {
        requested: false,
      },
      slackPlan: { plan, updatedAt: new Date() },
      wasDeleted: false,
      reference: {
        isEnable: false,
        channel: "",
      },
      reports: { enabled: true },
      channels: [],
      celebrationSettings: {
        common: {
          timeOfPosting: "9 am",
          timezone: null,
          whenToPostIfHappensOnWeekend: "MONDAY",
        },
        birthday: {
          changeSlackStatus: installedBy.isSlackWorkspaceAdmin,
          enabled: false,
          includeGif: true,
          templates: [],
        },
        anniversary: {
          changeSlackStatus: installedBy.isSlackWorkspaceAdmin,
          enabled: false,
          includeGif: true,
          templates: [],
        },
        reminders: [],
        sentAnnouncements: [],
      },
      timeOffs: {
        requests: [],
        types: [
          {
            id: "VACATION",
            color: Color.Amber,
            label: "Vacation",
            emoji: "🌴",
            slackEmoji: ":palm_tree:",
          },
          {
            id: "SICK_DAYS",
            color: Color.Red,
            label: "Sick Days",
            emoji: "🤒",
            slackEmoji: ":face_with_thermometer:",
          },
          {
            id: "UNPAID_LEAVE",
            color: Color.Orange,
            label: "Unpaid Leave",
            emoji: "💸",
            slackEmoji: ":money_with_wings:",
          },
          {
            id: "BUSINESS_TRIP",
            color: Color.Blue,
            label: "Business Trip",
            emoji: "✈️",
            slackEmoji: ":airplane:",
          },
          {
            id: "WORK_FROM_HOME",
            color: Color.Emerald,
            emoji: "🏠",
            label: "Work from Home",
            slackEmoji: ":house:",
          },
        ],
        isEnable: false,
        createDiscussionChannelWhenMultipleApprovers: false,
        changeSlackStatus: installedBy.isSlackWorkspaceAdmin,
        policies: [],
      },
      slackIntegration: false,
      announcements: [],
      kudosSettings: {
        enable: false,
        kudosLimit: 5,
        resetFrequency: KudosResetFrequency.Week,
        resetDay: 7,
        resetTime: "11 pm",
        resetTimezone: "GMT +00:00",
        values: [
          buildKudosValue({
            title: "Innovation",
            emoji: "🌟",
            description:
              "For those who think outside the box... and then crush the box for good measure!",
          }),
          buildKudosValue({
            title: "Teamwork",
            emoji: "🤝",
            description: "Because even superheroes need a sidekick.",
          }),
          buildKudosValue({
            title: "Integrity",
            emoji: "🔍",
            description:
              "Keeping it real, even when no one’s looking (or so we think).",
          }),
          buildKudosValue({
            title: "Customer Focus",
            emoji: "🎯",
            description:
              "For always hitting the bullseye, even with moving targets!",
          }),
          buildKudosValue({
            title: "Accountability",
            emoji: "📋",
            description: "Owning it like a boss—literally.",
          }),
          buildKudosValue({
            title: "Creativity",
            emoji: "🎨",
            description: "Turning coffee into code and doodles into designs.",
          }),
          buildKudosValue({
            title: "Empathy",
            emoji: "💖",
            description: "Feeling all the feels, one Slack message at a time.",
          }),
          buildKudosValue({
            title: "Excellence",
            emoji: "🏆",
            description: "For when 'good enough' just isn’t good enough.",
          }),
        ],
      },
      surveyTemplates: [],
      surveys: [],
      links: [],
      slackStatusChanges: [],
      softDeleted: {
        requests: [],
      },
      calendarFeeds: [],
      chartLayout: {
        clusterHorizontal: true,
        dataField: undefined,
      },
      notifications: [],
      publishedNotifications: [],
      notificationTemplates: [],
    };

    await repository.setWorkspace(newWorkspace);

    logger.info("OrgaNice Installed");
  }
}

export interface OpenIdMemberAuthentication {
  team: {
    id: string;
  };
  user: {
    id: string;
    token: string;
    scopes: string[];
  };
}

export async function onSlackOpenIdAuthentication(
  repository: WorkspaceRepository,
  logger: Logger,
  authentication: OpenIdMemberAuthentication
): Promise<void> {
  let workspace = await repository.getWorkspace(authentication.team.id);

  assert(workspace, "Workspace not found");

  if (authentication.user.id in workspace.slackMemberTokens) {
    workspace = {
      ...workspace,
      slackMemberTokens: {
        ...workspace.slackMemberTokens,
        [authentication.user.id]: {
          memberId: authentication.user.id,
          scopes: [
            ...new Set([
              ...workspace.slackMemberTokens[authentication.user.id]!.scopes,
              ...authentication.user.scopes,
            ]),
          ],
          token: authentication.user.token,
        },
      },
    };
  } else {
    workspace = {
      ...workspace,
      slackMemberTokens: {
        ...workspace.slackMemberTokens,
        [authentication.user.id]: {
          memberId: authentication.user.id,
          scopes: authentication.user.scopes,
          token: authentication.user.token,
        },
      },
    };
  }

  await repository.setWorkspace(workspace);

  logger.info("Sign In", {
    method: "openid",
  });
}

export function isFieldEditable(field: ReadonlyDeep<CustomField>): boolean {
  return !field.isEditableOnSlackOnly && field.visible;
}

export function replaceMember(
  workspace: ReadonlyDeep<Workspace>,
  member: ReadonlyDeep<Member>
): ReadonlyDeep<Workspace> {
  const index = workspace.members.findIndex((m) => m.id === member.id);

  assert(index !== -1, `Couldn't find the member with id = '${member.id}'`);

  if (isEqual(workspace.members[index], member)) {
    return workspace;
  }

  const members = [...workspace.members];

  members[index] = member;

  return {
    ...workspace,
    members,
  };
}

export function pickNRandom<T>(array: T[], length: number): T[] {
  const uniqueArray: T[] = [];

  const availableIndices: number[] = Array.from(
    { length: array.length },
    (_, i) => i
  );

  const uniqueArrayLength = Math.min(array.length, length);

  while (
    uniqueArray.length < uniqueArrayLength &&
    availableIndices.length > 0
  ) {
    const randomIndex = Math.floor(Math.random() * availableIndices.length);
    const randomMember = array[availableIndices[randomIndex]];

    uniqueArray.push(randomMember);
    availableIndices.splice(randomIndex, 1);
  }

  return uniqueArray;
}

export interface SurveyTemplate {
  id: string;
  title: string;
  isDefault: boolean;
  questions: ReadonlyDeep<SurveyQuestion[]>;
  createdById: Member["id"] | null;
  createdAt: Date | null;
  updatedAt: Date | null;
  usageCount: number;
}

export interface Survey {
  id: string;
  templateId: string | null;
  template: ReadonlyDeep<SurveyTemplate> | null;
  defaultTemplateId: string | null;
  title: string;
  startMessagePermalink: string;
  startMessageTS: string;
  isAnonymous: boolean;
  status: SurveyStatus;
  channelId: string | null;
  participantsCount: number;
  questions: ReadonlyDeep<SurveyQuestion[]>;
  answers: ReadonlyDeep<SurveyAnswer[]>;
  createdAt: Date;
  updatedAt: Date;
  createdById: Member["id"] | null;
}

export interface UISurvey {
  id: string;
  title: string;
  channel: ReadonlyDeep<Channel> | null;
  status: SurveyStatus;
  startMessagePermalink: string;
  startMessageTS: string;
  createdAt: Date;
  completedByCount: number;
  createdBy: ReadonlyDeep<Member> | null;
  completionRate: number;
}

export enum SurveyStatus {
  InProgress = "InProgress",
  Closed = "Closed",
}

export interface SurveyTemplateQuestion {
  id: string;
  title: string;
  required: boolean;
  type: SurveyQuestionType;
  singleOptions?: TextOption[];
  multipleOptions?: TextOption[];
  scaleOptions?: ScaleOption[];
}

export interface SurveyQuestion {
  id: string;
  title: string;
  required: boolean;
  type: SurveyQuestionType;
  singleOptions?: TextOption[];
  multipleOptions?: TextOption[];
  scaleOptions?: ScaleOption[];
}

export interface SurveyQuestionInput {
  title: string;
  required: boolean;
  type: SurveyQuestionType;
  singleOptions?: string[] | null;
  multipleOptions?: string[] | null;
  scaleOptions?: { label: string; value: number }[] | null;
}

export interface TextOption {
  id: string;
  value: string;
}

export interface ScaleOption {
  id: string;
  value: number;
  label: string;
}

export enum SurveyQuestionType {
  Text = "Text",
  Scale = "Scale",
  SingleOption = "SingleOption",
  MultipleOptions = "MultipleOptions",
}

export type SurveyAnswerValue =
  | TextOption[]
  | ScaleOption
  | TextOption
  | string
  | null;

export interface SurveyAnswer {
  id: string;
  questionId: SurveyQuestion["id"];
  responderId: Member["id"];
  value: SurveyAnswerValue;
}

export function convertTime12To24(time12: string): string {
  // eslint-disable-next-line prefer-const
  let [time, modifier] = time12.split(" ");

  if (time === "12") {
    time = "00";
  }

  if (modifier === "pm") {
    time = String(parseInt(time, 10) + 12);
  }

  return `${time}:00`;
}

export async function pullChannelsFromSlack(
  slackAdapter: SlackAdapter,
  workspace: ReadonlyDeep<Workspace>
): Promise<ReadonlyDeep<Workspace>> {
  const channels = await slackAdapter.getChannels(workspace.id);

  workspace = {
    ...workspace,
    channels,
  };

  return workspace;
}

export function isValidUrl(url: string): boolean {
  try {
    return new URL(url).hostname.includes(".");
  } catch (err) {
    const urlWithProtocol = getUrlWithProtocol(url);

    return url === urlWithProtocol ? false : isValidUrl(urlWithProtocol);
  }
}

export function getUrlWithProtocol(url: string): string {
  return url.includes("://") ? url : `https://${url}`;
}

type AddKudosValueInput = Pick<
  KudosValue,
  "emoji" | "title" | "description"
> & {
  id?: KudosValue["id"];
};

function buildKudosValue(
  input: Pick<AddKudosValueInput, "emoji" | "title" | "description">
): KudosValue {
  return {
    id: crypto.randomBytes(10).toString("hex"),
    emoji: input.emoji,
    title: input.title,
    description: input.description,
  };
}

export function addOrUpdateKudosValue(
  workspace: ReadonlyDeep<Workspace>,
  input: AddKudosValueInput
): ReadonlyDeep<Workspace> {
  const values =
    input.id != null
      ? workspace.kudosSettings.values.map((v) => {
          if (v.id === input.id) {
            return { ...input, id: v.id };
          }

          return v;
        })
      : [...workspace.kudosSettings.values, buildKudosValue(input)];

  return {
    ...workspace,
    kudosSettings: { ...workspace.kudosSettings, values },
  };
}

interface DeleteKudosValueInput {
  id: string;
}

export function deleteKudosValue(
  workspace: ReadonlyDeep<Workspace>,
  input: DeleteKudosValueInput
): ReadonlyDeep<Workspace> {
  const kudosValues = workspace.kudosSettings.values.filter(
    (item) => item.id !== input.id
  );

  return {
    ...workspace,
    kudosSettings: {
      ...workspace.kudosSettings,
      values: kudosValues,
    },
  };
}

interface CustomFieldInput {
  id: string;
  message:
    | string
    | {
        photoUrl: string;
        photo512Url: string;
        photo72Url: string;
      };
}

export function updateMemberCustomFields(
  currentWorkspace: ReadonlyDeep<Workspace>,
  currentMember: ReadonlyDeep<Member>,
  customField: CustomFieldInput,
  logger: Logger,
  slackAdapter: SlackAdapter | null
): {
  workspace: ReadonlyDeep<Workspace>;
  member: ReadonlyDeep<Member>;
  customFields: Record<string, string>;
} {
  const policy = currentWorkspace.policy;

  let customFields: Record<string, string> = {};
  let member = currentMember;
  let workspace = currentWorkspace;

  if (customField.id === PresetPolicyId.PHONE) {
    assert(
      typeof customField.message === "string",
      `You can set only string value to «${customField.id}» field`
    );

    const isValidPhone =
      !!customField.message && isValidPhoneNumber(customField.message);
    const phoneFieldSlackId = policy[PresetPolicyId.PHONE].slackFieldId;

    if (customField.message && !isValidPhone) {
      throw new PhoneError();
    }

    member = {
      ...member,
      organicePhone: formatPhoneNumber(customField.message),
    };

    if (phoneFieldSlackId) {
      customFields = {
        ...customFields,
        [phoneFieldSlackId]: formatPhoneNumber(customField.message),
      };
    }
  } else if (customField.id === PresetPolicyId.COUNTRY) {
    assert(
      typeof customField.message === "string",
      `You can set only string value to «${customField.id}» field`
    );

    const country = countries.find((c) => c.name === customField.message);

    assert(!customField.message || country, "Unknown country");

    const countrySlackFieldId = policy[PresetPolicyId.COUNTRY].slackFieldId;
    const policyField = formatPolicyFields(workspace).find(
      (p) => p.id === PresetPolicyId.COUNTRY
    );

    assert(policyField, "Unknown policy field");

    member = {
      ...member,
      country: customField.message,
    };

    if (countrySlackFieldId) {
      customFields = {
        ...customFields,
        [countrySlackFieldId]: formatFieldValueForSlack(
          policyField,
          customField.message
        ),
      };
    }
  } else if (customField.id === PresetPolicyId.BIRTHDAY) {
    assert(
      typeof customField.message === "string",
      `You can set only string value to «${customField.id}» field`
    );

    const birthday = new Date(customField.message);
    const year = new Date(customField.message).getFullYear();
    const birthdayFieldSlackId = policy[PresetPolicyId.BIRTHDAY].slackFieldId;

    const currentYear = new Date().getFullYear();

    const isValidYear = currentYear - year >= 16 && currentYear - year <= 99;

    if (!isValidYear && customField.message !== "") {
      throw new BirthdayError();
    }

    if (
      slackAdapter &&
      member.birthday &&
      !isEqual(birthday, member.birthday as Date)
    ) {
      workspace = removeAnnouncementReminder(
        slackAdapter,
        logger,
        workspace,
        member,
        "birthday"
      );
    }

    member = {
      ...member,
      birthday:
        // ! due to the fact that we cannot set undefined into db via prisma
        customField.message === "" ? null! : new Date(customField.message),
    };

    if (birthdayFieldSlackId) {
      customFields = {
        ...customFields,
        [birthdayFieldSlackId]: customField.message,
      };
    }
  } else if (customField.id === PresetPolicyId.ANNIVERSARY) {
    assert(
      typeof customField.message === "string",
      `You can set only string value to «${customField.id}» field`
    );

    const anniversary = new Date(customField.message);
    const joinedAt = isValid(anniversary) ? anniversary : undefined;
    const anniversaryFieldSlackId =
      policy[PresetPolicyId.ANNIVERSARY].slackFieldId;

    if (
      slackAdapter &&
      member.joinedAt &&
      !isEqual(anniversary, member.joinedAt as Date)
    ) {
      workspace = removeAnnouncementReminder(
        slackAdapter,
        logger,
        workspace,
        member,
        "anniversary"
      );
    }
    member = {
      ...member,
      joinedAt,
    };

    if (anniversaryFieldSlackId) {
      customFields = {
        ...customFields,
        [anniversaryFieldSlackId]: customField.message,
      };
    }
    const timeOffPolicy =
      workspace.timeOffs.policies.find(
        (p) => p.id === member.timeOffTypePolicyId
      ) ?? workspace.timeOffs.policies.find((p) => p.isDefault);

    if (timeOffPolicy) {
      workspace = updateMemberNextTimeOffResetDates(
        timeOffPolicy,
        [member],
        workspace,
        new Date()
      );
      member = workspace.members.find((m) => m.id === member.id)!;
    }
  } else if (customField.id === PresetPolicyId.PHOTO_URL) {
    assert(
      typeof customField.message === "object",
      "You can set only object value to PHOTO_URL field"
    );

    member = {
      ...member,
      photoUrl: customField.message.photoUrl,
      photo512Url: customField.message.photo512Url,
      photo72Url: customField.message.photo72Url,
    };
  } else {
    assert(
      typeof customField.message === "string",
      `You can set only string value to «${customField.id}» field`
    );

    const { slackFieldId, type } =
      policy.customPolicyFields.find(
        (policyField) => policyField.id === customField.id
      ) ?? {};

    if (
      type === "link" &&
      customField.message &&
      !isValidUrl(customField.message)
    ) {
      throw new InvalidLinkError();
    }

    member = {
      ...member,
      organiceCustomFileds: {
        ...member.organiceCustomFileds,
        [customField.id]: customField.message,
      },
    };

    if (slackFieldId) {
      customFields = {
        ...customFields,
        [slackFieldId]: customField.message,
      };
    }
  }

  return { workspace, customFields, member };
}

export class ValidationError extends Error {}

export class PhoneError extends ValidationError {
  constructor(message?: string) {
    super(message ?? "Invalid phone number");
  }
}

export class InvalidLinkError extends ValidationError {
  constructor() {
    super("Invalid link");
  }
}

export class BirthdayError extends ValidationError {
  constructor() {
    super("Looks like the year of the birthday you picked is not correct");
  }
}

export async function formatCustomFieldsForSlackUpdate(
  workspace: ReadonlyDeep<Workspace>,
  member: ReadonlyDeep<Member>,
  slackAdapter: SlackAdapter
): Promise<UpdateMemberCustomFields> {
  const formattedPolicy = formatPolicyFields(workspace);
  const memberData = collectMemberData(workspace, member);

  function convertToSlackValue(pf: FormattedPolicy): string {
    return pf.id === PresetPolicyId.BIRTHDAY && member.hideBirthday === true
      ? ""
      : formatFieldValueForSlack(pf, memberData[pf.id]) ?? "";
  }

  const customFields = await slackAdapter.getMemberCustomFields(
    workspace.id,
    member.id,
    workspace.policy
  );

  return Object.fromEntries<{ value: string }>(
    formattedPolicy
      .filter((pf) => {
        if (pf.slackFieldId == null) {
          return false;
        }

        const slackCustomField = workspace.customFields.find(
          (cf) => cf.id === pf.slackFieldId
        );

        const isEditable =
          slackCustomField != null && isFieldEditable(slackCustomField);

        const fieldValue = parseCustomFieldFromSlackField(
          pf,
          customFields[pf.slackFieldId]
        );

        if (
          !isEditable ||
          fieldValue === memberData[pf.id] ||
          (!fieldValue && !memberData[pf.id])
        ) {
          return false;
        }

        return true;
      })
      .map((pf) => [pf.slackFieldId!, { value: convertToSlackValue(pf) }])
  );
}
